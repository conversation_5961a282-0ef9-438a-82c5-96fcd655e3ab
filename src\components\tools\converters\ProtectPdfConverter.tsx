"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { FileUpload } from "@/components/ui/file-upload";
import { Progress } from "@/components/ui/progress";

export default function ProtectPdfConverter() {
  const [file, setFile] = useState<File | null>(null);
  const [userPassword, setUserPassword] = useState("");
  const [ownerPassword, setOwnerPassword] = useState("");
  const [allowPrinting, setAllowPrinting] = useState(false);
  const [allowCopying, setAllowCopying] = useState(false);
  const [allowModifying, setAllowModifying] = useState(false);
  const [allowAnnotations, setAllowAnnotations] = useState(false);

  const handleAllowPrintingChange = (checked: boolean | "indeterminate") => {
    setAllowPrinting(checked === true);
  };

  const handleAllowCopyingChange = (checked: boolean | "indeterminate") => {
    setAllowCopying(checked === true);
  };

  const handleAllowModifyingChange = (checked: boolean | "indeterminate") => {
    setAllowModifying(checked === true);
  };

  const handleAllowAnnotationsChange = (checked: boolean | "indeterminate") => {
    setAllowAnnotations(checked === true);
  };
  const [isConverting, setIsConverting] = useState(false);
  const [conversionProgress, setConversionProgress] = useState(0);
  const [convertedFileUrl, setConvertedFileUrl] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  const handleFileSelect = (files: File[]) => {
    if (files.length > 0) {
      setFile(files[0]);
      setError(null);
      setConvertedFileUrl(null);
    }
  };

  const handleProtectPdf = async () => {
    if (!file) {
      setError("Please select a PDF file first.");
      return;
    }

    if (!userPassword && !ownerPassword) {
      setError("Please enter at least one password (user or owner).");
      return;
    }

    setIsConverting(true);
    setError(null);
    setConversionProgress(0);

    try {
      // Create FormData for file upload
      const formData = new FormData();
      formData.append('file', file);
      formData.append('userPassword', userPassword);
      formData.append('ownerPassword', ownerPassword);
      formData.append('allowPrinting', allowPrinting.toString());
      formData.append('allowCopying', allowCopying.toString());
      formData.append('allowModifying', allowModifying.toString());
      formData.append('allowAnnotations', allowAnnotations.toString());

      // Start progress simulation
      const progressInterval = setInterval(() => {
        setConversionProgress(prev => {
          if (prev >= 90) return prev;
          return prev + Math.random() * 10;
        });
      }, 300);

      // Send file to protection API
      const response = await fetch('/api/tools/protect-pdf', {
        method: 'POST',
        body: formData,
      });

      clearInterval(progressInterval);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Protection failed');
      }

      // Get the protected PDF blob
      const protectedBlob = await response.blob();
      setConvertedFileUrl(URL.createObjectURL(protectedBlob));
      setConversionProgress(100);

    } catch (err) {
      setError(err instanceof Error ? err.message : "An error occurred during protection. Please try again.");
      console.error(err);
    } finally {
      setIsConverting(false);
    }
  };

  const handleDownload = () => {
    if (convertedFileUrl) {
      const link = document.createElement("a");
      link.href = convertedFileUrl;
      link.download = file ? `${file.name.replace(".pdf", "")}_protected.pdf` : "protected.pdf";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  return (
    <div className="space-y-6">
      <div className="bg-blue-50 p-4 rounded-lg">
        <h2 className="text-lg font-semibold mb-2">How to Protect PDF</h2>
        <ol className="list-decimal list-inside space-y-2 text-gray-700">
          <li>Upload your PDF file using the uploader below.</li>
          <li>Set passwords and permissions for the document.</li>
          <li>Click "Protect PDF" to process your file.</li>
          <li>Download your protected PDF when ready.</li>
        </ol>
      </div>

      <div className="space-y-4">
        <div>
          <Label htmlFor="file-upload">Select PDF File</Label>
          <FileUpload
            accept=".pdf"
            onFileSelect={handleFileSelect}
            maxSize={10 * 1024 * 1024} // 10MB
          />
          {file && (
            <p className="text-sm text-gray-600 mt-2">
              Selected: {file.name} ({(file.size / 1024 / 1024).toFixed(2)} MB)
            </p>
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="user-password">User Password</Label>
            <Input
              id="user-password"
              type="password"
              value={userPassword}
              onChange={(e) => setUserPassword(e.target.value)}
              placeholder="Password to open the document"
            />
            <p className="text-xs text-gray-500 mt-1">Required to open the PDF</p>
          </div>

          <div>
            <Label htmlFor="owner-password">Owner Password</Label>
            <Input
              id="owner-password"
              type="password"
              value={ownerPassword}
              onChange={(e) => setOwnerPassword(e.target.value)}
              placeholder="Password to modify permissions"
            />
            <p className="text-xs text-gray-500 mt-1">Required to change permissions</p>
          </div>
        </div>

        <div>
          <Label className="text-base font-semibold">Permissions</Label>
          <div className="grid grid-cols-2 gap-4 mt-2">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="allow-printing"
                checked={allowPrinting}
                onCheckedChange={handleAllowPrintingChange}
              />
              <Label htmlFor="allow-printing">Allow Printing</Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="allow-copying"
                checked={allowCopying}
                onCheckedChange={handleAllowCopyingChange}
              />
              <Label htmlFor="allow-copying">Allow Copying Text</Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="allow-modifying"
                checked={allowModifying}
                onCheckedChange={handleAllowModifyingChange}
              />
              <Label htmlFor="allow-modifying">Allow Modifying</Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="allow-annotations"
                checked={allowAnnotations}
                onCheckedChange={handleAllowAnnotationsChange}
              />
              <Label htmlFor="allow-annotations">Allow Annotations</Label>
            </div>
          </div>
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
            {error}
          </div>
        )}

        <Button
          onClick={handleProtectPdf}
          disabled={!file || isConverting || (!userPassword && !ownerPassword)}
          className="w-full"
        >
          {isConverting ? "Protecting PDF..." : "Protect PDF"}
        </Button>

        {isConverting && (
          <div className="space-y-2">
            <Progress value={conversionProgress} className="w-full" />
            <p className="text-sm text-gray-600 text-center">
              Processing... {conversionProgress}%
            </p>
          </div>
        )}

        {convertedFileUrl && (
          <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded">
            <p className="font-semibold">PDF protected successfully!</p>
            <p className="text-sm mt-1">
              Note: This is a demonstration version. Production implementation would include actual encryption.
            </p>
            <Button onClick={handleDownload} className="mt-2">
              Download Protected PDF
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}
