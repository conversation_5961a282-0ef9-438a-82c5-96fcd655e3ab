// Enterprise-grade caching system for ToolRapter
// Implements in-memory caching with TTL, LRU eviction, and performance monitoring

export interface CacheEntry<T> {
  value: T;
  timestamp: number;
  ttl: number;
  accessCount: number;
  lastAccessed: number;
}

export interface CacheStats {
  hits: number;
  misses: number;
  sets: number;
  deletes: number;
  evictions: number;
  size: number;
  maxSize: number;
  hitRate: number;
}

export interface CacheConfig {
  maxSize: number;
  defaultTTL: number;
  cleanupInterval: number;
  enableStats: boolean;
}

// Default cache configuration
const DEFAULT_CONFIG: CacheConfig = {
  maxSize: 1000,
  defaultTTL: 5 * 60 * 1000, // 5 minutes
  cleanupInterval: 60 * 1000, // 1 minute
  enableStats: true,
};

export class EnterpriseCache<T = any> {
  private cache = new Map<string, CacheEntry<T>>();
  private config: CacheConfig;
  private stats: CacheStats;
  private cleanupTimer?: NodeJS.Timeout;

  constructor(config: Partial<CacheConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
    this.stats = {
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0,
      evictions: 0,
      size: 0,
      maxSize: this.config.maxSize,
      hitRate: 0,
    };

    // Start cleanup timer
    if (this.config.cleanupInterval > 0) {
      this.startCleanupTimer();
    }
  }

  // Get value from cache
  get(key: string): T | undefined {
    const entry = this.cache.get(key);
    
    if (!entry) {
      this.incrementStat('misses');
      return undefined;
    }

    // Check if expired
    if (this.isExpired(entry)) {
      this.cache.delete(key);
      this.incrementStat('misses');
      this.updateSize();
      return undefined;
    }

    // Update access statistics
    entry.accessCount++;
    entry.lastAccessed = Date.now();
    
    this.incrementStat('hits');
    this.updateHitRate();
    
    return entry.value;
  }

  // Set value in cache
  set(key: string, value: T, ttl?: number): void {
    const now = Date.now();
    const entryTTL = ttl ?? this.config.defaultTTL;

    // Check if we need to evict entries
    if (this.cache.size >= this.config.maxSize && !this.cache.has(key)) {
      this.evictLRU();
    }

    const entry: CacheEntry<T> = {
      value,
      timestamp: now,
      ttl: entryTTL,
      accessCount: 1,
      lastAccessed: now,
    };

    this.cache.set(key, entry);
    this.incrementStat('sets');
    this.updateSize();
  }

  // Delete value from cache
  delete(key: string): boolean {
    const deleted = this.cache.delete(key);
    if (deleted) {
      this.incrementStat('deletes');
      this.updateSize();
    }
    return deleted;
  }

  // Check if key exists and is not expired
  has(key: string): boolean {
    const entry = this.cache.get(key);
    if (!entry) return false;
    
    if (this.isExpired(entry)) {
      this.cache.delete(key);
      this.updateSize();
      return false;
    }
    
    return true;
  }

  // Clear all cache entries
  clear(): void {
    this.cache.clear();
    this.resetStats();
  }

  // Get cache statistics
  getStats(): CacheStats {
    return { ...this.stats };
  }

  // Get all cache keys
  keys(): string[] {
    return Array.from(this.cache.keys());
  }

  // Get cache size
  size(): number {
    return this.cache.size;
  }

  // Cleanup expired entries
  cleanup(): number {
    const now = Date.now();
    let cleaned = 0;

    this.cache.forEach((entry, key) => {
      if (this.isExpired(entry)) {
        this.cache.delete(key);
        cleaned++;
      }
    });

    if (cleaned > 0) {
      this.updateSize();
    }

    return cleaned;
  }

  // Get or set pattern (cache-aside)
  async getOrSet<R = T>(
    key: string,
    factory: () => Promise<R> | R,
    ttl?: number
  ): Promise<R> {
    const cached = this.get(key);
    if (cached !== undefined) {
      return cached as R;
    }

    const value = await factory();
    this.set(key, value as T, ttl);
    return value;
  }

  // Memoize function with caching
  memoize<Args extends any[], Return>(
    fn: (...args: Args) => Promise<Return> | Return,
    keyGenerator?: (...args: Args) => string,
    ttl?: number
  ) {
    return async (...args: Args): Promise<Return> => {
      const key = keyGenerator ? keyGenerator(...args) : JSON.stringify(args);
      return this.getOrSet(key, () => fn(...args), ttl);
    };
  }

  // Batch operations
  mget(keys: string[]): Map<string, T> {
    const result = new Map<string, T>();
    
    for (const key of keys) {
      const value = this.get(key);
      if (value !== undefined) {
        result.set(key, value);
      }
    }
    
    return result;
  }

  mset(entries: Map<string, T>, ttl?: number): void {
    entries.forEach((value, key) => {
      this.set(key, value, ttl);
    });
  }

  // Private helper methods
  private isExpired(entry: CacheEntry<T>): boolean {
    return Date.now() - entry.timestamp > entry.ttl;
  }

  private evictLRU(): void {
    let oldestKey: string | undefined;
    let oldestTime = Infinity;

    this.cache.forEach((entry, key) => {
      if (entry.lastAccessed < oldestTime) {
        oldestTime = entry.lastAccessed;
        oldestKey = key;
      }
    });

    if (oldestKey) {
      this.cache.delete(oldestKey);
      this.incrementStat('evictions');
    }
  }

  private incrementStat(stat: keyof Omit<CacheStats, 'size' | 'maxSize' | 'hitRate'>): void {
    if (this.config.enableStats) {
      this.stats[stat]++;
    }
  }

  private updateSize(): void {
    this.stats.size = this.cache.size;
  }

  private updateHitRate(): void {
    const total = this.stats.hits + this.stats.misses;
    this.stats.hitRate = total > 0 ? (this.stats.hits / total) * 100 : 0;
  }

  private resetStats(): void {
    this.stats = {
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0,
      evictions: 0,
      size: 0,
      maxSize: this.config.maxSize,
      hitRate: 0,
    };
  }

  private startCleanupTimer(): void {
    this.cleanupTimer = setInterval(() => {
      this.cleanup();
    }, this.config.cleanupInterval);
  }

  // Cleanup resources
  destroy(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
    }
    this.clear();
  }
}

// Global cache instances for different use cases
export const blogCache = new EnterpriseCache({
  maxSize: 500,
  defaultTTL: 10 * 60 * 1000, // 10 minutes
});

export const apiCache = new EnterpriseCache({
  maxSize: 1000,
  defaultTTL: 5 * 60 * 1000, // 5 minutes
});

export const userCache = new EnterpriseCache({
  maxSize: 200,
  defaultTTL: 15 * 60 * 1000, // 15 minutes
});

export const staticCache = new EnterpriseCache({
  maxSize: 100,
  defaultTTL: 60 * 60 * 1000, // 1 hour
});

// Cache key generators
export const cacheKeys = {
  blogPost: (id: string) => `blog:post:${id}`,
  blogList: (page: number, limit: number) => `blog:list:${page}:${limit}`,
  user: (id: string) => `user:${id}`,
  tool: (slug: string) => `tool:${slug}`,
  calculator: (slug: string) => `calculator:${slug}`,
  api: (endpoint: string, params?: Record<string, any>) => 
    `api:${endpoint}${params ? ':' + JSON.stringify(params) : ''}`,
};

// Cache decorators for API routes
export function withCache<T>(
  cache: EnterpriseCache<T>,
  keyGenerator: (...args: any[]) => string,
  ttl?: number
) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      const key = keyGenerator(...args);
      const cached = cache.get(key);
      
      if (cached !== undefined) {
        return cached;
      }

      const result = await method.apply(this, args);
      cache.set(key, result, ttl);
      return result;
    };

    return descriptor;
  };
}

// Performance monitoring for cache
export function getCachePerformanceReport() {
  return {
    blog: blogCache.getStats(),
    api: apiCache.getStats(),
    user: userCache.getStats(),
    static: staticCache.getStats(),
    timestamp: Date.now(),
  };
}

// Cleanup all caches
export function cleanupAllCaches(): number {
  return (
    blogCache.cleanup() +
    apiCache.cleanup() +
    userCache.cleanup() +
    staticCache.cleanup()
  );
}

// Clear all caches
export function clearAllCaches(): void {
  blogCache.clear();
  apiCache.clear();
  userCache.clear();
  staticCache.clear();
}
