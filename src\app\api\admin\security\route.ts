import { NextRequest, NextResponse } from 'next/server';
import { getToken } from 'next-auth/jwt';
import { 
  getSecurityMetrics, 
  getRecentSecurityEvents, 
  getSecurityEventsByType,
  generateSecurityReport 
} from '@/lib/security-monitor';
import { applyAPISecurityHeaders } from '@/lib/security-headers';

const secret = process.env.NEXTAUTH_SECRET;

// GET /api/admin/security - Get security dashboard data
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const token = await getToken({ req: request, secret });
    if (!token || token.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized access' },
        { status: 401 }
      );
    }

    const url = new URL(request.url);
    const action = url.searchParams.get('action');
    const limit = parseInt(url.searchParams.get('limit') || '100');
    const type = url.searchParams.get('type');

    let data;

    switch (action) {
      case 'metrics':
        data = getSecurityMetrics();
        break;
        
      case 'events':
        if (type) {
          data = getSecurityEventsByType(type as any, limit);
        } else {
          data = getRecentSecurityEvents(limit);
        }
        break;
        
      case 'report':
        data = generateSecurityReport();
        break;
        
      default:
        // Return comprehensive dashboard data
        data = {
          metrics: getSecurityMetrics(),
          recentEvents: getRecentSecurityEvents(50),
          report: generateSecurityReport(),
        };
    }

    const response = NextResponse.json({
      success: true,
      data,
      timestamp: Date.now(),
    });

    return applyAPISecurityHeaders(response);
  } catch (error) {
    console.error('Security dashboard error:', error);
    
    const response = NextResponse.json(
      { 
        error: 'Internal server error',
        success: false 
      },
      { status: 500 }
    );

    return applyAPISecurityHeaders(response);
  }
}

// POST /api/admin/security - Security actions (clear events, reset metrics, etc.)
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const token = await getToken({ req: request, secret });
    if (!token || token.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized access' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { action } = body;

    let result;

    switch (action) {
      case 'test_security':
        // Test security monitoring system
        result = {
          message: 'Security monitoring system is operational',
          metrics: getSecurityMetrics(),
          timestamp: Date.now(),
        };
        break;
        
      case 'export_events':
        // Export security events for analysis
        const events = getRecentSecurityEvents(1000);
        result = {
          message: 'Security events exported',
          events,
          count: events.length,
          timestamp: Date.now(),
        };
        break;
        
      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        );
    }

    const response = NextResponse.json({
      success: true,
      data: result,
    });

    return applyAPISecurityHeaders(response);
  } catch (error) {
    console.error('Security action error:', error);
    
    const response = NextResponse.json(
      { 
        error: 'Internal server error',
        success: false 
      },
      { status: 500 }
    );

    return applyAPISecurityHeaders(response);
  }
}
