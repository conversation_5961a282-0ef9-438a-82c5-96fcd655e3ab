{"timestamp": "2025-07-17T20:50:59.884Z", "targets": {"BUILD_TIME_MAX": 20, "PAGE_LOAD_MAX": 5, "BUNDLE_SIZE_MAX": 5, "FIRST_CONTENTFUL_PAINT_MAX": 2, "LARGEST_CONTENTFUL_PAINT_MAX": 4}, "checks": {"packageJson": true, "nextConfig": true, "typeScript": true}, "recommendations": ["Use dynamic imports for large components", "Implement proper image optimization", "Enable compression and caching", "Minimize bundle size with tree shaking", "Use React.memo for expensive components", "Implement proper error boundaries", "Use service workers for caching"]}