import { NextRequest, NextResponse } from 'next/server';
import { PDFDocument } from 'pdf-lib';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const files = formData.getAll('files') as File[];

    if (!files || files.length === 0) {
      return NextResponse.json(
        { error: 'No files provided' },
        { status: 400 }
      );
    }

    // Validate all files are PNG images
    const supportedTypes = ['image/png'];
    for (const file of files) {
      if (!supportedTypes.includes(file.type)) {
        return NextResponse.json(
          { error: `File "${file.name}" is not a PNG image. Only PNG files are supported.` },
          { status: 400 }
        );
      }
    }

    // Create a new PDF document
    const pdfDoc = await PDFDocument.create();
    
    let totalImages = 0;
    const imageInfo = [];

    // Process each PNG file
    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      const arrayBuffer = await file.arrayBuffer();
      
      try {
        // Embed PNG image
        const image = await pdfDoc.embedPng(arrayBuffer);

        // Get image dimensions
        const { width, height } = image.scale(1);
        
        // Calculate page size to fit image (max A4 size)
        const maxWidth = 595.28; // A4 width in points
        const maxHeight = 841.89; // A4 height in points
        
        let pageWidth = width;
        let pageHeight = height;
        
        // Scale down if image is larger than A4
        if (width > maxWidth || height > maxHeight) {
          const widthRatio = maxWidth / width;
          const heightRatio = maxHeight / height;
          const scale = Math.min(widthRatio, heightRatio);
          
          pageWidth = width * scale;
          pageHeight = height * scale;
        }

        // Add a new page for this image
        const page = pdfDoc.addPage([pageWidth, pageHeight]);
        
        // Draw the image on the page
        page.drawImage(image, {
          x: 0,
          y: 0,
          width: pageWidth,
          height: pageHeight,
        });

        totalImages++;
        imageInfo.push({
          name: file.name,
          originalWidth: width,
          originalHeight: height,
          pageWidth: pageWidth,
          pageHeight: pageHeight,
          size: arrayBuffer.byteLength
        });

      } catch (error) {
        console.error(`Error processing PNG ${file.name}:`, error);
        return NextResponse.json(
          { error: `Failed to process PNG "${file.name}". Please ensure it's a valid PNG file.` },
          { status: 400 }
        );
      }
    }

    // Save the PDF
    const pdfBytes = await pdfDoc.save({
      useObjectStreams: true,
      addDefaultPage: false,
    });

    // Generate filename for PDF
    const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
    const pdfFilename = files.length === 1 
      ? `${files[0].name.replace(/\.png$/i, '')}_converted.pdf`
      : `png_to_pdf_${files.length}_images_${timestamp}.pdf`;

    // Create response with PDF
    const response = new NextResponse(pdfBytes, {
      status: 200,
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `attachment; filename="${pdfFilename}"`,
        'X-Total-Images': totalImages.toString(),
        'X-Total-Pages': totalImages.toString(),
        'X-Image-Info': JSON.stringify(imageInfo),
      },
    });

    return response;

  } catch (error) {
    console.error('PNG to PDF conversion error:', error);
    return NextResponse.json(
      { error: 'Failed to convert PNG to PDF. Please ensure all files are valid PNG images.' },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json(
    { 
      message: 'PNG to PDF Conversion API',
      supportedFormats: ['PNG'],
      maxFiles: 20,
      maxFileSize: '10MB per file',
      note: 'Each PNG image will be placed on a separate page in the PDF'
    },
    { status: 200 }
  );
}
