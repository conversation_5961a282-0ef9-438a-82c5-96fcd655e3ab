import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { TouchableCard } from '../TouchableCard';

// Mock framer-motion
jest.mock('framer-motion', () => {
  const MockMotionDiv = React.forwardRef<HTMLDivElement, any>(({ children, ...props }, ref) => (
    <div ref={ref} {...props}>
      {children}
    </div>
  ));
  MockMotionDiv.displayName = 'MockMotionDiv';

  return {
    motion: {
      div: MockMotionDiv,
    },
  };
});

// Mock useTouch hook
jest.mock('@/hooks/useTouch', () => ({
  __esModule: true,
  default: jest.fn(() => ({
    touchState: {
      isPressed: false,
      isLongPressed: false,
      swipeDirection: null,
    },
    touchHandlers: {
      onTouchStart: jest.fn(),
      onTouchEnd: jest.fn(),
      onTouchMove: jest.fn(),
    },
    isTouchDevice: false,
  })),
}));

// Mock haptic feedback
jest.mock('@/utils/hapticFeedback', () => ({
  success: jest.fn(),
  warning: jest.fn(),
  error: jest.fn(),
  impact: jest.fn(),
}));

describe('TouchableCard', () => {
  const defaultProps = {
    children: <div>Test Content</div>,
    onTap: jest.fn(),
    onLongPress: jest.fn(),
    onSwipeLeft: jest.fn(),
    onSwipeRight: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock window.matchMedia
    Object.defineProperty(window, 'matchMedia', {
      writable: true,
      value: jest.fn().mockImplementation(query => ({
        matches: query === '(hover: hover) and (pointer: fine)',
        media: query,
        onchange: null,
        addListener: jest.fn(),
        removeListener: jest.fn(),
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
        dispatchEvent: jest.fn(),
      })),
    });
  });

  it('should render children correctly', () => {
    render(<TouchableCard {...defaultProps} />);
    expect(screen.getByText('Test Content')).toBeInTheDocument();
  });

  it('should apply correct CSS classes', () => {
    render(
      <TouchableCard 
        {...defaultProps} 
        variant="elevated" 
        size="lg" 
        cardType="blog"
        className="custom-class"
      />
    );
    
    const card = screen.getByText('Test Content').parentElement;
    expect(card).toHaveClass('touch-card');
    expect(card).toHaveClass('custom-class');
  });

  it('should handle tap events', () => {
    const onTap = jest.fn();
    render(<TouchableCard {...defaultProps} onTap={onTap} />);
    
    const card = screen.getByText('Test Content').parentElement;
    fireEvent.click(card!);
    
    expect(onTap).toHaveBeenCalledTimes(1);
  });

  it('should handle disabled state', () => {
    const onTap = jest.fn();
    render(<TouchableCard {...defaultProps} onTap={onTap} disabled />);
    
    const card = screen.getByText('Test Content').parentElement;
    expect(card).toHaveClass('opacity-50');
    expect(card).toHaveClass('cursor-not-allowed');
    
    fireEvent.click(card!);
    expect(onTap).not.toHaveBeenCalled();
  });

  it('should apply card type specific classes', () => {
    render(<TouchableCard {...defaultProps} cardType="blog" enableHoverAnimations />);
    
    const card = screen.getByText('Test Content').parentElement;
    expect(card).toHaveClass('blog-card');
  });

  it('should handle different variants', () => {
    const { rerender } = render(<TouchableCard {...defaultProps} variant="default" />);
    let card = screen.getByText('Test Content').parentElement;
    expect(card).toHaveClass('bg-background');

    rerender(<TouchableCard {...defaultProps} variant="elevated" />);
    card = screen.getByText('Test Content').parentElement;
    expect(card).toHaveClass('bg-card');

    rerender(<TouchableCard {...defaultProps} variant="outlined" />);
    card = screen.getByText('Test Content').parentElement;
    expect(card).toHaveClass('border-2');
  });

  it('should handle different sizes', () => {
    const { rerender } = render(<TouchableCard {...defaultProps} size="sm" />);
    let card = screen.getByText('Test Content').parentElement;
    expect(card).toHaveClass('p-3');

    rerender(<TouchableCard {...defaultProps} size="md" />);
    card = screen.getByText('Test Content').parentElement;
    expect(card).toHaveClass('p-4');

    rerender(<TouchableCard {...defaultProps} size="lg" />);
    card = screen.getByText('Test Content').parentElement;
    expect(card).toHaveClass('p-6');
  });

  it('should handle mouse events for hover animations', () => {
    render(<TouchableCard {...defaultProps} enableHoverAnimations />);
    
    const card = screen.getByText('Test Content').parentElement;
    
    // Test mouse move
    fireEvent.mouseMove(card!, {
      clientX: 100,
      clientY: 100,
    });
    
    // Test mouse leave
    fireEvent.mouseLeave(card!);
  });

  it('should handle touch feedback options', () => {
    const { rerender } = render(
      <TouchableCard {...defaultProps} touchFeedback="scale" />
    );
    
    rerender(<TouchableCard {...defaultProps} touchFeedback="opacity" />);
    rerender(<TouchableCard {...defaultProps} touchFeedback="both" />);
    rerender(<TouchableCard {...defaultProps} touchFeedback="none" />);
    
    // Should render without errors
    expect(screen.getByText('Test Content')).toBeInTheDocument();
  });

  it('should forward ref correctly', () => {
    const ref = React.createRef<HTMLDivElement>();
    render(<TouchableCard {...defaultProps} ref={ref} />);
    
    expect(ref.current).toBeInstanceOf(HTMLDivElement);
  });

  it('should handle swipe gestures', () => {
    const useTouch = require('@/hooks/useTouch').default;
    const mockTouchHandlers = {
      onTouchStart: jest.fn(),
      onTouchEnd: jest.fn(),
      onTouchMove: jest.fn(),
    };
    
    useTouch.mockReturnValue({
      touchState: {
        isPressed: false,
        isLongPressed: false,
        swipeDirection: 'left',
      },
      touchHandlers: mockTouchHandlers,
      isTouchDevice: true,
    });

    render(<TouchableCard {...defaultProps} />);
    
    const card = screen.getByText('Test Content').parentElement;
    
    fireEvent.touchStart(card!);
    expect(mockTouchHandlers.onTouchStart).toHaveBeenCalled();
  });

  it('should handle long press state', () => {
    const useTouch = require('@/hooks/useTouch').default;
    
    useTouch.mockReturnValue({
      touchState: {
        isPressed: false,
        isLongPressed: true,
        swipeDirection: null,
      },
      touchHandlers: {
        onTouchStart: jest.fn(),
        onTouchEnd: jest.fn(),
        onTouchMove: jest.fn(),
      },
      isTouchDevice: true,
    });

    render(<TouchableCard {...defaultProps} />);
    
    const card = screen.getByText('Test Content').parentElement;
    expect(card).toHaveClass('touch-long-press');
  });

  it('should handle pressed state', () => {
    const useTouch = require('@/hooks/useTouch').default;
    
    useTouch.mockReturnValue({
      touchState: {
        isPressed: true,
        isLongPressed: false,
        swipeDirection: null,
      },
      touchHandlers: {
        onTouchStart: jest.fn(),
        onTouchEnd: jest.fn(),
        onTouchMove: jest.fn(),
      },
      isTouchDevice: true,
    });

    render(<TouchableCard {...defaultProps} />);
    
    const card = screen.getByText('Test Content').parentElement;
    expect(card).toHaveClass('touch-active');
  });
});
