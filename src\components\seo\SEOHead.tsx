import Head from 'next/head';

interface SEOHeadProps {
  title: string;
  description: string;
  keywords?: string[];
  canonicalUrl?: string;
  ogImage?: string;
  ogType?: 'website' | 'article' | 'product';
  twitterCard?: 'summary' | 'summary_large_image' | 'app' | 'player';
  structuredData?: object;
  noIndex?: boolean;
  alternateUrls?: { hreflang: string; href: string }[];
}

export default function SEOHead({
  title,
  description,
  keywords = [],
  canonicalUrl,
  ogImage = '/images/og-default.png',
  ogType = 'website',
  twitterCard = 'summary_large_image',
  structuredData,
  noIndex = false,
  alternateUrls = []
}: SEOHeadProps) {
  const siteName = 'ToolRapter';
  const siteUrl = 'https://toolrapter.com';
  const fullTitle = title.includes(siteName) ? title : `${title} | ${siteName}`;
  const fullCanonicalUrl = canonicalUrl || siteUrl;
  const fullOgImage = ogImage.startsWith('http') ? ogImage : `${siteUrl}${ogImage}`;

  return (
    <Head>
      {/* Basic Meta Tags */}
      <title>{fullTitle}</title>
      <meta name="description" content={description} />
      {keywords.length > 0 && (
        <meta name="keywords" content={keywords.join(', ')} />
      )}
      <meta name="author" content="ToolRapter" />
      <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      <meta httpEquiv="Content-Type" content="text/html; charset=utf-8" />
      <meta name="language" content="English" />
      
      {/* Robots Meta */}
      <meta 
        name="robots" 
        content={noIndex ? 'noindex, nofollow' : 'index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1'} 
      />
      <meta name="googlebot" content={noIndex ? 'noindex, nofollow' : 'index, follow'} />
      
      {/* Canonical URL */}
      <link rel="canonical" href={fullCanonicalUrl} />
      
      {/* Alternate URLs for internationalization */}
      {alternateUrls.map((alt, index) => (
        <link
          key={index}
          rel="alternate"
          hrefLang={alt.hreflang}
          href={alt.href}
        />
      ))}
      
      {/* Open Graph Meta Tags */}
      <meta property="og:title" content={fullTitle} />
      <meta property="og:description" content={description} />
      <meta property="og:type" content={ogType} />
      <meta property="og:url" content={fullCanonicalUrl} />
      <meta property="og:image" content={fullOgImage} />
      <meta property="og:image:alt" content={title} />
      <meta property="og:site_name" content={siteName} />
      <meta property="og:locale" content="en_US" />
      
      {/* Twitter Card Meta Tags */}
      <meta name="twitter:card" content={twitterCard} />
      <meta name="twitter:title" content={fullTitle} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:image" content={fullOgImage} />
      <meta name="twitter:image:alt" content={title} />
      <meta name="twitter:site" content="@toolrapter" />
      <meta name="twitter:creator" content="@toolrapter" />
      
      {/* Additional Meta Tags for Tools */}
      <meta name="application-name" content={siteName} />
      <meta name="apple-mobile-web-app-title" content={siteName} />
      <meta name="apple-mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-status-bar-style" content="default" />
      <meta name="mobile-web-app-capable" content="yes" />
      <meta name="theme-color" content="#2563eb" />
      <meta name="msapplication-TileColor" content="#2563eb" />
      
      {/* Favicon and Icons */}
      <link rel="icon" type="image/x-icon" href="/favicon.ico" />
      <link rel="icon" type="image/png" sizes="16x16" href="/icons/icon-16x16.png" />
      <link rel="icon" type="image/png" sizes="32x32" href="/icons/icon-32x32.png" />
      <link rel="apple-touch-icon" sizes="180x180" href="/icons/apple-touch-icon.png" />
      <link rel="mask-icon" href="/icons/safari-pinned-tab.svg" color="#2563eb" />
      
      {/* PWA Manifest */}
      <link rel="manifest" href="/manifest.json" />
      
      {/* Preconnect to External Domains */}
      <link rel="preconnect" href="https://fonts.googleapis.com" />
      <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      <link rel="preconnect" href="https://www.google-analytics.com" />
      
      {/* DNS Prefetch */}
      <link rel="dns-prefetch" href="//fonts.googleapis.com" />
      <link rel="dns-prefetch" href="//fonts.gstatic.com" />
      <link rel="dns-prefetch" href="//www.google-analytics.com" />
      
      {/* Structured Data */}
      {structuredData && (
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(structuredData)
          }}
        />
      )}
    </Head>
  );
}

// Utility function to generate tool-specific SEO data
export function generateToolSEO(toolConfig: {
  name: string;
  description: string;
  slug: string;
  keywords?: string[];
  category?: string;
}) {
  const baseKeywords = [
    'PDF converter',
    'online PDF tools',
    'document conversion',
    'file converter',
    'free PDF tools',
    'ToolRapter'
  ];

  const toolKeywords = toolConfig.keywords || [];
  const allKeywords = [...baseKeywords, ...toolKeywords];

  const structuredData = {
    '@context': 'https://schema.org',
    '@type': 'WebApplication',
    name: `${toolConfig.name} - ToolRapter`,
    description: toolConfig.description,
    url: `https://toolrapter.com/tools/${toolConfig.slug}`,
    applicationCategory: 'ProductivityApplication',
    operatingSystem: 'Web Browser',
    offers: {
      '@type': 'Offer',
      price: '0',
      priceCurrency: 'USD'
    },
    provider: {
      '@type': 'Organization',
      name: 'ToolRapter',
      url: 'https://toolrapter.com'
    },
    featureList: [
      'Free online conversion',
      'No registration required',
      'Secure file processing',
      'High-quality output',
      'Fast conversion speed'
    ]
  };

  return {
    title: `${toolConfig.name} - Free Online PDF Converter`,
    description: toolConfig.description,
    keywords: allKeywords,
    canonicalUrl: `https://toolrapter.com/tools/${toolConfig.slug}`,
    structuredData
  };
}

// Utility function to generate homepage SEO data
export function generateHomepageSEO() {
  const structuredData = {
    '@context': 'https://schema.org',
    '@type': 'WebSite',
    name: 'ToolRapter',
    description: 'Enterprise-grade PDF conversion tools for all your document needs',
    url: 'https://toolrapter.com',
    potentialAction: {
      '@type': 'SearchAction',
      target: 'https://toolrapter.com/search?q={search_term_string}',
      'query-input': 'required name=search_term_string'
    },
    publisher: {
      '@type': 'Organization',
      name: 'ToolRapter',
      url: 'https://toolrapter.com',
      logo: {
        '@type': 'ImageObject',
        url: 'https://toolrapter.com/icons/icon-512x512.png'
      }
    }
  };

  return {
    title: 'ToolRapter - Free Online PDF Conversion Tools',
    description: 'Convert, merge, split, and optimize PDF files with our free online tools. Excel to PDF, Word to PDF, and 15+ more converters. No registration required.',
    keywords: [
      'PDF converter',
      'online PDF tools',
      'Excel to PDF',
      'Word to PDF',
      'merge PDF',
      'split PDF',
      'compress PDF',
      'free PDF converter',
      'document conversion',
      'ToolRapter'
    ],
    canonicalUrl: 'https://toolrapter.com',
    structuredData
  };
}

// Utility function to generate blog post SEO data
export function generateBlogSEO(post: {
  title: string;
  description: string;
  slug: string;
  author?: string;
  publishDate?: string;
  modifiedDate?: string;
  tags?: string[];
  category?: string;
}) {
  const structuredData = {
    '@context': 'https://schema.org',
    '@type': 'Article',
    headline: post.title,
    description: post.description,
    url: `https://toolrapter.com/blog/${post.slug}`,
    datePublished: post.publishDate,
    dateModified: post.modifiedDate || post.publishDate,
    author: {
      '@type': 'Person',
      name: post.author || 'ToolRapter Team'
    },
    publisher: {
      '@type': 'Organization',
      name: 'ToolRapter',
      logo: {
        '@type': 'ImageObject',
        url: 'https://toolrapter.com/icons/icon-512x512.png'
      }
    },
    mainEntityOfPage: {
      '@type': 'WebPage',
      '@id': `https://toolrapter.com/blog/${post.slug}`
    }
  };

  return {
    title: `${post.title} | ToolRapter Blog`,
    description: post.description,
    keywords: post.tags || [],
    canonicalUrl: `https://toolrapter.com/blog/${post.slug}`,
    ogType: 'article' as const,
    structuredData
  };
}

// Utility function to generate FAQ structured data
export function generateFAQStructuredData(faqs: Array<{ question: string; answer: string }>) {
  return {
    '@context': 'https://schema.org',
    '@type': 'FAQPage',
    mainEntity: faqs.map(faq => ({
      '@type': 'Question',
      name: faq.question,
      acceptedAnswer: {
        '@type': 'Answer',
        text: faq.answer
      }
    }))
  };
}

// Utility function to generate breadcrumb structured data
export function generateBreadcrumbStructuredData(breadcrumbs: Array<{ name: string; url: string }>) {
  return {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: breadcrumbs.map((crumb, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      name: crumb.name,
      item: crumb.url
    }))
  };
}
