'use client';

import Image from 'next/image';
import { useTheme } from '@/hooks/useTheme';
import { PageData } from '@/lib/routing';

interface BlogContentProps {
  data: PageData;
}

export default function BlogContent({ data }: BlogContentProps) {
  const { theme } = useTheme();
  
  return (
    <div className={`max-w-4xl mx-auto ${
      theme === 'dark' ? 'text-gray-100' : 'text-gray-900'
    }`}>
      {/* Blog image */}
      {data.image && (
        <div className="mb-8 relative h-64 sm:h-80">
          <Image
            src={data.image}
            alt={data.title}
            fill
            className="object-cover rounded-lg shadow-lg"
          />
        </div>
      )}

      {/* Blog metadata */}
      <div className={`flex flex-wrap items-center gap-4 mb-8 text-sm ${
        theme === 'dark' ? 'text-gray-400' : 'text-gray-600'
      }`}>
        {data.author && (
          <span className="flex items-center gap-2">
            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
            </svg>
            {data.author}
          </span>
        )}
        {data.date && (
          <span className="flex items-center gap-2">
            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
            </svg>
            {data.date}
          </span>
        )}
        {data.category && (
          <span className={`px-2 py-1 rounded text-xs font-medium ${
            theme === 'dark' 
              ? 'bg-gray-800 text-gray-300' 
              : 'bg-gray-100 text-gray-700'
          }`}>
            {data.category}
          </span>
        )}
      </div>

      {/* Blog content */}
      <div className={`prose prose-lg max-w-none ${
        theme === 'dark' 
          ? 'prose-invert prose-headings:text-gray-100 prose-p:text-gray-300 prose-a:text-blue-400' 
          : 'prose-headings:text-gray-900 prose-p:text-gray-700 prose-a:text-blue-600'
      }`}>
        {data.content ? (
          <div dangerouslySetInnerHTML={{ __html: data.content }} />
        ) : (
          <p>Content coming soon...</p>
        )}
      </div>

      {/* Tags */}
      {data.tags && data.tags.length > 0 && (
        <div className="mt-8 pt-8 border-t border-gray-200 dark:border-gray-700">
          <h3 className={`text-lg font-semibold mb-4 ${
            theme === 'dark' ? 'text-gray-100' : 'text-gray-900'
          }`}>
            Tags
          </h3>
          <div className="flex flex-wrap gap-2">
            {data.tags.map((tag, index) => (
              <span
                key={index}
                className={`px-3 py-1 rounded-full text-sm ${
                  theme === 'dark'
                    ? 'bg-gray-800 text-gray-300 hover:bg-gray-700'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                } transition-colors cursor-pointer`}
              >
                #{tag}
              </span>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
