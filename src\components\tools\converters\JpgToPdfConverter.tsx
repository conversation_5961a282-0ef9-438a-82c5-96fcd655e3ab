"use client";

import { useState } from "react";
import FileUploader from "../FileUploader";

export default function JpgToPdfConverter() {
  const [files, setFiles] = useState<File[]>([]);
  const [isConverting, setIsConverting] = useState(false);
  const [convertedFileUrl, setConvertedFileUrl] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [conversionProgress, setConversionProgress] = useState(0);

  const handleFileSelect = (selectedFile: File) => {
    setFiles((prevFiles) => [...prevFiles, selectedFile]);
    setConvertedFileUrl(null);
    setError(null);
    setConversionProgress(0);
  };

  const removeFile = (index: number) => {
    setFiles((prevFiles) => prevFiles.filter((_, i) => i !== index));
    if (files.length === 1) {
      setConvertedFileUrl(null);
    }
  };

  const handleConvert = async () => {
    if (files.length === 0) return;

    setIsConverting(true);
    setError(null);
    setConversionProgress(0);

    try {
      // Create FormData for file upload
      const formData = new FormData();
      files.forEach(file => {
        formData.append('files', file);
      });

      // Start progress simulation
      const progressInterval = setInterval(() => {
        setConversionProgress(prev => {
          if (prev >= 90) return prev;
          return prev + Math.random() * 10;
        });
      }, 300);

      // Send files to conversion API
      const response = await fetch('/api/tools/jpg-to-pdf', {
        method: 'POST',
        body: formData,
      });

      clearInterval(progressInterval);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Conversion failed');
      }

      // Get the converted PDF blob
      const pdfBlob = await response.blob();
      setConvertedFileUrl(URL.createObjectURL(pdfBlob));
      setConversionProgress(100);

    } catch (err) {
      setError(err instanceof Error ? err.message : "An error occurred during conversion. Please try again.");
      console.error(err);
    } finally {
      setIsConverting(false);
    }
  };

  const handleDownload = () => {
    if (convertedFileUrl) {
      const link = document.createElement("a");
      link.href = convertedFileUrl;
      link.download = "images_to_pdf.pdf";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  return (
    <div className="space-y-6">
      <div className="bg-blue-50 p-4 rounded-lg">
        <h2 className="text-lg font-semibold mb-2">
          How to Convert JPG to PDF
        </h2>
        <ol className="list-decimal list-inside space-y-2 text-gray-700">
          <li>Upload one or more JPG images using the uploader below.</li>
          <li>Click the "Convert to PDF" button to start the conversion.</li>
          <li>Download your converted PDF document when ready.</li>
        </ol>
      </div>

      <div className="space-y-4">
        <FileUploader
          acceptedFileTypes=".jpg,.jpeg,.png,image/jpeg,image/png"
          maxFileSizeMB={10}
          onFileSelect={handleFileSelect}
          multiple={true}
        />

        {files.length > 0 && (
          <div className="space-y-2">
            <h3 className="text-sm font-medium text-gray-700">
              Selected Images ({files.length})
            </h3>
            <div className="max-h-60 overflow-y-auto space-y-2 border rounded-md p-2">
              {files.map((file, index) => (
                <div
                  key={index}
                  className="flex items-center space-x-2 p-2 bg-gray-50 rounded-lg"
                >
                  <svg
                    className="w-6 h-6 text-gray-500"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                    ></path>
                  </svg>
                  <span className="flex-1 truncate">{file.name}</span>
                  <span className="text-sm text-gray-500">
                    {(file.size / (1024 * 1024)).toFixed(2)} MB
                  </span>
                  <button
                    onClick={() => removeFile(index)}
                    className="text-red-500 hover:text-red-700"
                    aria-label="Remove file"
                  >
                    <svg
                      className="w-5 h-5"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M6 18L18 6M6 6l12 12"
                      ></path>
                    </svg>
                  </button>
                </div>
              ))}
            </div>
          </div>
        )}

        {files.length > 0 && (
          <button
            onClick={handleConvert}
            disabled={isConverting}
            className={`w-full py-2 px-4 rounded-md font-medium ${isConverting ? "bg-gray-400 cursor-not-allowed" : "bg-blue-600 hover:bg-blue-700 text-white"}`}
          >
            {isConverting ? "Converting..." : "Convert to PDF"}
          </button>
        )}

        {isConverting && (
          <div className="space-y-2">
            <div className="w-full bg-gray-200 rounded-full h-2.5">
              <div
                className="bg-blue-600 h-2.5 rounded-full"
                style={{ width: `${conversionProgress}%` }}
              ></div>
            </div>
            <p className="text-sm text-gray-600 text-center">
              {conversionProgress}% complete
            </p>
          </div>
        )}

        {error && (
          <div className="p-4 bg-red-50 text-red-700 rounded-lg">{error}</div>
        )}

        {convertedFileUrl && (
          <div className="p-4 bg-green-50 rounded-lg space-y-4">
            <div className="flex items-center text-green-700">
              <svg
                className="w-5 h-5 mr-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M5 13l4 4L19 7"
                ></path>
              </svg>
              <span>Conversion completed successfully!</span>
            </div>
            <button
              onClick={handleDownload}
              className="w-full py-2 px-4 bg-green-600 hover:bg-green-700 text-white rounded-md font-medium flex items-center justify-center"
            >
              <svg
                className="w-5 h-5 mr-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"
                ></path>
              </svg>
              Download PDF Document
            </button>
          </div>
        )}
      </div>

      <div className="mt-8 border-t pt-6">
        <h3 className="text-lg font-semibold mb-4">
          About JPG to PDF Conversion
        </h3>
        <p className="text-gray-700 mb-4">
          Our JPG to PDF converter allows you to combine multiple JPG images
          into a single PDF document. Each image will be converted to a separate
          page in the PDF, maintaining the original quality and dimensions. This
          is useful for creating documentation, reports, or photo albums from
          your image files.
        </p>
        <div className="bg-yellow-50 p-4 rounded-lg">
          <h4 className="font-medium text-yellow-800 mb-2">Please Note:</h4>
          <p className="text-yellow-700 text-sm">
            The images will be arranged in the PDF in the order they were
            uploaded. You can upload JPG, JPEG, and PNG files. The conversion
            preserves the original image quality, but very large images may be
            slightly compressed to optimize the PDF file size.
          </p>
        </div>
      </div>
    </div>
  );
}
