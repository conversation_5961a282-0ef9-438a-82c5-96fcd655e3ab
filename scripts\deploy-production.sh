#!/bin/bash

# =============================================================================
# TOOLRAPTER - ENTERPRISE PRODUCTION DEPLOYMENT SCRIPT
# =============================================================================
# Zero-downtime deployment with performance monitoring and rollback capabilities
# Domain: toolrapter.com | VPS: ************
# Usage: ./scripts/deploy-production.sh

set -e  # Exit on any error

# =============================================================================
# CONFIGURATION
# =============================================================================
VPS_HOST="************"
VPS_USER="root"
APP_NAME="toolrapter"
DOMAIN="toolrapter.com"
REPO_URL="https://github.com/MuhammadShahbaz195/ToolCrush.git"

# Paths
APP_DIR="/var/www/$APP_NAME"
BACKUP_DIR="/var/backups/$APP_NAME"
LOG_DIR="/var/log/pm2"
TEMP_DIR="/tmp/toolrapter-deploy"

# Performance targets
MAX_BUILD_TIME=20  # seconds
MAX_PAGE_LOAD_TIME=5  # seconds

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# =============================================================================
# HELPER FUNCTIONS
# =============================================================================
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_performance() {
    echo -e "${PURPLE}[PERFORMANCE]${NC} $1"
}

# Timer functions
start_timer() {
    TIMER_START=$(date +%s)
}

end_timer() {
    TIMER_END=$(date +%s)
    ELAPSED=$((TIMER_END - TIMER_START))
    echo $ELAPSED
}

# Pre-deployment checks
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check required tools
    for tool in ssh scp curl npm tar; do
        if ! command -v $tool &> /dev/null; then
            log_error "$tool is required but not installed"
            exit 1
        fi
    done
    
    # Check SSH connection
    if ! ssh -o ConnectTimeout=10 -o StrictHostKeyChecking=no $VPS_USER@$VPS_HOST "echo 'SSH OK'" > /dev/null 2>&1; then
        log_error "Failed to connect to VPS. Check SSH configuration."
        exit 1
    fi
    
    # Check TypeScript compilation
    log_info "Running TypeScript type check..."
    if ! npm run type-check; then
        log_error "TypeScript compilation failed. Fix errors before deployment."
        exit 1
    fi
    
    log_success "Prerequisites check passed"
}

# Build application with performance monitoring
build_application() {
    log_info "Building application with performance monitoring..."
    
    start_timer
    
    # Clean previous builds
    rm -rf .next
    rm -rf out
    
    # Install dependencies
    log_info "Installing dependencies..."
    npm ci --production=false --prefer-offline
    
    # Download fonts
    log_info "Downloading fonts..."
    npm run download-fonts
    
    # Build application
    log_info "Building Next.js application..."
    npm run build
    
    BUILD_TIME=$(end_timer)
    log_performance "Build completed in ${BUILD_TIME} seconds"
    
    # Check build time performance target
    if [ $BUILD_TIME -gt $MAX_BUILD_TIME ]; then
        log_warning "Build time (${BUILD_TIME}s) exceeds target (${MAX_BUILD_TIME}s)"
    else
        log_success "Build time target met: ${BUILD_TIME}s <= ${MAX_BUILD_TIME}s"
    fi
}

# Create optimized deployment package
create_deployment_package() {
    log_info "Creating optimized deployment package..."
    
    # Create temporary directory
    mkdir -p $TEMP_DIR
    
    # Copy essential files only
    tar -czf $TEMP_DIR/deployment.tar.gz \
        .next/ \
        public/ \
        package.json \
        package-lock.json \
        next.config.js \
        ecosystem.config.js \
        scripts/download-fonts.js \
        --exclude=node_modules \
        --exclude=.git \
        --exclude=*.log \
        --exclude=coverage \
        --exclude=.nyc_output
    
    PACKAGE_SIZE=$(du -h $TEMP_DIR/deployment.tar.gz | cut -f1)
    log_success "Deployment package created: $PACKAGE_SIZE"
}

# Deploy to VPS with zero downtime
deploy_to_vps() {
    log_info "Deploying to VPS with zero-downtime strategy..."
    
    # Upload deployment package
    log_info "Uploading deployment package..."
    scp -o StrictHostKeyChecking=no $TEMP_DIR/deployment.tar.gz $VPS_USER@$VPS_HOST:/tmp/
    
    # Deploy on VPS
    ssh $VPS_USER@$VPS_HOST << 'EOF'
        set -e
        
        APP_DIR="/var/www/toolrapter"
        BACKUP_DIR="/var/backups/toolrapter"
        
        # Create backup of current deployment
        if [ -d "$APP_DIR" ]; then
            TIMESTAMP=$(date +%Y%m%d_%H%M%S)
            mkdir -p "$BACKUP_DIR"
            tar -czf "$BACKUP_DIR/backup_$TIMESTAMP.tar.gz" -C "$APP_DIR" . 2>/dev/null || true
            
            # Keep only last 5 backups
            find "$BACKUP_DIR" -name "backup_*.tar.gz" -type f | sort -r | tail -n +6 | xargs rm -f
            
            echo "Backup created: backup_$TIMESTAMP.tar.gz"
        fi
        
        # Extract new deployment
        cd "$APP_DIR"
        tar -xzf /tmp/deployment.tar.gz
        
        # Install production dependencies
        npm ci --production --prefer-offline --silent
        
        # Set proper permissions
        chown -R www-data:www-data "$APP_DIR"
        chmod -R 755 "$APP_DIR"
        
        # Clean up
        rm -f /tmp/deployment.tar.gz
        
        echo "Deployment extracted and configured"
EOF
    
    log_success "Application deployed to VPS"
}

# Restart application with zero downtime
restart_application() {
    log_info "Restarting application with zero downtime..."
    
    ssh $VPS_USER@$VPS_HOST << 'EOF'
        # Graceful reload with PM2
        if pm2 list | grep -q "toolrapter"; then
            pm2 reload toolrapter --update-env
        else
            pm2 start ecosystem.config.js --env production
        fi
        
        # Save PM2 configuration
        pm2 save
        
        # Test and reload Nginx
        nginx -t && systemctl reload nginx
        
        echo "Application restarted successfully"
EOF
    
    log_success "Application restart completed"
}

# Performance validation
validate_performance() {
    log_info "Validating performance targets..."
    
    # Wait for application to fully start
    sleep 15
    
    # Test internal health endpoint
    start_timer
    if ssh $VPS_USER@$VPS_HOST "curl -f --max-time 30 http://localhost:3000/api/health" > /dev/null 2>&1; then
        INTERNAL_RESPONSE_TIME=$(end_timer)
        log_success "Internal health check passed (${INTERNAL_RESPONSE_TIME}s)"
    else
        log_error "Internal health check failed"
        return 1
    fi
    
    # Test external page load time
    start_timer
    if curl -f --max-time 30 "https://$DOMAIN" > /dev/null 2>&1; then
        EXTERNAL_RESPONSE_TIME=$(end_timer)
        log_performance "External page load time: ${EXTERNAL_RESPONSE_TIME}s"
        
        if [ $EXTERNAL_RESPONSE_TIME -gt $MAX_PAGE_LOAD_TIME ]; then
            log_warning "Page load time (${EXTERNAL_RESPONSE_TIME}s) exceeds target (${MAX_PAGE_LOAD_TIME}s)"
        else
            log_success "Page load time target met: ${EXTERNAL_RESPONSE_TIME}s <= ${MAX_PAGE_LOAD_TIME}s"
        fi
    else
        log_error "External health check failed"
        return 1
    fi
    
    # Test API performance
    start_timer
    if curl -f --max-time 10 "https://$DOMAIN/api/health" > /dev/null 2>&1; then
        API_RESPONSE_TIME=$(end_timer)
        log_performance "API response time: ${API_RESPONSE_TIME}s"
    else
        log_warning "API health check failed"
    fi
}

# Cleanup function
cleanup() {
    log_info "Cleaning up temporary files..."
    rm -rf $TEMP_DIR
}

# Rollback function
rollback_deployment() {
    log_warning "Rolling back to previous deployment..."
    
    ssh $VPS_USER@$VPS_HOST << 'EOF'
        BACKUP_DIR="/var/backups/toolrapter"
        APP_DIR="/var/www/toolrapter"
        
        LATEST_BACKUP=$(find "$BACKUP_DIR" -name "backup_*.tar.gz" -type f | sort -r | head -n 1)
        
        if [ -n "$LATEST_BACKUP" ]; then
            echo "Rolling back to: $(basename $LATEST_BACKUP)"
            
            cd "$APP_DIR"
            tar -xzf "$LATEST_BACKUP"
            
            pm2 reload toolrapter --update-env
            
            echo "Rollback completed successfully"
        else
            echo "No backup found for rollback"
            exit 1
        fi
EOF
}

# =============================================================================
# MAIN DEPLOYMENT PROCESS
# =============================================================================
main() {
    echo "🚀 ToolRapter Enterprise Production Deployment"
    echo "=============================================="
    echo "Target: $VPS_USER@$VPS_HOST"
    echo "Domain: $DOMAIN"
    echo "Performance Targets: Build <${MAX_BUILD_TIME}s, Load <${MAX_PAGE_LOAD_TIME}s"
    echo ""
    
    # Set trap for cleanup
    trap cleanup EXIT
    
    # Pre-deployment checks
    check_prerequisites
    
    # Build application
    build_application
    
    # Create deployment package
    create_deployment_package
    
    # Deploy to VPS
    deploy_to_vps
    
    # Restart application
    restart_application
    
    # Validate performance
    if validate_performance; then
        log_success "🎉 Deployment completed successfully!"
        log_info "Application is live at: https://$DOMAIN"
        echo ""
        echo "📊 Performance Summary:"
        echo "- Build Time: ${BUILD_TIME}s (target: <${MAX_BUILD_TIME}s)"
        echo "- Page Load: ${EXTERNAL_RESPONSE_TIME}s (target: <${MAX_PAGE_LOAD_TIME}s)"
        echo "- API Response: ${API_RESPONSE_TIME}s"
    else
        log_error "Performance validation failed. Rolling back..."
        rollback_deployment
        exit 1
    fi
}

# =============================================================================
# SCRIPT EXECUTION
# =============================================================================
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
