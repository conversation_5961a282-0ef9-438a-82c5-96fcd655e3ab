// Comprehensive testing script for all 17 PDF tools
const https = require('https');
const http = require('http');

const BASE_URL = 'http://localhost:3002';

// Tool categories for systematic testing
const TOOL_CATEGORIES = {
  HIGH_PRIORITY: [
    'excel-to-pdf',
    'word-to-pdf', 
    'pdf-to-word',
    'pdf-to-excel',
    'merge-pdf',
    'split-pdf',
    'compress-pdf',
    'rotate-pdf'
  ],
  MEDIUM_PRIORITY: [
    'jpg-to-pdf',
    'pdf-to-jpg',
    'html-to-pdf',
    'powerpoint-to-pdf',
    'pdf-to-powerpoint'
  ],
  LOWER_PRIORITY: [
    'add-watermark',
    'protect-pdf',
    'pdf-to-pdfa'
  ],
  GENERIC: [
    'png-to-pdf'
  ]
};

// Test results storage
const testResults = {
  passed: [],
  failed: [],
  errors: []
};

/**
 * Test if a tool page loads without errors
 */
async function testToolPage(toolId) {
  return new Promise((resolve) => {
    const url = `${BASE_URL}/tools/${toolId}`;
    
    console.log(`🧪 Testing: ${toolId}`);
    
    const req = http.get(url, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        const result = {
          toolId,
          status: res.statusCode,
          success: res.statusCode === 200,
          hasError: data.includes('Error:') || data.includes('error'),
          hasSpecificComponent: data.includes('ExcelToPdfConverter') || 
                               data.includes('WordToPdfConverter') ||
                               data.includes('PdfToWordConverter') ||
                               data.includes('MergePdfConverter') ||
                               !data.includes('GenericConverter'),
          responseSize: data.length
        };
        
        if (result.success && !result.hasError) {
          console.log(`✅ ${toolId}: Page loads successfully`);
          if (result.hasSpecificComponent) {
            console.log(`   📦 Uses specific converter component`);
          } else {
            console.log(`   🔄 Uses generic converter component`);
          }
          testResults.passed.push(result);
        } else {
          console.log(`❌ ${toolId}: Failed (Status: ${result.status})`);
          if (result.hasError) {
            console.log(`   🐛 Contains error messages`);
          }
          testResults.failed.push(result);
        }
        
        resolve(result);
      });
    });
    
    req.on('error', (err) => {
      console.log(`💥 ${toolId}: Request failed - ${err.message}`);
      testResults.errors.push({ toolId, error: err.message });
      resolve({ toolId, success: false, error: err.message });
    });
    
    req.setTimeout(10000, () => {
      console.log(`⏰ ${toolId}: Request timeout`);
      req.destroy();
      testResults.errors.push({ toolId, error: 'Timeout' });
      resolve({ toolId, success: false, error: 'Timeout' });
    });
  });
}

/**
 * Test all tools in a category
 */
async function testCategory(categoryName, tools) {
  console.log(`\n🎯 Testing ${categoryName} Tools (${tools.length} tools)`);
  console.log('='.repeat(50));
  
  for (const toolId of tools) {
    await testToolPage(toolId);
    // Small delay between requests
    await new Promise(resolve => setTimeout(resolve, 500));
  }
}

/**
 * Generate test report
 */
function generateReport() {
  console.log('\n📊 TEST RESULTS SUMMARY');
  console.log('='.repeat(50));
  
  const totalTools = testResults.passed.length + testResults.failed.length + testResults.errors.length;
  
  console.log(`✅ Passed: ${testResults.passed.length}/${totalTools}`);
  console.log(`❌ Failed: ${testResults.failed.length}/${totalTools}`);
  console.log(`💥 Errors: ${testResults.errors.length}/${totalTools}`);
  
  if (testResults.passed.length > 0) {
    console.log('\n✅ WORKING TOOLS:');
    testResults.passed.forEach(result => {
      console.log(`   - ${result.toolId} (${result.hasSpecificComponent ? 'Specific' : 'Generic'} component)`);
    });
  }
  
  if (testResults.failed.length > 0) {
    console.log('\n❌ FAILED TOOLS:');
    testResults.failed.forEach(result => {
      console.log(`   - ${result.toolId} (Status: ${result.status})`);
    });
  }
  
  if (testResults.errors.length > 0) {
    console.log('\n💥 ERROR TOOLS:');
    testResults.errors.forEach(result => {
      console.log(`   - ${result.toolId}: ${result.error}`);
    });
  }
  
  console.log('\n🎯 NEXT STEPS:');
  if (testResults.failed.length > 0 || testResults.errors.length > 0) {
    console.log('1. Fix failing tools before proceeding');
    console.log('2. Check server logs for detailed error messages');
    console.log('3. Verify API endpoints are working');
  } else {
    console.log('1. All tools loading successfully!');
    console.log('2. Proceed with authentication testing');
    console.log('3. Test actual file conversion functionality');
  }
}

/**
 * Main testing function
 */
async function runAllTests() {
  console.log('🚀 Starting Comprehensive Tool Testing');
  console.log(`📍 Base URL: ${BASE_URL}`);
  console.log(`📅 Started at: ${new Date().toISOString()}`);
  
  try {
    // Test each category
    await testCategory('HIGH PRIORITY', TOOL_CATEGORIES.HIGH_PRIORITY);
    await testCategory('MEDIUM PRIORITY', TOOL_CATEGORIES.MEDIUM_PRIORITY);
    await testCategory('LOWER PRIORITY', TOOL_CATEGORIES.LOWER_PRIORITY);
    await testCategory('GENERIC', TOOL_CATEGORIES.GENERIC);
    
    // Generate final report
    generateReport();
    
  } catch (error) {
    console.error('💥 Testing failed:', error);
  }
}

// Run the tests
runAllTests();
