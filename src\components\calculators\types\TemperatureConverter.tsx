"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

interface TemperatureResult {
  celsius: number;
  fahrenheit: number;
  kelvin: number;
  rankine: number;
  reaumur: number;
}

export default function TemperatureConverter() {
  const [inputValue, setInputValue] = useState<number>(20);
  const [inputUnit, setInputUnit] = useState<string>("celsius");
  const [result, setResult] = useState<TemperatureResult | null>(null);

  const convertTemperature = () => {
    let celsius: number;

    // Convert input to Celsius first
    switch (inputUnit) {
      case "celsius":
        celsius = inputValue;
        break;
      case "fahrenheit":
        celsius = (inputValue - 32) * 5 / 9;
        break;
      case "kelvin":
        celsius = inputValue - 273.15;
        break;
      case "rankine":
        celsius = (inputValue - 491.67) * 5 / 9;
        break;
      case "reaumur":
        celsius = inputValue * 5 / 4;
        break;
      default:
        celsius = inputValue;
    }

    // Convert from Celsius to all other units
    const fahrenheit = (celsius * 9 / 5) + 32;
    const kelvin = celsius + 273.15;
    const rankine = (celsius + 273.15) * 9 / 5;
    const reaumur = celsius * 4 / 5;

    setResult({
      celsius,
      fahrenheit,
      kelvin,
      rankine,
      reaumur
    });
  };

  // Auto-convert when input changes
  useEffect(() => {
    if (inputValue !== null && !isNaN(inputValue)) {
      convertTemperature();
    }
  }, [inputValue, inputUnit]);

  const reset = () => {
    setInputValue(20);
    setInputUnit("celsius");
    setResult(null);
  };

  const formatTemperature = (temp: number, decimals: number = 2): string => {
    return temp.toFixed(decimals);
  };

  const getTemperatureDescription = (celsius: number): string => {
    if (celsius < -40) return "Extremely cold";
    if (celsius < -20) return "Very cold";
    if (celsius < 0) return "Freezing";
    if (celsius < 10) return "Cold";
    if (celsius < 20) return "Cool";
    if (celsius < 25) return "Comfortable";
    if (celsius < 30) return "Warm";
    if (celsius < 35) return "Hot";
    if (celsius < 40) return "Very hot";
    return "Extremely hot";
  };

  const getTemperatureColor = (celsius: number): string => {
    if (celsius < 0) return "blue";
    if (celsius < 15) return "cyan";
    if (celsius < 25) return "green";
    if (celsius < 35) return "yellow";
    return "red";
  };

  const getCommonTemperatures = () => [
    { name: "Absolute Zero", celsius: -273.15, description: "Theoretical lowest temperature" },
    { name: "Liquid Nitrogen", celsius: -196, description: "Boiling point" },
    { name: "Dry Ice", celsius: -78.5, description: "Sublimation point" },
    { name: "Water Freezes", celsius: 0, description: "At sea level" },
    { name: "Room Temperature", celsius: 20, description: "Typical indoor temperature" },
    { name: "Human Body", celsius: 37, description: "Normal body temperature" },
    { name: "Water Boils", celsius: 100, description: "At sea level" },
    { name: "Oven Temperature", celsius: 180, description: "Typical baking temperature" },
    { name: "Paper Burns", celsius: 233, description: "Fahrenheit 451" },
    { name: "Lead Melts", celsius: 327, description: "Melting point" }
  ];

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Temperature Converter</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Input Section */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div>
                <Label htmlFor="input-value">Temperature Value</Label>
                <Input
                  id="input-value"
                  type="number"
                  value={inputValue}
                  onChange={(e) => setInputValue(parseFloat(e.target.value) || 0)}
                  step="0.1"
                />
              </div>

              <div>
                <Label htmlFor="input-unit">From Unit</Label>
                <Select value={inputUnit} onValueChange={setInputUnit}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="celsius">Celsius (°C)</SelectItem>
                    <SelectItem value="fahrenheit">Fahrenheit (°F)</SelectItem>
                    <SelectItem value="kelvin">Kelvin (K)</SelectItem>
                    <SelectItem value="rankine">Rankine (°R)</SelectItem>
                    <SelectItem value="reaumur">Réaumur (°Ré)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
              <h3 className="font-semibold mb-2">Temperature Scales</h3>
              <div className="space-y-2 text-sm">
                <div><strong>Celsius:</strong> Water freezes at 0°, boils at 100°</div>
                <div><strong>Fahrenheit:</strong> Water freezes at 32°, boils at 212°</div>
                <div><strong>Kelvin:</strong> Absolute temperature scale, 0K = -273.15°C</div>
                <div><strong>Rankine:</strong> Absolute Fahrenheit scale</div>
                <div><strong>Réaumur:</strong> Water freezes at 0°, boils at 80°</div>
              </div>
            </div>
          </div>

          {/* Results */}
          {result && (
            <div className="space-y-6">
              {/* Status Card */}
              <Card className={`bg-${getTemperatureColor(result.celsius)}-50 dark:bg-${getTemperatureColor(result.celsius)}-900/20`}>
                <CardContent className="pt-6">
                  <div className="text-center space-y-2">
                    <div className="text-sm text-muted-foreground">Temperature Description</div>
                    <div className={`text-2xl font-bold text-${getTemperatureColor(result.celsius)}-600 dark:text-${getTemperatureColor(result.celsius)}-400`}>
                      {getTemperatureDescription(result.celsius)}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {formatTemperature(result.celsius)}°C
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Conversion Results */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <Card className="bg-green-50 dark:bg-green-900/20">
                  <CardContent className="pt-6">
                    <div className="text-center space-y-2">
                      <div className="text-sm text-muted-foreground">Celsius</div>
                      <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                        {formatTemperature(result.celsius)}°C
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-blue-50 dark:bg-blue-900/20">
                  <CardContent className="pt-6">
                    <div className="text-center space-y-2">
                      <div className="text-sm text-muted-foreground">Fahrenheit</div>
                      <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                        {formatTemperature(result.fahrenheit)}°F
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-purple-50 dark:bg-purple-900/20">
                  <CardContent className="pt-6">
                    <div className="text-center space-y-2">
                      <div className="text-sm text-muted-foreground">Kelvin</div>
                      <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                        {formatTemperature(result.kelvin)} K
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-orange-50 dark:bg-orange-900/20">
                  <CardContent className="pt-6">
                    <div className="text-center space-y-2">
                      <div className="text-sm text-muted-foreground">Rankine</div>
                      <div className="text-2xl font-bold text-orange-600 dark:text-orange-400">
                        {formatTemperature(result.rankine)}°R
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-pink-50 dark:bg-pink-900/20">
                  <CardContent className="pt-6">
                    <div className="text-center space-y-2">
                      <div className="text-sm text-muted-foreground">Réaumur</div>
                      <div className="text-2xl font-bold text-pink-600 dark:text-pink-400">
                        {formatTemperature(result.reaumur)}°Ré
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-gray-50 dark:bg-gray-900/20">
                  <CardContent className="pt-6">
                    <div className="text-center space-y-2">
                      <div className="text-sm text-muted-foreground">Quick Copy</div>
                      <div className="text-lg font-bold text-gray-600 dark:text-gray-400">
                        {formatTemperature(result.celsius, 1)}°C<br/>
                        {formatTemperature(result.fahrenheit, 1)}°F
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Conversion Formulas */}
              <Card>
                <CardHeader>
                  <CardTitle>Conversion Formulas</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div className="space-y-2">
                      <div><strong>Celsius to Fahrenheit:</strong> °F = (°C × 9/5) + 32</div>
                      <div><strong>Fahrenheit to Celsius:</strong> °C = (°F - 32) × 5/9</div>
                      <div><strong>Celsius to Kelvin:</strong> K = °C + 273.15</div>
                    </div>
                    <div className="space-y-2">
                      <div><strong>Kelvin to Celsius:</strong> °C = K - 273.15</div>
                      <div><strong>Celsius to Rankine:</strong> °R = (°C + 273.15) × 9/5</div>
                      <div><strong>Celsius to Réaumur:</strong> °Ré = °C × 4/5</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* Common Temperatures Reference */}
          <Card>
            <CardHeader>
              <CardTitle>Common Temperature References</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {getCommonTemperatures().map((temp, index) => (
                  <div key={index} className="flex justify-between items-center p-2 bg-gray-50 dark:bg-gray-800 rounded">
                    <div>
                      <div className="font-semibold">{temp.name}</div>
                      <div className="text-sm text-muted-foreground">{temp.description}</div>
                    </div>
                    <div className="text-right">
                      <div className="font-semibold">{formatTemperature(temp.celsius, 1)}°C</div>
                      <div className="text-sm text-muted-foreground">
                        {formatTemperature((temp.celsius * 9/5) + 32, 1)}°F
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <div className="flex gap-4">
            <Button onClick={reset} variant="outline" className="flex-1">
              Reset
            </Button>
          </div>

          {/* Tips */}
          <div className="p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
            <h3 className="font-semibold mb-2">Temperature Conversion Tips</h3>
            <ul className="text-sm space-y-1">
              <li>• Celsius and Fahrenheit intersect at -40° (both scales show the same value)</li>
              <li>• Kelvin is the SI base unit for temperature (no degree symbol)</li>
              <li>• Room temperature is typically 20-22°C (68-72°F)</li>
              <li>• Human body temperature is 37°C (98.6°F)</li>
              <li>• Water boils at 100°C (212°F) at sea level</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
