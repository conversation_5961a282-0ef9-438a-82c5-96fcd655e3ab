"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { <PERSON>, CardContent, CardH<PERSON>er, CardTitle } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";

interface StatisticsResult {
  count: number;
  sum: number;
  mean: number;
  median: number;
  mode: number[];
  range: number;
  variance: number;
  standardDeviation: number;
  min: number;
  max: number;
  q1: number;
  q3: number;
  iqr: number;
}

export default function StatisticsCalculator() {
  const [inputData, setInputData] = useState<string>("");
  const [numbers, setNumbers] = useState<number[]>([]);
  const [results, setResults] = useState<StatisticsResult | null>(null);
  const [error, setError] = useState<string>("");

  const parseInput = (input: string): number[] => {
    const cleanInput = input.replace(/[^\d\s,.-]/g, "");
    const parts = cleanInput.split(/[\s,]+/).filter(part => part.trim() !== "");
    
    const numbers: number[] = [];
    for (const part of parts) {
      const num = parseFloat(part);
      if (!isNaN(num)) {
        numbers.push(num);
      }
    }
    
    return numbers;
  };

  const calculateStatistics = (data: number[]): StatisticsResult => {
    if (data.length === 0) {
      throw new Error("No valid numbers found");
    }

    const sortedData = [...data].sort((a, b) => a - b);
    const count = data.length;
    const sum = data.reduce((acc, val) => acc + val, 0);
    const mean = sum / count;

    // Median
    let median: number;
    if (count % 2 === 0) {
      median = (sortedData[count / 2 - 1] + sortedData[count / 2]) / 2;
    } else {
      median = sortedData[Math.floor(count / 2)];
    }

    // Mode
    const frequency: { [key: number]: number } = {};
    data.forEach(num => {
      frequency[num] = (frequency[num] || 0) + 1;
    });
    
    const maxFreq = Math.max(...Object.values(frequency));
    const mode = Object.keys(frequency)
      .filter(key => frequency[parseFloat(key)] === maxFreq)
      .map(key => parseFloat(key));

    // Range
    const min = Math.min(...data);
    const max = Math.max(...data);
    const range = max - min;

    // Variance and Standard Deviation
    const variance = data.reduce((acc, val) => acc + Math.pow(val - mean, 2), 0) / count;
    const standardDeviation = Math.sqrt(variance);

    // Quartiles
    const q1Index = Math.floor(count * 0.25);
    const q3Index = Math.floor(count * 0.75);
    const q1 = sortedData[q1Index];
    const q3 = sortedData[q3Index];
    const iqr = q3 - q1;

    return {
      count,
      sum,
      mean,
      median,
      mode,
      range,
      variance,
      standardDeviation,
      min,
      max,
      q1,
      q3,
      iqr
    };
  };

  const handleCalculate = () => {
    try {
      setError("");
      const parsedNumbers = parseInput(inputData);
      
      if (parsedNumbers.length === 0) {
        throw new Error("Please enter valid numbers separated by spaces or commas");
      }

      setNumbers(parsedNumbers);
      const stats = calculateStatistics(parsedNumbers);
      setResults(stats);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Calculation error");
      setResults(null);
    }
  };

  const reset = () => {
    setInputData("");
    setNumbers([]);
    setResults(null);
    setError("");
  };

  const addSampleData = () => {
    setInputData("12, 15, 18, 20, 22, 25, 28, 30, 32, 35");
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Statistics Calculator</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Input Section */}
          <div className="space-y-4">
            <div>
              <Label htmlFor="data-input">Enter Numbers</Label>
              <Textarea
                id="data-input"
                value={inputData}
                onChange={(e) => setInputData(e.target.value)}
                placeholder="Enter numbers separated by spaces or commas (e.g., 1, 2, 3, 4, 5)"
                className="min-h-[100px]"
              />
              <p className="text-sm text-muted-foreground mt-1">
                Separate numbers with spaces, commas, or line breaks
              </p>
            </div>

            <div className="flex gap-4">
              <Button onClick={handleCalculate} className="flex-1">
                Calculate Statistics
              </Button>
              <Button onClick={addSampleData} variant="outline">
                Sample Data
              </Button>
              <Button onClick={reset} variant="outline">
                Reset
              </Button>
            </div>
          </div>

          {/* Error Display */}
          {error && (
            <div className="p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
              <p className="text-red-600 dark:text-red-400">{error}</p>
            </div>
          )}

          {/* Data Preview */}
          {numbers.length > 0 && (
            <Card className="bg-blue-50 dark:bg-blue-900/20">
              <CardContent className="pt-6">
                <h3 className="font-semibold mb-2">Data Set ({numbers.length} values)</h3>
                <p className="text-sm text-muted-foreground">
                  {numbers.slice(0, 20).join(", ")}
                  {numbers.length > 20 && "..."}
                </p>
              </CardContent>
            </Card>
          )}

          {/* Results */}
          {results && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {/* Basic Statistics */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Basic Statistics</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex justify-between">
                    <span>Count:</span>
                    <span className="font-mono">{results.count}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Sum:</span>
                    <span className="font-mono">{results.sum.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Mean:</span>
                    <span className="font-mono">{results.mean.toFixed(4)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Median:</span>
                    <span className="font-mono">{results.median.toFixed(4)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Mode:</span>
                    <span className="font-mono">
                      {results.mode.length === results.count ? "No mode" : results.mode.join(", ")}
                    </span>
                  </div>
                </CardContent>
              </Card>

              {/* Spread Statistics */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Spread & Variation</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex justify-between">
                    <span>Range:</span>
                    <span className="font-mono">{results.range.toFixed(4)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Variance:</span>
                    <span className="font-mono">{results.variance.toFixed(4)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Std Deviation:</span>
                    <span className="font-mono">{results.standardDeviation.toFixed(4)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Min:</span>
                    <span className="font-mono">{results.min.toFixed(4)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Max:</span>
                    <span className="font-mono">{results.max.toFixed(4)}</span>
                  </div>
                </CardContent>
              </Card>

              {/* Quartiles */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Quartiles</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex justify-between">
                    <span>Q1 (25th):</span>
                    <span className="font-mono">{results.q1.toFixed(4)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Q2 (Median):</span>
                    <span className="font-mono">{results.median.toFixed(4)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Q3 (75th):</span>
                    <span className="font-mono">{results.q3.toFixed(4)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>IQR:</span>
                    <span className="font-mono">{results.iqr.toFixed(4)}</span>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* Instructions */}
          <div className="p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
            <h3 className="font-semibold mb-2">Statistical Measures Explained</h3>
            <ul className="text-sm space-y-1">
              <li>• <strong>Mean:</strong> Average of all values</li>
              <li>• <strong>Median:</strong> Middle value when sorted</li>
              <li>• <strong>Mode:</strong> Most frequently occurring value(s)</li>
              <li>• <strong>Standard Deviation:</strong> Measure of data spread</li>
              <li>• <strong>IQR:</strong> Interquartile Range (Q3 - Q1)</li>
              <li>• <strong>Variance:</strong> Average of squared differences from mean</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
