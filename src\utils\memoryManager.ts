/**
 * Memory Management Utilities
 * Prevents memory leaks and optimizes resource usage
 */

// Global cleanup registry
const cleanupRegistry = new Set<() => void>();

// File URL registry for cleanup
const fileUrlRegistry = new Set<string>();

// Timer registry for cleanup
const timerRegistry = new Set<NodeJS.Timeout>();

// Event listener registry
const eventListenerRegistry = new Map<EventTarget, Array<{
  event: string;
  handler: EventListener;
  options?: boolean | AddEventListenerOptions;
}>>();

/**
 * Register a cleanup function to be called on component unmount
 */
export function registerCleanup(cleanupFn: () => void): () => void {
  cleanupRegistry.add(cleanupFn);
  
  // Return unregister function
  return () => {
    cleanupRegistry.delete(cleanupFn);
  };
}

/**
 * Execute all registered cleanup functions
 */
export function executeCleanup(): void {
  cleanupRegistry.forEach(cleanup => {
    try {
      cleanup();
    } catch (error) {
      console.error('Error during cleanup:', error);
    }
  });
  cleanupRegistry.clear();
}

/**
 * Create and register a file URL for cleanup
 */
export function createFileUrl(file: File | Blob): string {
  const url = URL.createObjectURL(file);
  fileUrlRegistry.add(url);
  
  // Auto-cleanup after 1 hour
  const timeoutId = setTimeout(() => {
    revokeFileUrl(url);
  }, 60 * 60 * 1000);
  
  timerRegistry.add(timeoutId);
  
  return url;
}

/**
 * Revoke a file URL and remove from registry
 */
export function revokeFileUrl(url: string): void {
  if (fileUrlRegistry.has(url)) {
    URL.revokeObjectURL(url);
    fileUrlRegistry.delete(url);
  }
}

/**
 * Clean up all file URLs
 */
export function cleanupFileUrls(): void {
  fileUrlRegistry.forEach(url => {
    URL.revokeObjectURL(url);
  });
  fileUrlRegistry.clear();
}

/**
 * Register a timer for cleanup
 */
export function registerTimer(timerId: NodeJS.Timeout): () => void {
  timerRegistry.add(timerId);
  
  return () => {
    clearTimeout(timerId);
    timerRegistry.delete(timerId);
  };
}

/**
 * Clear all registered timers
 */
export function clearAllTimers(): void {
  timerRegistry.forEach(timerId => {
    clearTimeout(timerId);
  });
  timerRegistry.clear();
}

/**
 * Add event listener with automatic cleanup registration
 */
export function addEventListenerWithCleanup(
  target: EventTarget,
  event: string,
  handler: EventListener,
  options?: boolean | AddEventListenerOptions
): () => void {
  target.addEventListener(event, handler, options);
  
  // Register for cleanup
  if (!eventListenerRegistry.has(target)) {
    eventListenerRegistry.set(target, []);
  }
  
  const listeners = eventListenerRegistry.get(target)!;
  listeners.push({ event, handler, options });
  
  // Return cleanup function
  return () => {
    target.removeEventListener(event, handler, options);
    const index = listeners.findIndex(l => l.event === event && l.handler === handler);
    if (index > -1) {
      listeners.splice(index, 1);
    }
  };
}

/**
 * Remove all event listeners for a target
 */
export function removeAllEventListeners(target: EventTarget): void {
  const listeners = eventListenerRegistry.get(target);
  if (listeners) {
    listeners.forEach(({ event, handler, options }) => {
      target.removeEventListener(event, handler, options);
    });
    eventListenerRegistry.delete(target);
  }
}

/**
 * Clean up all event listeners
 */
export function cleanupAllEventListeners(): void {
  eventListenerRegistry.forEach((listeners, target) => {
    listeners.forEach(({ event, handler, options }) => {
      target.removeEventListener(event, handler, options);
    });
  });
  eventListenerRegistry.clear();
}

/**
 * Memory-safe file processing with automatic cleanup
 */
export async function processFileWithCleanup<T>(
  file: File,
  processor: (file: File, url: string) => Promise<T>
): Promise<T> {
  const url = createFileUrl(file);
  
  try {
    const result = await processor(file, url);
    return result;
  } finally {
    // Always cleanup the URL
    revokeFileUrl(url);
  }
}

/**
 * React hook for automatic cleanup on unmount
 */
export function useMemoryCleanup() {
  const cleanup = () => {
    if (typeof window === 'undefined') return;
    executeCleanup();
    cleanupFileUrls();
    clearAllTimers();
    cleanupAllEventListeners();
  };

  // Register cleanup on page unload (only in browser)
  const unregisterPageCleanup = typeof window !== 'undefined'
    ? addEventListenerWithCleanup(window, 'beforeunload', cleanup)
    : () => {};

  return {
    cleanup,
    registerCleanup,
    createFileUrl,
    revokeFileUrl,
    registerTimer,
    addEventListenerWithCleanup,
    unregister: unregisterPageCleanup
  };
}

/**
 * Monitor memory usage (development only)
 */
export function monitorMemoryUsage(): void {
  if (process.env.NODE_ENV !== 'development' || typeof window === 'undefined') return;
  
  const logMemoryUsage = () => {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      console.log('Memory Usage:', {
        used: `${Math.round(memory.usedJSHeapSize / 1024 / 1024)} MB`,
        total: `${Math.round(memory.totalJSHeapSize / 1024 / 1024)} MB`,
        limit: `${Math.round(memory.jsHeapSizeLimit / 1024 / 1024)} MB`,
        fileUrls: fileUrlRegistry.size,
        timers: timerRegistry.size,
        eventListeners: eventListenerRegistry.size
      });
    }
  };
  
  // Log every 30 seconds in development
  const intervalId = setInterval(logMemoryUsage, 30000);
  registerTimer(intervalId);
  
  // Initial log
  logMemoryUsage();
}

/**
 * Force garbage collection (development only)
 */
export function forceGarbageCollection(): void {
  if (process.env.NODE_ENV !== 'development') return;
  
  // Clean up our registries first
  executeCleanup();
  cleanupFileUrls();
  clearAllTimers();
  cleanupAllEventListeners();
  
  // Force GC if available (Chrome DevTools)
  if (typeof window !== 'undefined' && 'gc' in window) {
    (window as any).gc();
  }
}
