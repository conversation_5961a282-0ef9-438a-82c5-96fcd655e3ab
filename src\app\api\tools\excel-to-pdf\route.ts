import { NextRequest, NextResponse } from 'next/server';
import { PDFDocument, rgb, StandardFonts } from 'pdf-lib';
import * as XLSX from 'xlsx';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }

    if (!file.name.toLowerCase().endsWith('.xlsx') && 
        file.type !== 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
      return NextResponse.json(
        { error: 'File must be an Excel (.xlsx) document' },
        { status: 400 }
      );
    }

    // Convert file to buffer
    const arrayBuffer = await file.arrayBuffer();
    
    // Read Excel file
    const workbook = XLSX.read(arrayBuffer, { type: 'array' });
    
    if (!workbook.SheetNames || workbook.SheetNames.length === 0) {
      return NextResponse.json(
        { error: 'No worksheets found in the Excel file.' },
        { status: 400 }
      );
    }

    // Create PDF document
    const pdfDoc = await PDFDocument.create();
    const font = await pdfDoc.embedFont(StandardFonts.Helvetica);
    const boldFont = await pdfDoc.embedFont(StandardFonts.HelveticaBold);
    const fontSize = 10;
    const lineHeight = fontSize * 1.2;
    const margin = 40;
    
    // Page dimensions
    const pageWidth = 841.89; // A4 landscape width in points
    const pageHeight = 595.28; // A4 landscape height in points
    const contentWidth = pageWidth - (margin * 2);
    const contentHeight = pageHeight - (margin * 2);

    let totalSheets = 0;

    // Process each worksheet
    for (const sheetName of workbook.SheetNames) {
      const worksheet = workbook.Sheets[sheetName];
      
      // Convert worksheet to array of arrays
      const data = XLSX.utils.sheet_to_json(worksheet, { header: 1, defval: '' }) as any[][];
      
      if (data.length === 0) continue;

      // Add new page for this sheet
      const page = pdfDoc.addPage([pageWidth, pageHeight]);
      let yPosition = pageHeight - margin;

      // Draw sheet title
      page.drawText(`Sheet: ${sheetName}`, {
        x: margin,
        y: yPosition,
        size: fontSize + 2,
        font: boldFont,
        color: rgb(0, 0, 0),
      });
      yPosition -= lineHeight * 2;

      // Calculate column widths
      const maxCols = Math.max(...data.map(row => row.length));
      const colWidth = Math.min(contentWidth / maxCols, 120); // Max 120 points per column

      // Draw table headers and data
      for (let rowIndex = 0; rowIndex < data.length; rowIndex++) {
        const row = data[rowIndex];
        
        // Check if we need a new page
        if (yPosition < margin + lineHeight) {
          const newPage = pdfDoc.addPage([pageWidth, pageHeight]);
          yPosition = pageHeight - margin;
          
          // Redraw sheet title on new page
          newPage.drawText(`Sheet: ${sheetName} (continued)`, {
            x: margin,
            y: yPosition,
            size: fontSize + 2,
            font: boldFont,
            color: rgb(0, 0, 0),
          });
          yPosition -= lineHeight * 2;
        }

        // Draw row data
        for (let colIndex = 0; colIndex < row.length && colIndex < 10; colIndex++) { // Limit to 10 columns
          const cellValue = String(row[colIndex] || '');
          const xPosition = margin + (colIndex * colWidth);
          
          // Truncate long text
          let displayText = cellValue;
          if (displayText.length > 15) {
            displayText = displayText.substring(0, 12) + '...';
          }

          const currentPage = pdfDoc.getPages()[pdfDoc.getPageCount() - 1];
          currentPage.drawText(displayText, {
            x: xPosition,
            y: yPosition,
            size: fontSize,
            font: rowIndex === 0 ? boldFont : font, // Bold for header row
            color: rgb(0, 0, 0),
          });
        }
        
        yPosition -= lineHeight;
      }

      totalSheets++;
    }

    if (totalSheets === 0) {
      return NextResponse.json(
        { error: 'No data found in any worksheet.' },
        { status: 400 }
      );
    }

    // Save the PDF
    const pdfBytes = await pdfDoc.save();

    // Generate filename
    const originalName = file.name.replace(/\.xlsx$/i, '');
    const pdfFilename = `${originalName}_converted.pdf`;

    // Create response with PDF
    const response = new NextResponse(pdfBytes, {
      status: 200,
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `attachment; filename="${pdfFilename}"`,
        'X-Original-Size': arrayBuffer.byteLength.toString(),
        'X-Sheets-Processed': totalSheets.toString(),
        'X-Pages-Created': pdfDoc.getPageCount().toString(),
      },
    });

    return response;

  } catch (error) {
    console.error('Excel to PDF conversion error:', error);
    
    // Provide more specific error messages
    if (error instanceof Error) {
      if (error.message.includes('Invalid Excel')) {
        return NextResponse.json(
          { error: 'Invalid Excel file. Please ensure the file is not corrupted.' },
          { status: 400 }
        );
      }
      if (error.message.includes('password')) {
        return NextResponse.json(
          { error: 'Password-protected Excel files are not supported.' },
          { status: 400 }
        );
      }
    }

    return NextResponse.json(
      { error: 'Failed to convert Excel to PDF. Please ensure the file is a valid Excel document.' },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json(
    { 
      message: 'Excel to PDF Conversion API',
      supportedInput: 'XLSX',
      outputFormat: 'PDF',
      maxFileSize: '10MB',
      note: 'Converts Excel worksheets to PDF pages. Large tables may be split across multiple pages.'
    },
    { status: 200 }
  );
}
