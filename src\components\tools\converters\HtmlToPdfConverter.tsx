"use client";

import { useState } from "react";

export default function HtmlToPdfConverter() {
  const [url, setUrl] = useState<string>("");
  const [htmlContent, setHtmlContent] = useState<string>("");
  const [activeTab, setActiveTab] = useState<"url" | "html">("url");
  const [isConverting, setIsConverting] = useState(false);
  const [convertedFileUrl, setConvertedFileUrl] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [conversionProgress, setConversionProgress] = useState(0);

  const handleConvert = async () => {
    if (
      (activeTab === "url" && !url) ||
      (activeTab === "html" && !htmlContent)
    ) {
      setError("Please provide a URL or HTML content to convert");
      return;
    }

    setIsConverting(true);
    setError(null);
    setConversionProgress(0);

    try {
      // Create FormData for file upload
      const formData = new FormData();

      if (activeTab === "url" && url) {
        formData.append('url', url);
      } else if (activeTab === "html" && htmlContent) {
        formData.append('htmlContent', htmlContent);
      } else {
        throw new Error('Please provide either a URL or HTML content');
      }

      // Start progress simulation
      const progressInterval = setInterval(() => {
        setConversionProgress(prev => {
          if (prev >= 90) return prev;
          return prev + Math.random() * 10;
        });
      }, 500);

      // Send to conversion API
      const response = await fetch('/api/tools/html-to-pdf', {
        method: 'POST',
        body: formData,
      });

      clearInterval(progressInterval);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Conversion failed');
      }

      // Get the converted PDF blob
      const pdfBlob = await response.blob();
      setConvertedFileUrl(URL.createObjectURL(pdfBlob));
      setConversionProgress(100);

    } catch (err) {
      setError(err instanceof Error ? err.message : "An error occurred during conversion. Please try again.");
      console.error(err);
    } finally {
      setIsConverting(false);
    }
  };

  const handleDownload = () => {
    if (convertedFileUrl) {
      const link = document.createElement("a");
      link.href = convertedFileUrl;
      link.download =
        activeTab === "url"
          ? `${new URL(url).hostname}.pdf`
          : "html-to-pdf.pdf";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  return (
    <div className="space-y-6">
      <div className="bg-blue-50 p-4 rounded-lg">
        <h2 className="text-lg font-semibold mb-2">
          How to Convert HTML to PDF
        </h2>
        <ol className="list-decimal list-inside space-y-2 text-gray-700">
          <li>Enter a URL or paste HTML content using the tabs below.</li>
          <li>Click the "Convert to PDF" button to start the conversion.</li>
          <li>Download your converted PDF document when ready.</li>
        </ol>
      </div>

      <div className="space-y-4">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            <button
              onClick={() => setActiveTab("url")}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${activeTab === "url" ? "border-blue-500 text-blue-600" : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`}
            >
              URL
            </button>
            <button
              onClick={() => setActiveTab("html")}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${activeTab === "html" ? "border-blue-500 text-blue-600" : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`}
            >
              HTML
            </button>
          </nav>
        </div>

        {activeTab === "url" ? (
          <div>
            <label
              htmlFor="url"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              Website URL
            </label>
            <input
              type="url"
              id="url"
              value={url}
              onChange={(e) => setUrl(e.target.value)}
              placeholder="https://example.com"
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
        ) : (
          <div>
            <label
              htmlFor="html"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              HTML Content
            </label>
            <textarea
              id="html"
              value={htmlContent}
              onChange={(e) => setHtmlContent(e.target.value)}
              placeholder="<html><body><h1>Hello World</h1></body></html>"
              rows={8}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 font-mono text-sm"
            />
          </div>
        )}

        <button
          onClick={handleConvert}
          disabled={
            isConverting ||
            (activeTab === "url" && !url) ||
            (activeTab === "html" && !htmlContent)
          }
          className={`w-full py-2 px-4 rounded-md font-medium ${isConverting || (activeTab === "url" && !url) || (activeTab === "html" && !htmlContent) ? "bg-gray-400 cursor-not-allowed" : "bg-blue-600 hover:bg-blue-700 text-white"}`}
        >
          {isConverting ? "Converting..." : "Convert to PDF"}
        </button>

        {isConverting && (
          <div className="space-y-2">
            <div className="w-full bg-gray-200 rounded-full h-2.5">
              <div
                className="bg-blue-600 h-2.5 rounded-full"
                style={{ width: `${conversionProgress}%` }}
              ></div>
            </div>
            <p className="text-sm text-gray-600 text-center">
              {conversionProgress}% complete
            </p>
          </div>
        )}

        {error && (
          <div className="p-4 bg-red-50 text-red-700 rounded-lg">{error}</div>
        )}

        {convertedFileUrl && (
          <div className="p-4 bg-green-50 rounded-lg space-y-4">
            <div className="flex items-center text-green-700">
              <svg
                className="w-5 h-5 mr-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M5 13l4 4L19 7"
                ></path>
              </svg>
              <span>Conversion completed successfully!</span>
            </div>
            <button
              onClick={handleDownload}
              className="w-full py-2 px-4 bg-green-600 hover:bg-green-700 text-white rounded-md font-medium flex items-center justify-center"
            >
              <svg
                className="w-5 h-5 mr-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"
                ></path>
              </svg>
              Download PDF Document
            </button>
          </div>
        )}
      </div>

      <div className="mt-8 border-t pt-6">
        <h3 className="text-lg font-semibold mb-4">
          About HTML to PDF Conversion
        </h3>
        <p className="text-gray-700 mb-4">
          Our HTML to PDF converter transforms web pages or HTML code into PDF
          documents, preserving the layout, formatting, images, and styling.
          This is useful for archiving web content, creating documentation, or
          generating reports from HTML templates.
        </p>
        <div className="bg-yellow-50 p-4 rounded-lg">
          <h4 className="font-medium text-yellow-800 mb-2">Please Note:</h4>
          <p className="text-yellow-700 text-sm">
            The conversion process captures the current state of the webpage.
            Dynamic content that requires user interaction or JavaScript
            execution might not be fully captured. For best results with URL
            conversion, ensure the website is publicly accessible.
          </p>
        </div>
      </div>
    </div>
  );
}
