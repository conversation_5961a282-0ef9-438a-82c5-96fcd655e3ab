// Server-compatible configuration for tool implementations
// This file can be imported by both server and client components

/**
 * List of tools that have specific converter implementations
 * These tools will use DynamicToolLoader instead of GenericConverter
 */
export const TOOLS_WITH_SPECIFIC_IMPLEMENTATIONS = [
  'merge-pdf',
  'split-pdf', 
  'compress-pdf',
  'pdf-to-word',
  'word-to-pdf',
  'pdf-to-excel',
  'excel-to-pdf',
  'pdf-to-powerpoint',
  'powerpoint-to-pdf',
  'pdf-to-jpg',
  'jpg-to-pdf',
  'html-to-pdf',
  'protect-pdf',
  'rotate-pdf',
  'add-watermark',
  'pdf-to-pdfa'
] as const;

/**
 * Type for tools with specific implementations
 */
export type ToolWithImplementation = typeof TOOLS_WITH_SPECIFIC_IMPLEMENTATIONS[number];

/**
 * Check if a tool has a specific implementation
 * @param toolId - The tool identifier (slug)
 * @returns true if the tool has a specific implementation, false otherwise
 */
export function hasSpecificImplementation(toolId: string): boolean {
  return TOOLS_WITH_SPECIFIC_IMPLEMENTATIONS.includes(toolId as ToolWithImplementation);
}

/**
 * Get all tools with specific implementations
 * @returns Array of tool IDs that have specific implementations
 */
export function getToolsWithImplementations(): readonly string[] {
  return TOOLS_WITH_SPECIFIC_IMPLEMENTATIONS;
}

/**
 * Tool implementation status
 */
export const TOOL_IMPLEMENTATION_STATUS = {
  // Tools with real functionality
  WORKING_TOOLS: [
    'excel-to-pdf',
    'word-to-pdf', 
    'pdf-to-word',
    'pdf-to-excel',
    'powerpoint-to-pdf',
    'pdf-to-powerpoint',
    'jpg-to-pdf',
    'pdf-to-jpg',
    'html-to-pdf',
    'merge-pdf',
    'split-pdf',
    'compress-pdf',
    'rotate-pdf',
    'add-watermark',
    'protect-pdf'
  ],
  
  // Tools that return 501 with alternatives
  NOT_IMPLEMENTED_TOOLS: [
    'pdf-to-pdfa'
  ],
  
  // Tools using generic converter (simulation)
  GENERIC_TOOLS: [
    'png-to-pdf'
  ]
} as const;

/**
 * Check if a tool is fully working (has real conversion functionality)
 * @param toolId - The tool identifier
 * @returns true if the tool has working conversion functionality
 */
export function isWorkingTool(toolId: string): boolean {
  return TOOL_IMPLEMENTATION_STATUS.WORKING_TOOLS.includes(toolId as any);
}

/**
 * Check if a tool is not implemented (returns 501)
 * @param toolId - The tool identifier  
 * @returns true if the tool returns 501 with alternatives
 */
export function isNotImplementedTool(toolId: string): boolean {
  return TOOL_IMPLEMENTATION_STATUS.NOT_IMPLEMENTED_TOOLS.includes(toolId as any);
}

/**
 * Check if a tool uses generic converter (simulation)
 * @param toolId - The tool identifier
 * @returns true if the tool uses generic converter
 */
export function isGenericTool(toolId: string): boolean {
  return TOOL_IMPLEMENTATION_STATUS.GENERIC_TOOLS.includes(toolId as any);
}
