import { NextRequest } from 'next/server';

// Security monitoring and threat detection
export interface SecurityEvent {
  type: 'rate_limit_exceeded' | 'suspicious_request' | 'blocked_user_agent' | 'csrf_violation' | 'injection_attempt';
  severity: 'low' | 'medium' | 'high' | 'critical';
  ip: string;
  userAgent: string;
  path: string;
  timestamp: number;
  details: Record<string, any>;
}

export interface SecurityMetrics {
  totalRequests: number;
  blockedRequests: number;
  rateLimitViolations: number;
  csrfViolations: number;
  suspiciousRequests: number;
  lastReset: number;
}

// In-memory security metrics store (for production, consider Redis or database)
class SecurityMetricsStore {
  private metrics: SecurityMetrics = {
    totalRequests: 0,
    blockedRequests: 0,
    rateLimitViolations: 0,
    csrfViolations: 0,
    suspiciousRequests: 0,
    lastReset: Date.now(),
  };

  private events: SecurityEvent[] = [];
  private readonly MAX_EVENTS = 1000;
  private readonly METRICS_RESET_INTERVAL = 24 * 60 * 60 * 1000; // 24 hours

  increment(metric: keyof Omit<SecurityMetrics, 'lastReset'>): void {
    this.metrics[metric]++;
    this.checkReset();
  }

  addEvent(event: SecurityEvent): void {
    this.events.push(event);
    
    // Keep only the most recent events
    if (this.events.length > this.MAX_EVENTS) {
      this.events = this.events.slice(-this.MAX_EVENTS);
    }
  }

  getMetrics(): SecurityMetrics {
    this.checkReset();
    return { ...this.metrics };
  }

  getRecentEvents(limit: number = 100): SecurityEvent[] {
    return this.events.slice(-limit);
  }

  getEventsByType(type: SecurityEvent['type'], limit: number = 50): SecurityEvent[] {
    return this.events
      .filter(event => event.type === type)
      .slice(-limit);
  }

  private checkReset(): void {
    const now = Date.now();
    if (now - this.metrics.lastReset > this.METRICS_RESET_INTERVAL) {
      this.resetMetrics();
    }
  }

  private resetMetrics(): void {
    this.metrics = {
      totalRequests: 0,
      blockedRequests: 0,
      rateLimitViolations: 0,
      csrfViolations: 0,
      suspiciousRequests: 0,
      lastReset: Date.now(),
    };
  }
}

// Global security metrics store
const securityStore = new SecurityMetricsStore();

// Threat detection patterns - refined to reduce false positives
const THREAT_PATTERNS = {
  sqlInjection: [
    // More specific SQL injection patterns
    /(\b(union\s+select|insert\s+into|update\s+set|delete\s+from|drop\s+table|create\s+table|alter\s+table|exec\s*\(|execute\s*\()\b)/i,
    // SQL injection with quotes but not normal query parameters
    /((\%27)|(\'))\s*(union|select|insert|update|delete|drop|or\s+1\s*=\s*1|and\s+1\s*=\s*1)/i,
    // Malicious HTML/XML tags
    /((\%3C)|<)((\%2F)|\/)*[a-z0-9\%]+((\%3E)|>)/i,
  ],
  xss: [
    /<script[^>]*>.*?<\/script>/gi,
    /javascript\s*:/gi,
    /on(load|click|error|focus|blur|submit|change|mouseover|mouseout)\s*=/gi,
    /<iframe[^>]*>.*?<\/iframe>/gi,
    /<object[^>]*>.*?<\/object>/gi,
    /<embed[^>]*>/gi,
  ],
  pathTraversal: [
    /\.\.\//g,
    /\.\.\\/g,
    /%2e%2e%2f/gi,
    /%2e%2e%5c/gi,
    /\.\.%2f/gi,
    /\.\.%5c/gi,
  ],
  commandInjection: [
    // More specific command injection patterns
    /[;&|`$]\s*(cat|ls|pwd|whoami|id|uname|ps|netstat|ifconfig|ping|wget|curl|rm|mv|cp|chmod|chown)/gi,
    /\$\([^)]*\)/g,
    /`[^`]*`/g,
    // Exclude normal query parameters and JSON
    /[;&|]\s*[a-zA-Z_][a-zA-Z0-9_]*\s*=/,
  ],
};

// Suspicious user agents
const SUSPICIOUS_USER_AGENTS = [
  'sqlmap',
  'nikto',
  'nmap',
  'masscan',
  'zap',
  'burp',
  'w3af',
  'acunetix',
  'nessus',
  'openvas',
  'metasploit',
];

// Whitelist for legitimate query parameters and patterns
const LEGITIMATE_PATTERNS = [
  // Common query parameters
  /^[?&](page|limit|offset|sort|order|status|admin|category|tag|search|q|id|slug|type)=[^&]*$/i,
  // Boolean values
  /^[?&]\w+=(true|false)$/i,
  // Numeric values
  /^[?&]\w+=\d+$/i,
  // Common status values
  /^[?&]status=(published|draft|pending|active|inactive)$/i,
  // Common admin flags
  /^[?&]admin=(true|false)$/i,
];

// Get client IP address
export function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for');
  const realIp = request.headers.get('x-real-ip');
  const cfConnectingIp = request.headers.get('cf-connecting-ip');
  
  return forwarded?.split(',')[0]?.trim() ||
         realIp ||
         cfConnectingIp ||
         'unknown';
}

// Check if URL contains only legitimate patterns
function isLegitimateRequest(url: string): boolean {
  // If no query parameters, it's safe
  if (!url.includes('?')) {
    return true;
  }

  // Extract query string
  const queryString = url.split('?')[1];
  if (!queryString) {
    return true;
  }

  // Split into individual parameters
  const params = queryString.split('&');

  // Check each parameter against whitelist
  for (const param of params) {
    const paramWithPrefix = `?${param}`;
    const isLegitimate = LEGITIMATE_PATTERNS.some(pattern => pattern.test(paramWithPrefix));
    if (!isLegitimate) {
      // Allow common encoded characters in legitimate contexts
      if (!/^[a-zA-Z_][a-zA-Z0-9_]*=[a-zA-Z0-9%._-]*$/.test(param)) {
        return false;
      }
    }
  }

  return true;
}

// Detect threat patterns in request
export function detectThreats(request: NextRequest): SecurityEvent[] {
  const events: SecurityEvent[] = [];
  const ip = getClientIP(request);
  const userAgent = request.headers.get('user-agent') || '';
  const path = request.nextUrl.pathname;
  const searchParams = request.nextUrl.searchParams.toString();
  const fullUrl = `${path}?${searchParams}`;

  // Skip threat detection for legitimate requests
  if (isLegitimateRequest(fullUrl)) {
    // Only check for suspicious user agents on legitimate requests
    const lowerUserAgent = userAgent.toLowerCase();
    for (const suspiciousAgent of SUSPICIOUS_USER_AGENTS) {
      if (lowerUserAgent.includes(suspiciousAgent)) {
        events.push({
          type: 'suspicious_request',
          severity: 'medium',
          ip,
          userAgent,
          path,
          timestamp: Date.now(),
          details: {
            threatType: 'suspicious_user_agent',
            detectedAgent: suspiciousAgent,
          },
        });
      }
    }
    return events;
  }

  // Check for SQL injection on suspicious requests
  for (const pattern of THREAT_PATTERNS.sqlInjection) {
    if (pattern.test(fullUrl)) {
      events.push({
        type: 'injection_attempt',
        severity: 'high',
        ip,
        userAgent,
        path,
        timestamp: Date.now(),
        details: {
          threatType: 'sql_injection',
          pattern: pattern.source,
          url: fullUrl,
        },
      });
    }
  }

  // Check for XSS
  for (const pattern of THREAT_PATTERNS.xss) {
    if (pattern.test(fullUrl)) {
      events.push({
        type: 'injection_attempt',
        severity: 'high',
        ip,
        userAgent,
        path,
        timestamp: Date.now(),
        details: {
          threatType: 'xss',
          pattern: pattern.source,
          url: fullUrl,
        },
      });
    }
  }

  // Check for path traversal
  for (const pattern of THREAT_PATTERNS.pathTraversal) {
    if (pattern.test(fullUrl)) {
      events.push({
        type: 'injection_attempt',
        severity: 'medium',
        ip,
        userAgent,
        path,
        timestamp: Date.now(),
        details: {
          threatType: 'path_traversal',
          pattern: pattern.source,
          url: fullUrl,
        },
      });
    }
  }

  // Check for command injection
  for (const pattern of THREAT_PATTERNS.commandInjection) {
    if (pattern.test(fullUrl)) {
      events.push({
        type: 'injection_attempt',
        severity: 'critical',
        ip,
        userAgent,
        path,
        timestamp: Date.now(),
        details: {
          threatType: 'command_injection',
          pattern: pattern.source,
          url: fullUrl,
        },
      });
    }
  }

  // Suspicious user agent check is now handled above for legitimate requests

  return events;
}

// Log security event
export function logSecurityEvent(event: SecurityEvent): void {
  securityStore.addEvent(event);
  
  // Increment relevant metrics
  switch (event.type) {
    case 'rate_limit_exceeded':
      securityStore.increment('rateLimitViolations');
      break;
    case 'csrf_violation':
      securityStore.increment('csrfViolations');
      break;
    case 'suspicious_request':
    case 'injection_attempt':
      securityStore.increment('suspiciousRequests');
      break;
    case 'blocked_user_agent':
      securityStore.increment('blockedRequests');
      break;
  }

  // Log to console in development
  if (process.env.NODE_ENV === 'development') {
    console.warn(`[SECURITY] ${event.type}:`, event);
  }

  // In production, you might want to send to external monitoring service
  if (process.env.NODE_ENV === 'production' && event.severity === 'critical') {
    // Send to monitoring service (implement as needed)
    console.error(`[CRITICAL SECURITY EVENT]`, event);
  }
}

// Monitor request and detect threats
export function monitorRequest(request: NextRequest): SecurityEvent[] {
  securityStore.increment('totalRequests');
  
  const threats = detectThreats(request);
  
  // Log all detected threats
  threats.forEach(threat => {
    logSecurityEvent(threat);
  });

  return threats;
}

// Get security metrics
export function getSecurityMetrics(): SecurityMetrics {
  return securityStore.getMetrics();
}

// Get recent security events
export function getRecentSecurityEvents(limit: number = 100): SecurityEvent[] {
  return securityStore.getRecentEvents(limit);
}

// Get security events by type
export function getSecurityEventsByType(type: SecurityEvent['type'], limit: number = 50): SecurityEvent[] {
  return securityStore.getEventsByType(type, limit);
}

// Check if IP should be blocked based on recent activity
export function shouldBlockIP(ip: string): boolean {
  const recentEvents = securityStore.getRecentEvents(500);
  const ipEvents = recentEvents.filter(event => event.ip === ip);

  // Block if too many critical events in the last hour
  const oneHourAgo = Date.now() - (60 * 60 * 1000);
  const recentCriticalEvents = ipEvents.filter(
    event => event.timestamp > oneHourAgo && event.severity === 'critical'
  );

  // Increased threshold for critical events
  if (recentCriticalEvents.length >= 5) {
    return true;
  }

  // Block if too many high severity events in the last hour (excluding false positives)
  const recentHighSeverityEvents = ipEvents.filter(
    event => event.timestamp > oneHourAgo &&
    event.severity === 'high' &&
    event.type === 'injection_attempt'
  );

  // Increased threshold for high severity events
  if (recentHighSeverityEvents.length >= 20) {
    return true;
  }

  // Block if too many suspicious user agent events (actual malicious tools)
  const recentSuspiciousAgentEvents = ipEvents.filter(
    event => event.timestamp > oneHourAgo &&
    event.type === 'suspicious_request' &&
    event.details?.threatType === 'suspicious_user_agent'
  );

  if (recentSuspiciousAgentEvents.length >= 5) {
    return true;
  }

  return false;
}

// Generate security report
export function generateSecurityReport(): {
  metrics: SecurityMetrics;
  recentThreats: SecurityEvent[];
  topThreats: { type: string; count: number }[];
  topIPs: { ip: string; count: number }[];
} {
  const metrics = getSecurityMetrics();
  const recentEvents = getRecentSecurityEvents(1000);
  
  // Count threats by type
  const threatCounts = recentEvents.reduce((acc, event) => {
    acc[event.type] = (acc[event.type] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);
  
  const topThreats = Object.entries(threatCounts)
    .map(([type, count]) => ({ type, count }))
    .sort((a, b) => b.count - a.count)
    .slice(0, 10);
  
  // Count events by IP
  const ipCounts = recentEvents.reduce((acc, event) => {
    acc[event.ip] = (acc[event.ip] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);
  
  const topIPs = Object.entries(ipCounts)
    .map(([ip, count]) => ({ ip, count }))
    .sort((a, b) => b.count - a.count)
    .slice(0, 10);
  
  return {
    metrics,
    recentThreats: recentEvents.slice(-50),
    topThreats,
    topIPs,
  };
}
