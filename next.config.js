/** @type {import('next').NextConfig} */
const nextConfig = {
  // Core options
  reactStrictMode: true,

  // Performance optimizations
  compress: true,
  poweredByHeader: false,

  // Enhanced image optimization
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'images.unsplash.com',
      },
      {
        protocol: 'http',
        hostname: 'localhost',
      },
      {
        protocol: 'https',
        hostname: 'res.cloudinary.com',
      },
      {
        protocol: 'https',
        hostname: '**',
      },
    ],
    // Performance optimizations for images
    formats: ['image/webp', 'image/avif'],
    minimumCacheTTL: 60,
    dangerouslyAllowSVG: true,
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
  },

  // Disable trailing slash to fix static asset loading
  trailingSlash: false,

  // Enhanced experimental features for performance
  experimental: {
    // Optimize package imports for better tree shaking
    optimizePackageImports: [
      'lucide-react',
      'framer-motion',
      'react-icons',
      '@radix-ui/react-dialog',
      '@radix-ui/react-dropdown-menu',
      '@radix-ui/react-select',
      '@radix-ui/react-popover',
      '@radix-ui/react-toast',
      '@radix-ui/react-tabs',
      '@radix-ui/react-accordion',
      '@radix-ui/react-alert-dialog',
      '@radix-ui/react-avatar',
      '@radix-ui/react-checkbox',
      '@radix-ui/react-label',
      '@radix-ui/react-progress',
      '@radix-ui/react-radio-group',
      '@radix-ui/react-separator',
      '@radix-ui/react-slider',
      '@radix-ui/react-switch',
      'recharts',
      'date-fns'
    ],
    // Server external packages for Next.js 14
    serverComponentsExternalPackages: ['mongoose', 'mongodb', 'puppeteer', 'canvas', 'sharp', 'pdf-parse', 'pdfjs-dist'],
    // Enable optimized CSS loading
    optimizeCss: true,
    // Enable gzip compression
    gzipSize: true,
  },

  // Turbopack configuration removed to fix DataCloneError

  // Compiler optimizations
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production',
    // Remove React DevTools in production
    reactRemoveProperties: process.env.NODE_ENV === 'production',
  },

  // Enhanced webpack configuration for optimal bundle splitting
  webpack: (config, { isServer }) => {
    // Handle pdf-parse worker import issues
    config.resolve.fallback = {
      ...config.resolve.fallback,
      fs: false,
      path: false,
      crypto: false,
    };

    // Ignore problematic worker imports
    config.module.rules.push({
      test: /pdf-parse/,
      resolve: {
        fallback: {
          fs: false,
          path: false,
          crypto: false,
        },
      },
    });

    if (!isServer) {
      config.optimization = {
        ...config.optimization,
        splitChunks: {
          chunks: 'all',
          minSize: 20000,
          maxSize: 244000,
          cacheGroups: {
            // Framework chunk (React, Next.js, react-icons)
            framework: {
              name: 'framework',
              chunks: 'all',
              test: /[\\/]node_modules[\\/](react|react-dom|scheduler|next|react-icons)[\\/]/,
              priority: 40,
              enforce: true,
            },
            // PDF processing libraries
            pdfLibs: {
              name: 'pdf-libs',
              chunks: 'all',
              test: /[\\/]node_modules[\\/](pdf-lib|pdf-parse|pdfjs-dist|jspdf|pdf2pic)[\\/]/,
              priority: 35,
              enforce: true,
            },
            // UI libraries
            uiLibs: {
              name: 'ui-libs',
              chunks: 'all',
              test: /[\\/]node_modules[\\/](@radix-ui|framer-motion|lucide-react)[\\/]/,
              priority: 30,
              enforce: true,
            },
            // Heavy utilities
            heavyUtils: {
              name: 'heavy-utils',
              chunks: 'all',
              test: /[\\/]node_modules[\\/](mammoth|docx|xlsx|jszip|puppeteer|canvas)[\\/]/,
              priority: 25,
              enforce: true,
            },
            // Default vendor chunk for other libraries
            vendor: {
              test: /[\\/]node_modules[\\/]/,
              name: 'vendors',
              priority: 20,
              chunks: 'all',
            },
          },
        },
      };
    }

    return config;
  },

  // Enhanced static generation configuration
  staticPageGenerationTimeout: 120,

  // Enterprise-grade security headers
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          // DNS Prefetch Control
          {
            key: 'X-DNS-Prefetch-Control',
            value: 'on'
          },
          // XSS Protection
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block'
          },
          // Frame Options
          {
            key: 'X-Frame-Options',
            value: 'SAMEORIGIN'
          },
          // Content Type Options
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff'
          },
          // Referrer Policy
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin'
          },
          // Permissions Policy
          {
            key: 'Permissions-Policy',
            value: 'camera=(), microphone=(), geolocation=(), interest-cohort=()'
          },
          // Content Security Policy
          {
            key: 'Content-Security-Policy',
            value: [
              "default-src 'self'",
              "script-src 'self' 'unsafe-inline' 'unsafe-eval'",
              "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
              "font-src 'self' https://fonts.gstatic.com data:",
              "img-src 'self' data: blob: https: http:",
              "media-src 'self' data: blob:",
              "connect-src 'self' https: wss:",
              "frame-src 'self' https:",
              "object-src 'none'",
              "base-uri 'self'",
              "form-action 'self'",
              "frame-ancestors 'self'",
              "upgrade-insecure-requests"
            ].join('; ')
          },
          // Strict Transport Security (HSTS)
          {
            key: 'Strict-Transport-Security',
            value: 'max-age=31536000; includeSubDomains; preload'
          },
        ],
      },
      {
        source: '/api/(.*)',
        headers: [
          // API Cache Control
          {
            key: 'Cache-Control',
            value: 'no-cache, no-store, must-revalidate'
          },
          // API Security Headers
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff'
          },
          {
            key: 'X-Frame-Options',
            value: 'DENY'
          },
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block'
          },
        ],
      },
      {
        source: '/_next/static/(.*)',
        headers: [
          // Static Assets Cache Control
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable'
          },
        ],
      },
      {
        source: '/images/(.*)',
        headers: [
          // Images Cache Control
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000'
          },
        ],
      },
    ];
  },

  // Ensure proper asset handling
  assetPrefix: '',
}

module.exports = nextConfig;
