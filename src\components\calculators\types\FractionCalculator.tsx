"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

interface Fraction {
  numerator: number;
  denominator: number;
}

export default function FractionCalculator() {
  const [fraction1, setFraction1] = useState<Fraction>({ numerator: 1, denominator: 2 });
  const [fraction2, setFraction2] = useState<Fraction>({ numerator: 1, denominator: 3 });
  const [operation, setOperation] = useState<string>("add");
  const [result, setResult] = useState<Fraction | null>(null);
  const [decimalResult, setDecimalResult] = useState<number | null>(null);

  // Greatest Common Divisor
  const gcd = (a: number, b: number): number => {
    a = Math.abs(a);
    b = Math.abs(b);
    while (b !== 0) {
      const temp = b;
      b = a % b;
      a = temp;
    }
    return a;
  };

  // Simplify fraction
  const simplifyFraction = (fraction: Fraction): Fraction => {
    const divisor = gcd(fraction.numerator, fraction.denominator);
    let num = fraction.numerator / divisor;
    let den = fraction.denominator / divisor;
    
    // Handle negative fractions
    if (den < 0) {
      num = -num;
      den = -den;
    }
    
    return { numerator: num, denominator: den };
  };

  // Add fractions
  const addFractions = (f1: Fraction, f2: Fraction): Fraction => {
    const numerator = f1.numerator * f2.denominator + f2.numerator * f1.denominator;
    const denominator = f1.denominator * f2.denominator;
    return simplifyFraction({ numerator, denominator });
  };

  // Subtract fractions
  const subtractFractions = (f1: Fraction, f2: Fraction): Fraction => {
    const numerator = f1.numerator * f2.denominator - f2.numerator * f1.denominator;
    const denominator = f1.denominator * f2.denominator;
    return simplifyFraction({ numerator, denominator });
  };

  // Multiply fractions
  const multiplyFractions = (f1: Fraction, f2: Fraction): Fraction => {
    const numerator = f1.numerator * f2.numerator;
    const denominator = f1.denominator * f2.denominator;
    return simplifyFraction({ numerator, denominator });
  };

  // Divide fractions
  const divideFractions = (f1: Fraction, f2: Fraction): Fraction => {
    if (f2.numerator === 0) {
      throw new Error("Cannot divide by zero");
    }
    const numerator = f1.numerator * f2.denominator;
    const denominator = f1.denominator * f2.numerator;
    return simplifyFraction({ numerator, denominator });
  };

  const calculate = () => {
    try {
      let resultFraction: Fraction;

      switch (operation) {
        case "add":
          resultFraction = addFractions(fraction1, fraction2);
          break;
        case "subtract":
          resultFraction = subtractFractions(fraction1, fraction2);
          break;
        case "multiply":
          resultFraction = multiplyFractions(fraction1, fraction2);
          break;
        case "divide":
          resultFraction = divideFractions(fraction1, fraction2);
          break;
        default:
          throw new Error("Invalid operation");
      }

      setResult(resultFraction);
      setDecimalResult(resultFraction.numerator / resultFraction.denominator);
    } catch (error) {
      alert(error instanceof Error ? error.message : "Calculation error");
    }
  };

  const formatFraction = (fraction: Fraction): string => {
    if (fraction.denominator === 1) {
      return fraction.numerator.toString();
    }
    if (Math.abs(fraction.numerator) > Math.abs(fraction.denominator)) {
      const whole = Math.floor(Math.abs(fraction.numerator) / Math.abs(fraction.denominator));
      const remainder = Math.abs(fraction.numerator) % Math.abs(fraction.denominator);
      const sign = fraction.numerator < 0 ? "-" : "";
      if (remainder === 0) {
        return `${sign}${whole}`;
      }
      return `${sign}${whole} ${remainder}/${Math.abs(fraction.denominator)}`;
    }
    return `${fraction.numerator}/${fraction.denominator}`;
  };

  const getOperationSymbol = (op: string): string => {
    switch (op) {
      case "add": return "+";
      case "subtract": return "-";
      case "multiply": return "×";
      case "divide": return "÷";
      default: return "";
    }
  };

  const reset = () => {
    setFraction1({ numerator: 1, denominator: 2 });
    setFraction2({ numerator: 1, denominator: 3 });
    setOperation("add");
    setResult(null);
    setDecimalResult(null);
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Fraction Calculator</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Input Section */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 items-end">
            {/* Fraction 1 */}
            <div className="space-y-2">
              <Label>First Fraction</Label>
              <div className="flex items-center space-x-2">
                <Input
                  type="number"
                  value={fraction1.numerator}
                  onChange={(e) => setFraction1(prev => ({ ...prev, numerator: parseInt(e.target.value) || 0 }))}
                  placeholder="Numerator"
                />
                <span className="text-2xl">/</span>
                <Input
                  type="number"
                  value={fraction1.denominator}
                  onChange={(e) => setFraction1(prev => ({ ...prev, denominator: parseInt(e.target.value) || 1 }))}
                  placeholder="Denominator"
                  min="1"
                />
              </div>
              <div className="text-center text-lg font-mono">
                {formatFraction(fraction1)}
              </div>
            </div>

            {/* Operation */}
            <div className="space-y-2">
              <Label>Operation</Label>
              <Select value={operation} onValueChange={setOperation}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="add">Addition (+)</SelectItem>
                  <SelectItem value="subtract">Subtraction (-)</SelectItem>
                  <SelectItem value="multiply">Multiplication (×)</SelectItem>
                  <SelectItem value="divide">Division (÷)</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Fraction 2 */}
            <div className="space-y-2">
              <Label>Second Fraction</Label>
              <div className="flex items-center space-x-2">
                <Input
                  type="number"
                  value={fraction2.numerator}
                  onChange={(e) => setFraction2(prev => ({ ...prev, numerator: parseInt(e.target.value) || 0 }))}
                  placeholder="Numerator"
                />
                <span className="text-2xl">/</span>
                <Input
                  type="number"
                  value={fraction2.denominator}
                  onChange={(e) => setFraction2(prev => ({ ...prev, denominator: parseInt(e.target.value) || 1 }))}
                  placeholder="Denominator"
                  min="1"
                />
              </div>
              <div className="text-center text-lg font-mono">
                {formatFraction(fraction2)}
              </div>
            </div>
          </div>

          {/* Calculate Button */}
          <div className="flex gap-4">
            <Button onClick={calculate} className="flex-1">
              Calculate
            </Button>
            <Button onClick={reset} variant="outline">
              Reset
            </Button>
          </div>

          {/* Result */}
          {result && (
            <Card className="bg-green-50 dark:bg-green-900/20">
              <CardContent className="pt-6">
                <div className="text-center space-y-4">
                  <div className="text-lg">
                    {formatFraction(fraction1)} {getOperationSymbol(operation)} {formatFraction(fraction2)} =
                  </div>
                  <div className="text-3xl font-bold text-green-600 dark:text-green-400">
                    {formatFraction(result)}
                  </div>
                  <div className="text-lg text-muted-foreground">
                    Decimal: {decimalResult?.toFixed(6)}
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Instructions */}
          <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
            <h3 className="font-semibold mb-2">How to Use</h3>
            <ul className="text-sm space-y-1">
              <li>• Enter numerator and denominator for each fraction</li>
              <li>• Select the operation you want to perform</li>
              <li>• Results are automatically simplified</li>
              <li>• Mixed numbers are shown when appropriate</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
