"use client";

import { useState } from "react";
import FileUploader from "../FileUploader";

export default function SplitPdfConverter() {
  const [file, setFile] = useState<File | null>(null);
  const [pageCount, setPageCount] = useState<number | null>(null);
  const [splitMethod, setSplitMethod] = useState<"all" | "range" | "custom">(
    "all",
  );
  const [pageRange, setPageRange] = useState<string>("");
  const [customSplitPoints, setCustomSplitPoints] = useState<string>("");
  const [isConverting, setIsConverting] = useState(false);
  const [convertedFileUrl, setConvertedFileUrl] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [conversionProgress, setConversionProgress] = useState(0);
  const [splitType, setSplitType] = useState<string>('all');
  const [pageRanges, setPageRanges] = useState<string>('');
  const [pagesPerFile, setPagesPerFile] = useState<number>(1);

  const handleFileSelect = (selectedFile: File) => {
    setFile(selectedFile);
    setConvertedFileUrl(null);
    setError(null);
    setConversionProgress(0);
    // In a real implementation, we would extract the page count from the PDF
    // For now, we'll simulate it with a random number between 5 and 20
    setPageCount(Math.floor(Math.random() * 16) + 5);
  };

  const validateInput = (): boolean => {
    if (!file || !pageCount) {
      setError("Please upload a PDF file");
      return false;
    }

    if (splitMethod === "range") {
      const rangePattern = /^\d+(-\d+)?(,\d+(-\d+)?)*$/;
      if (!rangePattern.test(pageRange)) {
        setError("Please enter a valid page range (e.g., 1-3,5,7-9)");
        return false;
      }

      // Check if any page number exceeds the total page count
      const ranges = pageRange.split(",");
      for (const range of ranges) {
        const parts = range.split("-");
        const start = parseInt(parts[0]);
        const end = parts.length > 1 ? parseInt(parts[1]) : start;

        if (start < 1 || end > pageCount || start > end) {
          setError(
            `Page numbers must be between 1 and ${pageCount} and in ascending order`,
          );
          return false;
        }
      }
    } else if (splitMethod === "custom") {
      const pointsPattern = /^\d+(,\d+)*$/;
      if (!pointsPattern.test(customSplitPoints)) {
        setError("Please enter valid split points (e.g., 3,7,12)");
        return false;
      }

      // Check if any split point exceeds the total page count
      const points = customSplitPoints.split(",").map((p) => parseInt(p));
      for (const point of points) {
        if (point < 2 || point >= pageCount) {
          setError(`Split points must be between 2 and ${pageCount - 1}`);
          return false;
        }
      }
    }

    return true;
  };

  const handleSplit = async () => {
    if (!validateInput()) return;

    setIsConverting(true);
    setError(null);
    setConversionProgress(0);

    try {
      // Create FormData for file upload
      const formData = new FormData();
      if (file) {
        formData.append('file', file);
      }
      formData.append('splitType', splitType);

      if (splitType === 'pages' && pageRanges) {
        formData.append('pageRanges', pageRanges);
      } else if (splitType === 'interval') {
        formData.append('pagesPerFile', pagesPerFile.toString());
      }

      // Start progress simulation
      const progressInterval = setInterval(() => {
        setConversionProgress(prev => {
          if (prev >= 90) return prev;
          return prev + Math.random() * 10;
        });
      }, 400);

      // Send file to split API
      const response = await fetch('/api/tools/split-pdf', {
        method: 'POST',
        body: formData,
      });

      clearInterval(progressInterval);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Split failed');
      }

      // Get the split files ZIP
      const zipBlob = await response.blob();
      setConvertedFileUrl(URL.createObjectURL(zipBlob));
      setConversionProgress(100);

    } catch (err) {
      setError(err instanceof Error ? err.message : "An error occurred during splitting. Please try again.");
      console.error(err);
    } finally {
      setIsConverting(false);
    }
  };

  const handleDownload = () => {
    if (convertedFileUrl) {
      const link = document.createElement("a");
      link.href = convertedFileUrl;
      link.download = file
        ? `${file.name.replace(".pdf", "")}_split.zip`
        : "split_pdf_files.zip";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  return (
    <div className="space-y-6">
      <div className="bg-blue-50 p-4 rounded-lg">
        <h2 className="text-lg font-semibold mb-2">How to Split PDF Files</h2>
        <ol className="list-decimal list-inside space-y-2 text-gray-700">
          <li>Upload your PDF file using the uploader below.</li>
          <li>Choose your preferred splitting method.</li>
          <li>Click the "Split PDF" button to process the file.</li>
          <li>Download your split PDF files as a ZIP archive when ready.</li>
        </ol>
      </div>

      <div className="space-y-4">
        <FileUploader
          acceptedFileTypes=".pdf,application/pdf"
          maxFileSizeMB={10}
          onFileSelect={handleFileSelect}
        />

        {file && (
          <div className="flex items-center space-x-2 p-3 bg-gray-50 rounded-lg">
            <svg
              className="w-6 h-6 text-gray-500"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
              ></path>
            </svg>
            <span className="flex-1 truncate">{file.name}</span>
            <span className="text-sm text-gray-500">
              {(file.size / (1024 * 1024)).toFixed(2)} MB
            </span>
            <button
              onClick={() => setFile(null)}
              className="text-red-500 hover:text-red-700"
              aria-label="Remove file"
            >
              <svg
                className="w-5 h-5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M6 18L18 6M6 6l12 12"
                ></path>
              </svg>
            </button>
          </div>
        )}

        {pageCount && (
          <div className="p-4 bg-gray-50 rounded-lg">
            <p className="text-sm text-gray-700 mb-4">
              Detected {pageCount} pages in the PDF. Choose how you want to
              split the document:
            </p>

            <div className="space-y-4">
              <div className="flex items-center">
                <input
                  id="split-all"
                  type="radio"
                  name="split-method"
                  checked={splitMethod === "all"}
                  onChange={() => setSplitMethod("all")}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                />
                <label
                  htmlFor="split-all"
                  className="ml-2 block text-sm text-gray-700"
                >
                  Split into individual pages (creates {pageCount} files)
                </label>
              </div>

              <div>
                <div className="flex items-center">
                  <input
                    id="split-range"
                    type="radio"
                    name="split-method"
                    checked={splitMethod === "range"}
                    onChange={() => setSplitMethod("range")}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                  />
                  <label
                    htmlFor="split-range"
                    className="ml-2 block text-sm text-gray-700"
                  >
                    Extract specific pages or ranges
                  </label>
                </div>
                {splitMethod === "range" && (
                  <div className="mt-2 ml-6">
                    <input
                      type="text"
                      value={pageRange}
                      onChange={(e) => setPageRange(e.target.value)}
                      placeholder="e.g., 1-3,5,7-9"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"
                    />
                    <p className="mt-1 text-xs text-gray-500">
                      Enter page numbers or ranges separated by commas (e.g.,
                      1-3,5,7-9)
                    </p>
                  </div>
                )}
              </div>

              <div>
                <div className="flex items-center">
                  <input
                    id="split-custom"
                    type="radio"
                    name="split-method"
                    checked={splitMethod === "custom"}
                    onChange={() => setSplitMethod("custom")}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                  />
                  <label
                    htmlFor="split-custom"
                    className="ml-2 block text-sm text-gray-700"
                  >
                    Split at specific pages
                  </label>
                </div>
                {splitMethod === "custom" && (
                  <div className="mt-2 ml-6">
                    <input
                      type="text"
                      value={customSplitPoints}
                      onChange={(e) => setCustomSplitPoints(e.target.value)}
                      placeholder="e.g., 3,7,12"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"
                    />
                    <p className="mt-1 text-xs text-gray-500">
                      Enter page numbers where you want to split the document
                      (e.g., 3,7,12)
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {file && (
          <button
            onClick={handleSplit}
            disabled={isConverting}
            className={`w-full py-2 px-4 rounded-md font-medium ${isConverting ? "bg-gray-400 cursor-not-allowed" : "bg-blue-600 hover:bg-blue-700 text-white"}`}
          >
            {isConverting ? "Splitting..." : "Split PDF"}
          </button>
        )}

        {isConverting && (
          <div className="space-y-2">
            <div className="w-full bg-gray-200 rounded-full h-2.5">
              <div
                className="bg-blue-600 h-2.5 rounded-full"
                style={{ width: `${conversionProgress}%` }}
              ></div>
            </div>
            <p className="text-sm text-gray-600 text-center">
              {conversionProgress}% complete
            </p>
          </div>
        )}

        {error && (
          <div className="p-4 bg-red-50 text-red-700 rounded-lg">{error}</div>
        )}

        {convertedFileUrl && (
          <div className="p-4 bg-green-50 rounded-lg space-y-4">
            <div className="flex items-center text-green-700">
              <svg
                className="w-5 h-5 mr-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M5 13l4 4L19 7"
                ></path>
              </svg>
              <span>Split completed successfully!</span>
            </div>
            <button
              onClick={handleDownload}
              className="w-full py-2 px-4 bg-green-600 hover:bg-green-700 text-white rounded-md font-medium flex items-center justify-center"
            >
              <svg
                className="w-5 h-5 mr-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"
                ></path>
              </svg>
              Download Split PDF Files (ZIP)
            </button>
          </div>
        )}
      </div>

      <div className="mt-8 border-t pt-6">
        <h3 className="text-lg font-semibold mb-4">About PDF Splitting</h3>
        <p className="text-gray-700 mb-4">
          Our PDF splitter allows you to divide a PDF document into multiple
          smaller files. You can split a PDF into individual pages, extract
          specific pages or page ranges, or split the document at designated
          points. This is useful for extracting specific sections from large
          reports, sharing only relevant pages, or breaking down large documents
          into manageable parts.
        </p>
        <div className="bg-yellow-50 p-4 rounded-lg">
          <h4 className="font-medium text-yellow-800 mb-2">Please Note:</h4>
          <p className="text-yellow-700 text-sm">
            The split PDF files will maintain the same quality and formatting as
            the original document. All split files are packaged in a ZIP archive
            for convenient downloading. For large PDFs, the splitting process
            may take longer to complete.
          </p>
        </div>
      </div>
    </div>
  );
}
