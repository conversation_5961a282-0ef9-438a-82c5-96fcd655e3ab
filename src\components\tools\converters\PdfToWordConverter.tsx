"use client";

import { useState, useEffect } from "react";
import FileUploader from "../FileUploader";
import { useProtectedDownload } from "@/hooks/useProtectedDownload";

export default function PdfToWordConverter() {
  const [file, setFile] = useState<File | null>(null);
  const [isConverting, setIsConverting] = useState(false);
  const [convertedFileUrl, setConvertedFileUrl] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [conversionProgress, setConversionProgress] = useState(0);

  // Protected download hook
  const {
    isAuthenticated,
    isLoading: authLoading,
    isDownloading,
    initiateDownload,
    processPendingDownload,
    canDownload,
    needsAuth
  } = useProtectedDownload({
    requireAuth: true,
    onAuthRequired: () => {
      setError("Please log in to download converted files. You can still upload and convert files without logging in.");
    },
    onDownloadStart: (data) => {
      console.log("Starting download:", data);
    },
    onDownloadComplete: (data) => {
      console.log("Download completed:", data);
    },
    onError: (errorMsg) => {
      setError(`Download failed: ${errorMsg}`);
    }
  });

  // Process pending downloads after login
  useEffect(() => {
    if (isAuthenticated) {
      processPendingDownload();
    }
  }, [isAuthenticated, processPendingDownload]);

  const handleFileSelect = (selectedFile: File) => {
    setFile(selectedFile);
    setConvertedFileUrl(null);
    setError(null);
    setConversionProgress(0);
  };

  const handleConvert = async () => {
    if (!file) return;

    setIsConverting(true);
    setError(null);
    setConversionProgress(0);

    try {
      // Create FormData for file upload
      const formData = new FormData();
      formData.append('file', file);

      // Start progress simulation
      const progressInterval = setInterval(() => {
        setConversionProgress(prev => {
          if (prev >= 90) return prev;
          return prev + Math.random() * 10;
        });
      }, 400);

      // Send file to conversion API
      const response = await fetch('/api/tools/pdf-to-word', {
        method: 'POST',
        body: formData,
      });

      clearInterval(progressInterval);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Conversion failed');
      }

      // Get the converted Word document blob
      const wordBlob = await response.blob();
      setConvertedFileUrl(URL.createObjectURL(wordBlob));
      setConversionProgress(100);

    } catch (err) {
      setError(err instanceof Error ? err.message : "An error occurred during conversion. Please try again.");
      console.error(err);
    } finally {
      setIsConverting(false);
    }
  };

  const handleDownload = async () => {
    if (convertedFileUrl && file) {
      const downloadData = {
        fileName: file.name.replace(".pdf", ".docx"),
        conversionType: "pdf-to-word",
        originalFileSize: file.size,
        convertedFileSize: 0, // Will be calculated by the hook
        toolName: "PDF to Word Converter"
      };

      await initiateDownload(convertedFileUrl, downloadData);
    }
  };

  return (
    <div className="space-y-6">
      <div className="bg-blue-50 p-4 rounded-lg">
        <h2 className="text-lg font-semibold mb-2">
          How to Convert PDF to Word
        </h2>
        <ol className="list-decimal list-inside space-y-2 text-gray-700">
          <li>Upload your PDF file using the uploader below.</li>
          <li>Click the "Convert to Word" button to start the conversion.</li>
          <li>Download your converted Word document when ready.</li>
        </ol>
      </div>

      <div className="space-y-4">
        <FileUploader
          acceptedFileTypes=".pdf,application/pdf"
          maxFileSizeMB={10}
          onFileSelect={handleFileSelect}
        />

        {file && (
          <div className="flex items-center space-x-2 p-3 bg-gray-50 rounded-lg">
            <svg
              className="w-6 h-6 text-gray-500"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
              ></path>
            </svg>
            <span className="flex-1 truncate">{file.name}</span>
            <span className="text-sm text-gray-500">
              {(file.size / (1024 * 1024)).toFixed(2)} MB
            </span>
            <button
              onClick={() => setFile(null)}
              className="text-red-500 hover:text-red-700"
              aria-label="Remove file"
            >
              <svg
                className="w-5 h-5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M6 18L18 6M6 6l12 12"
                ></path>
              </svg>
            </button>
          </div>
        )}

        {file && (
          <button
            onClick={handleConvert}
            disabled={isConverting}
            className={`w-full py-2 px-4 rounded-md font-medium ${isConverting ? "bg-gray-400 cursor-not-allowed" : "bg-blue-600 hover:bg-blue-700 text-white"}`}
          >
            {isConverting ? "Converting..." : "Convert to Word"}
          </button>
        )}

        {isConverting && (
          <div className="space-y-2">
            <div className="w-full bg-gray-200 rounded-full h-2.5">
              <div
                className="bg-blue-600 h-2.5 rounded-full"
                style={{ width: `${conversionProgress}%` }}
              ></div>
            </div>
            <p className="text-sm text-gray-600 text-center">
              {conversionProgress}% complete
            </p>
          </div>
        )}

        {error && (
          <div className="p-4 bg-red-50 text-red-700 rounded-lg">{error}</div>
        )}

        {convertedFileUrl && (
          <div className="p-4 bg-green-50 rounded-lg space-y-4">
            <div className="flex items-center text-green-700">
              <svg
                className="w-5 h-5 mr-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M5 13l4 4L19 7"
                ></path>
              </svg>
              <span>Conversion completed successfully!</span>
            </div>
            <button
              onClick={handleDownload}
              disabled={!convertedFileUrl || isDownloading || (needsAuth && authLoading)}
              className="w-full py-2 px-4 bg-green-600 hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white rounded-md font-medium flex items-center justify-center"
            >
              {isDownloading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Downloading...
                </>
              ) : needsAuth ? (
                "Login to Download"
              ) : (
                <>
                  <svg
                    className="w-5 h-5 mr-2"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"
                    ></path>
                  </svg>
                  Download Word Document
                </>
              )}
            </button>
          </div>
        )}
      </div>

      <div className="mt-8 border-t pt-6">
        <h3 className="text-lg font-semibold mb-4">
          About PDF to Word Conversion
        </h3>
        <p className="text-gray-700 mb-4">
          Our PDF to Word converter transforms PDF documents into editable
          Microsoft Word files while preserving the original formatting, images,
          and text. This makes it easy to edit and modify content that was
          previously locked in a PDF format.
        </p>
        <div className="bg-yellow-50 p-4 rounded-lg">
          <h4 className="font-medium text-yellow-800 mb-2">Please Note:</h4>
          <p className="text-yellow-700 text-sm">
            While our converter strives to maintain the original layout, some
            complex formatting or special elements might not convert perfectly.
            For best results, use PDFs with clear text and simple layouts.
          </p>
        </div>
      </div>
    </div>
  );
}
