"use client";

import { lazy, Suspense, ComponentType } from 'react';
import { ToolSkeleton } from './ToolSkeleton';

// Dynamic imports for all PDF tools to enable code splitting
const toolComponents: Record<string, () => Promise<{ default: ComponentType<any> }>> = {
  'merge-pdf': () => import('./converters/MergePdfConverter'),
  'split-pdf': () => import('./converters/SplitPdfConverter'),
  'compress-pdf': () => import('./converters/CompressPdfConverter'),
  'pdf-to-word': () => import('./converters/PdfToWordConverter'),
  'word-to-pdf': () => import('./converters/WordToPdfConverter'),
  'pdf-to-excel': () => import('./converters/PdfToExcelConverter'),
  'excel-to-pdf': () => import('./converters/ExcelToPdfConverter'),
  'pdf-to-powerpoint': () => import('./converters/PdfToPowerPointConverter'),
  'powerpoint-to-pdf': () => import('./converters/PowerPointToPdfConverter'),
  'pdf-to-jpg': () => import('./converters/PdfToJpgConverter'),
  'jpg-to-pdf': () => import('./converters/JpgToPdfConverter'),
  'html-to-pdf': () => import('./converters/HtmlToPdfConverter'),
  'protect-pdf': () => import('./converters/ProtectPdfConverter'),
  'rotate-pdf': () => import('./converters/RotatePdfConverter'),
  'add-watermark': () => import('./converters/AddWatermarkConverter'),
  'pdf-to-pdfa': () => import('./converters/PdfToPdfaConverter'),
};

// Create lazy components with proper error boundaries
const createLazyTool = (importFn: () => Promise<{ default: ComponentType<any> }>) => {
  return lazy(() =>
    importFn()
      .then(module => {
        // Ensure we have a default export
        if (module.default) {
          return module;
        }
        // If no default export, try to find the component
        const componentName = Object.keys(module).find(key =>
          typeof module[key as keyof typeof module] === 'function'
        );
        if (componentName) {
          return { default: module[componentName as keyof typeof module] as ComponentType<any> };
        }
        throw new Error('No valid component found in module');
      })
      .catch(error => {
        console.error('Failed to load tool component:', error);
        // Return a fallback component
        return {
          default: () => (
            <div className="p-8 text-center">
              <h3 className="text-lg font-semibold text-red-600 mb-2">
                Failed to Load Tool
              </h3>
              <p className="text-gray-600 mb-4">
                This tool is temporarily unavailable. Please try again later.
              </p>
              <button
                onClick={() => window.location.reload()}
                className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
              >
                Refresh Page
              </button>
            </div>
          )
        };
      })
  );
};

// Pre-create all lazy components
const lazyTools: Record<string, ComponentType<any>> = {};
Object.entries(toolComponents).forEach(([key, importFn]) => {
  lazyTools[key] = createLazyTool(importFn);
});

interface DynamicToolLoaderProps {
  toolId: string;
  [key: string]: any;
}

export function DynamicToolLoader({ toolId, ...props }: DynamicToolLoaderProps) {
  const LazyTool = lazyTools[toolId];

  if (!LazyTool) {
    return (
      <div className="p-8 text-center">
        <h3 className="text-lg font-semibold text-red-600 mb-2">
          Tool Not Found
        </h3>
        <p className="text-gray-600 mb-4">
          The tool "{toolId}" could not be found.
        </p>
        <a 
          href="/tools" 
          className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors inline-block"
        >
          Browse All Tools
        </a>
      </div>
    );
  }

  return (
    <Suspense fallback={<ToolSkeleton />}>
      <LazyTool {...props} />
    </Suspense>
  );
}

// Export for preloading specific tools
export const preloadTool = (toolId: string) => {
  const importFn = toolComponents[toolId];
  if (importFn) {
    importFn().catch(error => {
      console.error(`Failed to preload tool ${toolId}:`, error);
    });
  }
};

// Export list of available tools
export const availableTools = Object.keys(toolComponents);

// Re-export server-compatible functions for client components
export { hasSpecificImplementation, getToolsWithImplementations } from "@/lib/tool-implementations";

// Preload popular tools on idle
export const preloadPopularTools = () => {
  if (typeof window !== 'undefined' && 'requestIdleCallback' in window) {
    const popularTools = ['merge-pdf', 'compress-pdf', 'pdf-to-word', 'word-to-pdf'];
    
    popularTools.forEach((toolId, index) => {
      window.requestIdleCallback(() => {
        preloadTool(toolId);
      }, { timeout: 1000 + (index * 500) });
    });
  }
};

// Default export
export default DynamicToolLoader;
