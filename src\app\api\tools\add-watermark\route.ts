import { NextRequest, NextResponse } from 'next/server';
import { PDFDocument, rgb, StandardFonts } from 'pdf-lib';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const watermarkText = formData.get('watermarkText') as string || 'WATERMARK';
    const opacity = parseFloat(formData.get('opacity') as string || '0.3');
    const fontSize = parseInt(formData.get('fontSize') as string || '48');
    const position = formData.get('position') as string || 'center';
    const rotation = parseInt(formData.get('rotation') as string || '45');

    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }

    if (file.type !== 'application/pdf') {
      return NextResponse.json(
        { error: 'File must be a PDF document' },
        { status: 400 }
      );
    }

    if (!watermarkText || watermarkText.trim().length === 0) {
      return NextResponse.json(
        { error: 'Watermark text is required' },
        { status: 400 }
      );
    }

    // Validate parameters
    const validOpacity = Math.max(0.1, Math.min(1.0, opacity));
    const validFontSize = Math.max(12, Math.min(120, fontSize));
    const validRotation = Math.max(-180, Math.min(180, rotation));
    const validPositions = ['center', 'top-left', 'top-right', 'bottom-left', 'bottom-right'];
    const validPosition = validPositions.includes(position) ? position : 'center';

    // Convert file to buffer
    const arrayBuffer = await file.arrayBuffer();
    
    // Load the PDF document
    const pdfDoc = await PDFDocument.load(arrayBuffer);
    const totalPages = pdfDoc.getPageCount();

    if (totalPages === 0) {
      return NextResponse.json(
        { error: 'PDF document has no pages' },
        { status: 400 }
      );
    }

    // Embed font for watermark
    const font = await pdfDoc.embedFont(StandardFonts.HelveticaBold);
    
    // Get all pages and add watermark to each
    const pages = pdfDoc.getPages();
    let watermarkedPages = 0;

    for (const page of pages) {
      try {
        const { width, height } = page.getSize();
        
        // Calculate watermark position
        const { x, y } = calculateWatermarkPosition(validPosition, width, height, watermarkText, font, validFontSize);
        
        // Add watermark text
        page.drawText(watermarkText, {
          x: x,
          y: y,
          size: validFontSize,
          font: font,
          color: rgb(0.7, 0.7, 0.7),
          opacity: validOpacity,
          // rotate: validRotation, // TODO: Fix rotation type
        });

        watermarkedPages++;
      } catch (error) {
        console.error(`Error adding watermark to page ${watermarkedPages + 1}:`, error);
        // Continue with other pages even if one fails
      }
    }

    if (watermarkedPages === 0) {
      return NextResponse.json(
        { error: 'Failed to add watermark to any pages' },
        { status: 500 }
      );
    }

    // Save the watermarked PDF
    const pdfBytes = await pdfDoc.save({
      useObjectStreams: true,
      addDefaultPage: false,
    });

    // Generate filename
    const originalName = file.name.replace(/\.pdf$/i, '');
    const watermarkedFilename = `${originalName}_watermarked.pdf`;

    // Create response with watermarked PDF
    const response = new NextResponse(pdfBytes, {
      status: 200,
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `attachment; filename="${watermarkedFilename}"`,
        'X-Original-Size': arrayBuffer.byteLength.toString(),
        'X-Watermarked-Size': pdfBytes.length.toString(),
        'X-Total-Pages': totalPages.toString(),
        'X-Watermarked-Pages': watermarkedPages.toString(),
        'X-Watermark-Text': watermarkText,
        'X-Watermark-Opacity': validOpacity.toString(),
      },
    });

    return response;

  } catch (error) {
    console.error('PDF watermark error:', error);
    
    if (error instanceof Error) {
      if (error.message.includes('Invalid PDF')) {
        return NextResponse.json(
          { error: 'Invalid PDF file. Please ensure the file is not corrupted.' },
          { status: 400 }
        );
      }
      if (error.message.includes('password')) {
        return NextResponse.json(
          { error: 'Password-protected PDF files are not supported.' },
          { status: 400 }
        );
      }
    }

    return NextResponse.json(
      { error: 'Failed to add watermark to PDF. Please ensure the file is a valid PDF document.' },
      { status: 500 }
    );
  }
}

// Helper function to calculate watermark position
function calculateWatermarkPosition(
  position: string, 
  pageWidth: number, 
  pageHeight: number, 
  text: string, 
  font: any, 
  fontSize: number
): { x: number; y: number } {
  const textWidth = font.widthOfTextAtSize(text, fontSize);
  const textHeight = fontSize;

  switch (position) {
    case 'top-left':
      return { x: 50, y: pageHeight - textHeight - 50 };
    case 'top-right':
      return { x: pageWidth - textWidth - 50, y: pageHeight - textHeight - 50 };
    case 'bottom-left':
      return { x: 50, y: 50 };
    case 'bottom-right':
      return { x: pageWidth - textWidth - 50, y: 50 };
    case 'center':
    default:
      return { 
        x: (pageWidth - textWidth) / 2, 
        y: (pageHeight - textHeight) / 2 
      };
  }
}

export async function GET() {
  return NextResponse.json(
    { 
      message: 'PDF Watermark API',
      supportedInput: 'PDF',
      outputFormat: 'PDF',
      watermarkOptions: {
        text: 'Custom text (required)',
        opacity: '0.1 to 1.0 (default: 0.3)',
        fontSize: '12 to 120 (default: 48)',
        position: ['center', 'top-left', 'top-right', 'bottom-left', 'bottom-right'],
        rotation: '-180 to 180 degrees (default: 45)'
      },
      maxFileSize: '10MB',
      note: 'Adds text watermark to all pages of the PDF document'
    },
    { status: 200 }
  );
}
