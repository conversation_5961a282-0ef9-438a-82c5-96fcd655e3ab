"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import Link from "next/link";
import Image from "next/image";
import { BlogLayout } from "@/components/blog/BlogLayout";
import { <PERSON>, CardContent, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Calendar, Clock, Tag, User, ArrowRight } from "lucide-react";
import Header from "@/components/layout/Header";

// Mock blog posts data
const blogPosts = [
  {
    id: "1",
    title: "Top 10 PDF Tools for Professionals",
    slug: "top-10-pdf-tools-professionals",
    excerpt: "Discover the essential PDF tools that every professional should have in their digital toolkit for maximum productivity.",
    image: "https://images.unsplash.com/photo-1606857521015-7f9fcf423740?w=800&q=80",
    date: "October 15, 2023",
    readTime: "5 min read",
    category: "Productivity",
    author: "<PERSON>",
    tags: ["PDF", "Tools", "Productivity"]
  },
  {
    id: "2",
    title: "How to Compress PDF Files Without Losing Quality",
    slug: "compress-pdf-without-losing-quality",
    excerpt: "Learn the best techniques to reduce PDF file sizes while maintaining document quality for easier sharing and storage.",
    image: "https://images.unsplash.com/photo-1586281380349-632531db7ed4?w=800&q=80",
    date: "September 28, 2023",
    readTime: "7 min read",
    category: "Tutorials",
    author: "William Mason",
    tags: ["PDF", "Compression", "Tutorial"]
  },
  {
    id: "3",
    title: "The Ultimate Guide to PDF to Word Conversion",
    slug: "ultimate-guide-pdf-to-word",
    excerpt: "Everything you need to know about converting PDF documents to editable Word files with perfect formatting.",
    image: "https://images.unsplash.com/photo-1568667256549-094345857637?w=800&q=80",
    date: "August 15, 2023",
    readTime: "8 min read",
    category: "Guides",
    author: "Editor User",
    tags: ["PDF", "Word", "Conversion"]
  },
  {
    id: "4",
    title: "5 Ways to Secure Your PDF Documents",
    slug: "5-ways-secure-pdf-documents",
    excerpt: "Protect your sensitive PDF documents with these essential security measures and best practices.",
    image: "https://images.unsplash.com/photo-1563013544-824ae1b704d3?w=800&q=80",
    date: "July 5, 2023",
    readTime: "6 min read",
    category: "Security",
    author: "William Mason",
    tags: ["PDF", "Security", "Protection"]
  },
  {
    id: "5",
    title: "Creating Accessible PDFs: A Complete Guide",
    slug: "creating-accessible-pdfs",
    excerpt: "Learn how to make your PDF documents accessible to everyone, including people with disabilities.",
    image: "https://images.unsplash.com/photo-1551033406-611cf9a28f67?w=800&q=80",
    date: "June 20, 2023",
    readTime: "9 min read",
    category: "Accessibility",
    author: "Editor User",
    tags: ["PDF", "Accessibility", "Inclusive Design"]
  },
  {
    id: "6",
    title: "PDF vs Other Document Formats: When to Use Each",
    slug: "pdf-vs-other-document-formats",
    excerpt: "A comprehensive comparison of PDF with other document formats to help you choose the right format for your needs.",
    image: "https://images.unsplash.com/photo-1618044733300-9472054094ee?w=800&q=80",
    date: "May 12, 2023",
    readTime: "7 min read",
    category: "Comparison",
    author: "William Mason",
    tags: ["PDF", "Document Formats", "Comparison"]
  }
];

export default function BlogHomePage() {
  const [category, setCategory] = useState<string | null>(null);

  const filteredPosts = category
    ? blogPosts.filter(post => post.category === category)
    : blogPosts;

  const categories = Array.from(new Set(blogPosts.map(post => post.category)));

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { type: "spring", stiffness: 300, damping: 24 }
    }
  };

  return (
    <BlogLayout title="Blog" subtitle="Explore our latest articles and insights">
      <div className="max-w-7xl mx-auto">
        {/* Featured post */}
        <motion.div
          className="mb-12"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <div className="relative rounded-xl overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/50 to-transparent z-10" />
            <Image
              src={blogPosts[0].image}
              alt={blogPosts[0].title}
              width={800}
              height={500}
              className="w-full h-[500px] object-cover"
            />
            <div className="absolute bottom-0 left-0 right-0 p-6 md:p-10 z-20">
              <Badge className="mb-4 bg-primary text-primary-foreground">{blogPosts[0].category}</Badge>
              <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-4">{blogPosts[0].title}</h1>
              <p className="text-white/80 text-lg mb-6 max-w-3xl">{blogPosts[0].excerpt}</p>
              <div className="flex items-center gap-6 text-white/70 mb-6">
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4" />
                  <span className="text-sm">{blogPosts[0].date}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4" />
                  <span className="text-sm">{blogPosts[0].readTime}</span>
                </div>
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4" />
                  <span className="text-sm">{blogPosts[0].author}</span>
                </div>
              </div>
              <Button asChild size="lg" className="bg-primary hover:bg-primary/90 text-primary-foreground">
                <Link href={`/blog/${blogPosts[0].slug}`}>
                  Read Article
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            </div>
          </div>
        </motion.div>

        {/* Category filter */}
        <div className="mb-8 flex flex-wrap gap-2">
          <Button
            variant={category === null ? "default" : "outline"}
            onClick={() => setCategory(null)}
            className="rounded-full"
          >
            All
          </Button>
          {categories.map(cat => (
            <Button
              key={cat}
              variant={category === cat ? "default" : "outline"}
              onClick={() => setCategory(cat)}
              className="rounded-full"
            >
              {cat}
            </Button>
          ))}
        </div>

        {/* Blog posts grid */}
        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          {filteredPosts.slice(1).map((post) => (
            <motion.div key={post.id} variants={itemVariants}>
              <Card className="h-full flex flex-col overflow-hidden hover:shadow-md transition-all">
                <Link href={`/blog/${post.slug}`} className="block overflow-hidden">
                  <div className="relative h-48 overflow-hidden">
                    <Image
                      src={post.image}
                      alt={post.title}
                      fill
                      className="object-cover transition-transform hover:scale-110 duration-700"
                    />
                    <div className="absolute top-4 left-4">
                      <Badge className="bg-primary/90 text-primary-foreground">
                        {post.category}
                      </Badge>
                    </div>
                  </div>
                </Link>

                <CardContent className="flex-grow p-5">
                  <div className="flex items-center text-muted-foreground text-xs gap-4 mb-3">
                    <div className="flex items-center gap-1">
                      <Calendar className="h-3 w-3" />
                      <span>{post.date}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Clock className="h-3 w-3" />
                      <span>{post.readTime}</span>
                    </div>
                  </div>

                  <Link href={`/blog/${post.slug}`}>
                    <h2 className="text-xl font-bold mb-2 text-foreground hover:text-primary transition-colors">
                      {post.title}
                    </h2>
                  </Link>

                  <p className="text-muted-foreground mb-4 line-clamp-2">
                    {post.excerpt}
                  </p>

                  <div className="flex items-center gap-2 mb-4">
                    <User className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm text-muted-foreground">{post.author}</span>
                  </div>
                </CardContent>

                <CardFooter className="px-5 pb-5 pt-0">
                  <Link
                    href={`/blog/${post.slug}`}
                    className="text-primary text-sm font-medium hover:underline inline-flex items-center"
                  >
                    Read More
                    <ArrowRight className="h-4 w-4 ml-1" />
                  </Link>
                </CardFooter>
              </Card>
            </motion.div>
          ))}
        </motion.div>


      </div>
    </BlogLayout>
  );
}
