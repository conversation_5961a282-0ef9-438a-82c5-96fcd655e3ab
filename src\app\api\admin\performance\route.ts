import { NextRequest, NextResponse } from 'next/server';
import { getToken } from 'next-auth/jwt';
import { applyAPISecurityHeaders } from '@/lib/security-headers';

const secret = process.env.NEXTAUTH_SECRET;

// Performance metrics store
interface PerformanceMetric {
  timestamp: number;
  route: string;
  method: string;
  responseTime: number;
  statusCode: number;
  userAgent: string;
  ip: string;
}

interface PerformanceStats {
  totalRequests: number;
  averageResponseTime: number;
  slowestRequests: PerformanceMetric[];
  errorRate: number;
  requestsPerMinute: number;
  lastReset: number;
}

// In-memory performance store (for production, consider Redis or database)
class PerformanceStore {
  private metrics: PerformanceMetric[] = [];
  private readonly MAX_METRICS = 10000;
  private readonly RESET_INTERVAL = 24 * 60 * 60 * 1000; // 24 hours
  private lastReset = Date.now();

  addMetric(metric: PerformanceMetric): void {
    this.metrics.push(metric);
    
    // Keep only recent metrics
    if (this.metrics.length > this.MAX_METRICS) {
      this.metrics = this.metrics.slice(-this.MAX_METRICS);
    }
    
    this.checkReset();
  }

  getStats(): PerformanceStats {
    this.checkReset();
    
    if (this.metrics.length === 0) {
      return {
        totalRequests: 0,
        averageResponseTime: 0,
        slowestRequests: [],
        errorRate: 0,
        requestsPerMinute: 0,
        lastReset: this.lastReset,
      };
    }

    const totalRequests = this.metrics.length;
    const averageResponseTime = this.metrics.reduce((sum, m) => sum + m.responseTime, 0) / totalRequests;
    const slowestRequests = [...this.metrics]
      .sort((a, b) => b.responseTime - a.responseTime)
      .slice(0, 10);
    
    const errorRequests = this.metrics.filter(m => m.statusCode >= 400).length;
    const errorRate = (errorRequests / totalRequests) * 100;
    
    // Calculate requests per minute for the last hour
    const oneHourAgo = Date.now() - (60 * 60 * 1000);
    const recentRequests = this.metrics.filter(m => m.timestamp > oneHourAgo);
    const requestsPerMinute = recentRequests.length / 60;

    return {
      totalRequests,
      averageResponseTime: Math.round(averageResponseTime * 100) / 100,
      slowestRequests,
      errorRate: Math.round(errorRate * 100) / 100,
      requestsPerMinute: Math.round(requestsPerMinute * 100) / 100,
      lastReset: this.lastReset,
    };
  }

  getMetricsByRoute(): Record<string, PerformanceStats> {
    const routeMetrics: Record<string, PerformanceMetric[]> = {};
    
    this.metrics.forEach(metric => {
      if (!routeMetrics[metric.route]) {
        routeMetrics[metric.route] = [];
      }
      routeMetrics[metric.route].push(metric);
    });

    const result: Record<string, PerformanceStats> = {};
    
    Object.entries(routeMetrics).forEach(([route, metrics]) => {
      const totalRequests = metrics.length;
      const averageResponseTime = metrics.reduce((sum, m) => sum + m.responseTime, 0) / totalRequests;
      const slowestRequests = [...metrics]
        .sort((a, b) => b.responseTime - a.responseTime)
        .slice(0, 5);
      
      const errorRequests = metrics.filter(m => m.statusCode >= 400).length;
      const errorRate = (errorRequests / totalRequests) * 100;
      
      result[route] = {
        totalRequests,
        averageResponseTime: Math.round(averageResponseTime * 100) / 100,
        slowestRequests,
        errorRate: Math.round(errorRate * 100) / 100,
        requestsPerMinute: 0, // Not calculated per route
        lastReset: this.lastReset,
      };
    });

    return result;
  }

  private checkReset(): void {
    const now = Date.now();
    if (now - this.lastReset > this.RESET_INTERVAL) {
      this.metrics = [];
      this.lastReset = now;
    }
  }
}

// Global performance store
const performanceStore = new PerformanceStore();

// Helper to get client IP
function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for');
  const realIp = request.headers.get('x-real-ip');
  const cfConnectingIp = request.headers.get('cf-connecting-ip');
  
  return forwarded?.split(',')[0]?.trim() ||
         realIp ||
         cfConnectingIp ||
         'unknown';
}

// GET /api/admin/performance - Get performance metrics
export async function GET(request: NextRequest) {
  const startTime = Date.now();
  
  try {
    // Check authentication
    const token = await getToken({ req: request, secret });
    if (!token || token.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized access' },
        { status: 401 }
      );
    }

    const url = new URL(request.url);
    const action = url.searchParams.get('action');

    let data;

    switch (action) {
      case 'stats':
        data = performanceStore.getStats();
        break;
        
      case 'by-route':
        data = performanceStore.getMetricsByRoute();
        break;
        
      case 'health':
        // System health check
        const stats = performanceStore.getStats();
        data = {
          status: 'healthy',
          uptime: process.uptime(),
          memory: process.memoryUsage(),
          performance: stats,
          timestamp: Date.now(),
        };
        break;
        
      default:
        // Return comprehensive performance data
        data = {
          overall: performanceStore.getStats(),
          byRoute: performanceStore.getMetricsByRoute(),
          system: {
            uptime: process.uptime(),
            memory: process.memoryUsage(),
            timestamp: Date.now(),
          },
        };
    }

    const response = NextResponse.json({
      success: true,
      data,
      responseTime: Date.now() - startTime,
    });

    // Record this API call's performance
    performanceStore.addMetric({
      timestamp: Date.now(),
      route: '/api/admin/performance',
      method: 'GET',
      responseTime: Date.now() - startTime,
      statusCode: 200,
      userAgent: request.headers.get('user-agent') || '',
      ip: getClientIP(request),
    });

    return applyAPISecurityHeaders(response);
  } catch (error) {
    console.error('Performance API error:', error);
    
    const response = NextResponse.json(
      { 
        error: 'Internal server error',
        success: false,
        responseTime: Date.now() - startTime,
      },
      { status: 500 }
    );

    // Record this error
    performanceStore.addMetric({
      timestamp: Date.now(),
      route: '/api/admin/performance',
      method: 'GET',
      responseTime: Date.now() - startTime,
      statusCode: 500,
      userAgent: request.headers.get('user-agent') || '',
      ip: getClientIP(request),
    });

    return applyAPISecurityHeaders(response);
  }
}

// POST /api/admin/performance - Performance actions
export async function POST(request: NextRequest) {
  const startTime = Date.now();
  
  try {
    // Check authentication
    const token = await getToken({ req: request, secret });
    if (!token || token.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized access' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { action, metric } = body;

    let result;

    switch (action) {
      case 'record':
        // Record a performance metric
        if (metric) {
          performanceStore.addMetric({
            ...metric,
            timestamp: Date.now(),
          });
          result = { message: 'Metric recorded successfully' };
        } else {
          return NextResponse.json(
            { error: 'Metric data required' },
            { status: 400 }
          );
        }
        break;
        
      case 'benchmark':
        // Run a simple benchmark
        const benchmarkStart = Date.now();
        
        // Simulate some work
        for (let i = 0; i < 100000; i++) {
          Math.random();
        }
        
        const benchmarkTime = Date.now() - benchmarkStart;
        
        result = {
          message: 'Benchmark completed',
          benchmarkTime,
          timestamp: Date.now(),
        };
        break;
        
      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        );
    }

    const response = NextResponse.json({
      success: true,
      data: result,
      responseTime: Date.now() - startTime,
    });

    // Record this API call's performance
    performanceStore.addMetric({
      timestamp: Date.now(),
      route: '/api/admin/performance',
      method: 'POST',
      responseTime: Date.now() - startTime,
      statusCode: 200,
      userAgent: request.headers.get('user-agent') || '',
      ip: getClientIP(request),
    });

    return applyAPISecurityHeaders(response);
  } catch (error) {
    console.error('Performance action error:', error);
    
    const response = NextResponse.json(
      { 
        error: 'Internal server error',
        success: false,
        responseTime: Date.now() - startTime,
      },
      { status: 500 }
    );

    // Record this error
    performanceStore.addMetric({
      timestamp: Date.now(),
      route: '/api/admin/performance',
      method: 'POST',
      responseTime: Date.now() - startTime,
      statusCode: 500,
      userAgent: request.headers.get('user-agent') || '',
      ip: getClientIP(request),
    });

    return applyAPISecurityHeaders(response);
  }
}

// Note: performanceStore is available internally within this module
