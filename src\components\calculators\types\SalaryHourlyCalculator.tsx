"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

interface SalaryBreakdown {
  annual: number;
  monthly: number;
  biweekly: number;
  weekly: number;
  daily: number;
  hourly: number;
}

export default function SalaryHourlyCalculator() {
  // Salary to Hourly
  const [annualSalary, setAnnualSalary] = useState<number>(60000);
  const [hoursPerWeek, setHoursPerWeek] = useState<number>(40);
  const [weeksPerYear, setWeeksPerYear] = useState<number>(52);
  const [vacationDays, setVacationDays] = useState<number>(10);
  const [holidays, setHolidays] = useState<number>(8);

  // Hourly to Salary
  const [hourlyRate, setHourlyRate] = useState<number>(25);
  const [hoursPerWeekHourly, setHoursPerWeekHourly] = useState<number>(40);
  const [weeksPerYearHourly, setWeeksPerYearHourly] = useState<number>(52);

  const [salaryResult, setSalaryResult] = useState<SalaryBreakdown | null>(null);
  const [hourlyResult, setHourlyResult] = useState<SalaryBreakdown | null>(null);

  const calculateFromSalary = () => {
    // Calculate working days
    const workingDaysPerYear = (weeksPerYear * 5) - vacationDays - holidays;
    const workingHoursPerYear = workingDaysPerYear * (hoursPerWeek / 5);
    
    // Alternative calculation using weeks
    const totalWorkingWeeks = weeksPerYear - (vacationDays + holidays) / 5;
    const totalWorkingHours = totalWorkingWeeks * hoursPerWeek;

    // Use the more conservative calculation
    const actualWorkingHours = Math.min(workingHoursPerYear, totalWorkingHours);
    
    const hourly = annualSalary / actualWorkingHours;
    const daily = hourly * (hoursPerWeek / 5);
    const weekly = hourly * hoursPerWeek;
    const biweekly = weekly * 2;
    const monthly = annualSalary / 12;

    setSalaryResult({
      annual: annualSalary,
      monthly,
      biweekly,
      weekly,
      daily,
      hourly
    });
  };

  const calculateFromHourly = () => {
    const annual = hourlyRate * hoursPerWeekHourly * weeksPerYearHourly;
    const monthly = annual / 12;
    const biweekly = hourlyRate * hoursPerWeekHourly * 2;
    const weekly = hourlyRate * hoursPerWeekHourly;
    const daily = hourlyRate * (hoursPerWeekHourly / 5);

    setHourlyResult({
      annual,
      monthly,
      biweekly,
      weekly,
      daily,
      hourly: hourlyRate
    });
  };

  const reset = () => {
    setAnnualSalary(60000);
    setHoursPerWeek(40);
    setWeeksPerYear(52);
    setVacationDays(10);
    setHolidays(8);
    setHourlyRate(25);
    setHoursPerWeekHourly(40);
    setWeeksPerYearHourly(52);
    setSalaryResult(null);
    setHourlyResult(null);
  };

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount);
  };

  const getWorkingHours = (): number => {
    const workingDaysPerYear = (weeksPerYear * 5) - vacationDays - holidays;
    return workingDaysPerYear * (hoursPerWeek / 5);
  };

  const getTaxBracketInfo = (income: number): string => {
    if (income < 10275) return "10% tax bracket";
    if (income < 41775) return "12% tax bracket";
    if (income < 89450) return "22% tax bracket";
    if (income < 190750) return "24% tax bracket";
    if (income < 364200) return "32% tax bracket";
    if (income < 462550) return "35% tax bracket";
    return "37% tax bracket";
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Salary ↔ Hourly Rate Calculator</CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="salary-to-hourly" className="space-y-6">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="salary-to-hourly">Salary → Hourly</TabsTrigger>
              <TabsTrigger value="hourly-to-salary">Hourly → Salary</TabsTrigger>
            </TabsList>

            {/* Salary to Hourly */}
            <TabsContent value="salary-to-hourly" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="annual-salary">Annual Salary</Label>
                    <Input
                      id="annual-salary"
                      type="number"
                      value={annualSalary}
                      onChange={(e) => setAnnualSalary(parseFloat(e.target.value) || 0)}
                      min="0"
                      step="1000"
                    />
                  </div>

                  <div>
                    <Label htmlFor="hours-per-week">Hours per Week</Label>
                    <Input
                      id="hours-per-week"
                      type="number"
                      value={hoursPerWeek}
                      onChange={(e) => setHoursPerWeek(parseFloat(e.target.value) || 0)}
                      min="1"
                      max="80"
                    />
                  </div>

                  <div>
                    <Label htmlFor="weeks-per-year">Weeks per Year</Label>
                    <Input
                      id="weeks-per-year"
                      type="number"
                      value={weeksPerYear}
                      onChange={(e) => setWeeksPerYear(parseInt(e.target.value) || 0)}
                      min="1"
                      max="52"
                    />
                  </div>
                </div>

                <div className="space-y-4">
                  <div>
                    <Label htmlFor="vacation-days">Vacation Days</Label>
                    <Input
                      id="vacation-days"
                      type="number"
                      value={vacationDays}
                      onChange={(e) => setVacationDays(parseInt(e.target.value) || 0)}
                      min="0"
                      max="50"
                    />
                  </div>

                  <div>
                    <Label htmlFor="holidays">Holidays</Label>
                    <Input
                      id="holidays"
                      type="number"
                      value={holidays}
                      onChange={(e) => setHolidays(parseInt(e.target.value) || 0)}
                      min="0"
                      max="20"
                    />
                  </div>

                  <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                    <h3 className="font-semibold mb-2">Working Time</h3>
                    <div className="space-y-1 text-sm">
                      <div className="flex justify-between">
                        <span>Working hours/year:</span>
                        <span>{getWorkingHours().toFixed(0)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Working days/year:</span>
                        <span>{((weeksPerYear * 5) - vacationDays - holidays).toFixed(0)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Tax bracket:</span>
                        <span className="text-xs">{getTaxBracketInfo(annualSalary)}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <Button onClick={calculateFromSalary} className="w-full">
                Calculate Hourly Rate
              </Button>

              {salaryResult && (
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Card className="bg-green-50 dark:bg-green-900/20">
                    <CardContent className="pt-6">
                      <div className="text-center space-y-2">
                        <div className="text-sm text-muted-foreground">Hourly Rate</div>
                        <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                          {formatCurrency(salaryResult.hourly)}
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="bg-blue-50 dark:bg-blue-900/20">
                    <CardContent className="pt-6">
                      <div className="text-center space-y-2">
                        <div className="text-sm text-muted-foreground">Daily Rate</div>
                        <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                          {formatCurrency(salaryResult.daily)}
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="bg-purple-50 dark:bg-purple-900/20">
                    <CardContent className="pt-6">
                      <div className="text-center space-y-2">
                        <div className="text-sm text-muted-foreground">Weekly Rate</div>
                        <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                          {formatCurrency(salaryResult.weekly)}
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="pt-6">
                      <div className="text-center space-y-2">
                        <div className="text-sm text-muted-foreground">Bi-weekly</div>
                        <div className="text-xl font-semibold">
                          {formatCurrency(salaryResult.biweekly)}
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="pt-6">
                      <div className="text-center space-y-2">
                        <div className="text-sm text-muted-foreground">Monthly</div>
                        <div className="text-xl font-semibold">
                          {formatCurrency(salaryResult.monthly)}
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="pt-6">
                      <div className="text-center space-y-2">
                        <div className="text-sm text-muted-foreground">Annual</div>
                        <div className="text-xl font-semibold">
                          {formatCurrency(salaryResult.annual)}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              )}
            </TabsContent>

            {/* Hourly to Salary */}
            <TabsContent value="hourly-to-salary" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <Label htmlFor="hourly-rate">Hourly Rate</Label>
                  <Input
                    id="hourly-rate"
                    type="number"
                    value={hourlyRate}
                    onChange={(e) => setHourlyRate(parseFloat(e.target.value) || 0)}
                    min="0"
                    step="0.25"
                  />
                </div>

                <div>
                  <Label htmlFor="hours-per-week-hourly">Hours per Week</Label>
                  <Input
                    id="hours-per-week-hourly"
                    type="number"
                    value={hoursPerWeekHourly}
                    onChange={(e) => setHoursPerWeekHourly(parseFloat(e.target.value) || 0)}
                    min="1"
                    max="80"
                  />
                </div>

                <div>
                  <Label htmlFor="weeks-per-year-hourly">Weeks per Year</Label>
                  <Input
                    id="weeks-per-year-hourly"
                    type="number"
                    value={weeksPerYearHourly}
                    onChange={(e) => setWeeksPerYearHourly(parseInt(e.target.value) || 0)}
                    min="1"
                    max="52"
                  />
                </div>
              </div>

              <Button onClick={calculateFromHourly} className="w-full">
                Calculate Annual Salary
              </Button>

              {hourlyResult && (
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Card className="bg-green-50 dark:bg-green-900/20">
                    <CardContent className="pt-6">
                      <div className="text-center space-y-2">
                        <div className="text-sm text-muted-foreground">Annual Salary</div>
                        <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                          {formatCurrency(hourlyResult.annual)}
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="bg-blue-50 dark:bg-blue-900/20">
                    <CardContent className="pt-6">
                      <div className="text-center space-y-2">
                        <div className="text-sm text-muted-foreground">Monthly Salary</div>
                        <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                          {formatCurrency(hourlyResult.monthly)}
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="bg-purple-50 dark:bg-purple-900/20">
                    <CardContent className="pt-6">
                      <div className="text-center space-y-2">
                        <div className="text-sm text-muted-foreground">Weekly Salary</div>
                        <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                          {formatCurrency(hourlyResult.weekly)}
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="pt-6">
                      <div className="text-center space-y-2">
                        <div className="text-sm text-muted-foreground">Bi-weekly</div>
                        <div className="text-xl font-semibold">
                          {formatCurrency(hourlyResult.biweekly)}
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="pt-6">
                      <div className="text-center space-y-2">
                        <div className="text-sm text-muted-foreground">Daily</div>
                        <div className="text-xl font-semibold">
                          {formatCurrency(hourlyResult.daily)}
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="pt-6">
                      <div className="text-center space-y-2">
                        <div className="text-sm text-muted-foreground">Hourly</div>
                        <div className="text-xl font-semibold">
                          {formatCurrency(hourlyResult.hourly)}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              )}

              {hourlyResult && (
                <Card>
                  <CardContent className="pt-6">
                    <div className="text-center space-y-2">
                      <div className="text-sm text-muted-foreground">Tax Bracket Information</div>
                      <div className="text-lg font-semibold">
                        {getTaxBracketInfo(hourlyResult.annual)}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        Based on 2023 federal tax brackets (single filer)
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}
            </TabsContent>
          </Tabs>

          <div className="flex gap-4 mt-6">
            <Button onClick={reset} variant="outline" className="flex-1">
              Reset All
            </Button>
          </div>

          {/* Tips */}
          <div className="p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
            <h3 className="font-semibold mb-2">Salary Conversion Tips</h3>
            <ul className="text-sm space-y-1">
              <li>• Include vacation days and holidays for accurate hourly calculations</li>
              <li>• Consider benefits value when comparing salary vs hourly positions</li>
              <li>• Overtime pay typically applies to hourly workers over 40 hours/week</li>
              <li>• Salaried employees may work more than 40 hours without extra pay</li>
              <li>• Tax brackets are marginal - only income above each threshold is taxed at that rate</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
