import { ComponentType } from 'react';

const toolCache = new Map<string, ComponentType>();

export async function loadToolComponent(toolPath: string): Promise<ComponentType | null> {
  // Check cache first
  if (toolCache.has(toolPath)) {
    return toolCache.get(toolPath)!;
  }

  try {
    // Convert kebab-case to PascalCase (pdf-rotate → PdfRotate.tsx)
    const componentName = toolPath
      .split('-')
      .map(part => part.charAt(0).toUpperCase() + part.slice(1))
      .join('');

    // Dynamically import the component
    const moduleImport = await import(`@/components/tools/converters/${componentName}`);

    // Ensure it's a valid React component
    const Component: ComponentType | undefined = moduleImport.default;

    if (!Component) {
      throw new Error(`Component ${componentName} has no default export`);
    }

    // Cache the component for future use
    toolCache.set(toolPath, Component);
    return Component;

  } catch (error: any) {
    console.error(`Tool loading failed for "${toolPath}":`, error.message || error);
    return null;
  }
}
