"use client";

import React, { useEffect, useRef, ReactNode } from 'react';
import { useMemoryCleanup } from '@/utils/memoryManager';
import { usePerformanceMonitor } from '@/hooks/usePerformanceMonitor';

interface PerformanceOptimizerProps {
  children: ReactNode;
  componentName?: string;
  enableMemoryTracking?: boolean;
  enablePerformanceTracking?: boolean;
  preloadResources?: string[];
  criticalResources?: string[];
}

function PerformanceOptimizer({
  children,
  componentName = 'Component',
  enableMemoryTracking = false,
  enablePerformanceTracking = true,
  preloadResources = [],
  criticalResources = []
}: PerformanceOptimizerProps) {
  // Hook usage - must be called unconditionally
  const memoryCleanup = useMemoryCleanup();
  const performanceMonitor = usePerformanceMonitor({
    trackMemory: enableMemoryTracking,
    componentName,
    onMetricsUpdate: (metrics) => {
      if (process.env.NODE_ENV === 'development') {
        console.log(`[Performance] ${componentName} metrics:`, metrics);
      }
    }
  });

  const hasInitialized = useRef(false);

  // Preload critical resources with safety checks
  useEffect(() => {
    if (hasInitialized.current || typeof window === 'undefined') return;
    hasInitialized.current = true;

    try {
      // Preload critical resources immediately
      criticalResources.forEach(resource => {
        try {
          const link = document.createElement('link');
          link.rel = 'preload';
          link.href = resource;

          if (resource.endsWith('.js')) {
            link.as = 'script';
          } else if (resource.endsWith('.css')) {
            link.as = 'style';
          } else if (resource.match(/\.(jpg|jpeg|png|webp|avif)$/)) {
            link.as = 'image';
          } else if (resource.match(/\.(woff|woff2|ttf|otf)$/)) {
            link.as = 'font';
            link.crossOrigin = 'anonymous';
          }

          document.head.appendChild(link);
        } catch (error) {
          console.warn('[PerformanceOptimizer] Failed to preload resource:', resource, error);
        }
      });

      // Preload non-critical resources on idle
      if ('requestIdleCallback' in window) {
        window.requestIdleCallback(() => {
          preloadResources.forEach(resource => {
            try {
              const link = document.createElement('link');
              link.rel = 'prefetch';
              link.href = resource;
              document.head.appendChild(link);
            } catch (error) {
              console.warn('[PerformanceOptimizer] Failed to prefetch resource:', resource, error);
            }
          });
        });
      }
    } catch (error) {
      console.warn('[PerformanceOptimizer] Resource preloading failed:', error);
    }
  }, [criticalResources, preloadResources]);

  // Resource hints for better performance with safety checks
  useEffect(() => {
    if (typeof window === 'undefined') return;

    try {
      // DNS prefetch for external domains
      const externalDomains = [
        ...preloadResources,
        ...criticalResources
      ].filter(url => url.startsWith('http'))
        .map(url => {
          try {
            return new URL(url).hostname;
          } catch {
            return null;
          }
        })
        .filter((hostname): hostname is string => hostname !== null)
        .filter((hostname, index, arr) => arr.indexOf(hostname) === index);

      externalDomains.forEach(domain => {
        try {
          const link = document.createElement('link');
          link.rel = 'dns-prefetch';
          link.href = `//${domain}`;
          document.head.appendChild(link);
        } catch (error) {
          console.warn('[PerformanceOptimizer] Failed to add DNS prefetch for:', domain, error);
        }
      });
    } catch (error) {
      console.warn('[PerformanceOptimizer] DNS prefetch setup failed:', error);
    }
  }, [preloadResources, criticalResources]);

  // Cleanup on unmount with safety checks
  useEffect(() => {
    return () => {
      try {
        if (memoryCleanup && typeof memoryCleanup.cleanup === 'function') {
          memoryCleanup.cleanup();
        }
      } catch (error) {
        console.warn('[PerformanceOptimizer] Cleanup failed:', error);
      }
    };
  }, [memoryCleanup]);

  return <>{children}</>;
}

// HOC for automatic performance optimization
export function withPerformanceOptimization<P extends object>(
  Component: React.ComponentType<P>,
  options: Omit<PerformanceOptimizerProps, 'children'> = {}
) {
  const WrappedComponent = (props: P) => {
    return (
      <PerformanceOptimizer
        componentName={Component.displayName || Component.name}
        {...options}
      >
        <Component {...props} />
      </PerformanceOptimizer>
    );
  };

  WrappedComponent.displayName = `withPerformanceOptimization(${Component.displayName || Component.name})`;
  
  return WrappedComponent;
}

// Performance monitoring utilities
export const performanceUtils = {
  // Measure function execution time
  measureFunction: <T extends any[], R>(
    fn: (...args: T) => R,
    name: string
  ) => {
    return (...args: T): R => {
      const start = performance.now();
      const result = fn(...args);
      const end = performance.now();
      
      if (process.env.NODE_ENV === 'development') {
        console.log(`[Performance] ${name}: ${(end - start).toFixed(2)}ms`);
      }
      
      return result;
    };
  },

  // Measure async function execution time
  measureAsyncFunction: <T extends any[], R>(
    fn: (...args: T) => Promise<R>,
    name: string
  ) => {
    return async (...args: T): Promise<R> => {
      const start = performance.now();
      const result = await fn(...args);
      const end = performance.now();
      
      if (process.env.NODE_ENV === 'development') {
        console.log(`[Performance] ${name}: ${(end - start).toFixed(2)}ms`);
      }
      
      return result;
    };
  },

  // Debounce function for performance
  debounce: <T extends any[]>(
    fn: (...args: T) => void,
    delay: number
  ) => {
    let timeoutId: NodeJS.Timeout;
    
    return (...args: T) => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => fn(...args), delay);
    };
  },

  // Throttle function for performance
  throttle: <T extends any[]>(
    fn: (...args: T) => void,
    limit: number
  ) => {
    let inThrottle: boolean;
    
    return (...args: T) => {
      if (!inThrottle) {
        fn(...args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  },

  // Lazy load component
  lazyLoad: <P extends Record<string, any>>(
    importFn: () => Promise<{ default: React.ComponentType<P> }>,
    fallback?: ReactNode
  ) => {
    const LazyComponent = React.lazy(importFn);

    return (props: P) => (
      <React.Suspense fallback={fallback || <div>Loading...</div>}>
        <LazyComponent {...(props as any)} />
      </React.Suspense>
    );
  },

  // Preload component
  preloadComponent: (
    importFn: () => Promise<{ default: React.ComponentType<any> }>
  ) => {
    if ('requestIdleCallback' in window) {
      window.requestIdleCallback(() => {
        importFn().catch(error => {
          console.warn('Failed to preload component:', error);
        });
      });
    }
  }
};

// Export the main component as default
export default PerformanceOptimizer;

// Also export as named export for backward compatibility
export { PerformanceOptimizer };
