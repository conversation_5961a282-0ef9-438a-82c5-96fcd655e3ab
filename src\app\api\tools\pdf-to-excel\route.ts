import { NextRequest, NextResponse } from 'next/server';
import * as XLSX from 'xlsx';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }

    if (file.type !== 'application/pdf') {
      return NextResponse.json(
        { error: 'File must be a PDF document' },
        { status: 400 }
      );
    }

    // Convert file to buffer
    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);

    // Use require for pdf-parse to avoid ESM issues
    const pdfParse = require('pdf-parse');

    // Extract text from PDF
    const pdfData = await pdfParse(buffer);
    
    if (!pdfData.text || pdfData.text.trim().length === 0) {
      return NextResponse.json(
        { error: 'No text content found in PDF. The PDF might be image-based or empty.' },
        { status: 400 }
      );
    }

    // Process the extracted text into Excel format
    const excelData = processTextToExcel(pdfData.text);
    
    if (excelData.length === 0) {
      return NextResponse.json(
        { error: 'No structured data could be extracted from the PDF.' },
        { status: 400 }
      );
    }

    // Create Excel workbook
    const workbook = XLSX.utils.book_new();
    
    // Create worksheet from data
    const worksheet = XLSX.utils.aoa_to_sheet(excelData);
    
    // Add some basic formatting
    const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1');
    
    // Set column widths
    const columnWidths = [];
    for (let col = range.s.c; col <= range.e.c; col++) {
      let maxWidth = 10;
      for (let row = range.s.r; row <= range.e.r; row++) {
        const cellAddress = XLSX.utils.encode_cell({ r: row, c: col });
        const cell = worksheet[cellAddress];
        if (cell && cell.v) {
          const cellLength = cell.v.toString().length;
          maxWidth = Math.max(maxWidth, Math.min(cellLength, 50));
        }
      }
      columnWidths.push({ wch: maxWidth });
    }
    worksheet['!cols'] = columnWidths;
    
    // Add worksheet to workbook
    XLSX.utils.book_append_sheet(workbook, worksheet, 'PDF Data');
    
    // Generate Excel file buffer
    const excelBuffer = XLSX.write(workbook, { 
      type: 'buffer', 
      bookType: 'xlsx',
      compression: true 
    });

    // Generate filename
    const originalName = file.name.replace(/\.pdf$/i, '');
    const excelFilename = `${originalName}_converted.xlsx`;

    // Create response with Excel file
    const response = new NextResponse(excelBuffer, {
      status: 200,
      headers: {
        'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'Content-Disposition': `attachment; filename="${excelFilename}"`,
        'X-Original-Size': arrayBuffer.byteLength.toString(),
        'X-Extracted-Characters': pdfData.text.length.toString(),
        'X-Excel-Rows': excelData.length.toString(),
        'X-Excel-Columns': (excelData[0]?.length || 0).toString(),
      },
    });

    return response;

  } catch (error) {
    console.error('PDF to Excel conversion error:', error);
    
    if (error instanceof Error) {
      if (error.message.includes('Invalid PDF')) {
        return NextResponse.json(
          { error: 'Invalid PDF file. Please ensure the file is not corrupted.' },
          { status: 400 }
        );
      }
      if (error.message.includes('password')) {
        return NextResponse.json(
          { error: 'Password-protected PDF files are not supported.' },
          { status: 400 }
        );
      }
    }

    return NextResponse.json(
      { error: 'Failed to convert PDF to Excel. Please ensure the file contains extractable text.' },
      { status: 500 }
    );
  }
}

// Helper function to process extracted text into Excel-compatible format
function processTextToExcel(text: string): string[][] {
  const lines = text.split('\n').map(line => line.trim()).filter(line => line.length > 0);
  
  if (lines.length === 0) {
    return [];
  }

  const excelData: string[][] = [];
  
  // Add header row
  excelData.push(['Content Type', 'Text Content', 'Line Number']);
  
  // Process each line
  lines.forEach((line, index) => {
    // Try to detect if line contains tabular data
    if (line.includes('\t')) {
      // Tab-separated data
      const columns = line.split('\t').map(col => col.trim());
      excelData.push(['Tabular Data', ...columns]);
    } else if (line.match(/^\s*\d+[\.\)]/)) {
      // Numbered list item
      excelData.push(['Numbered List', line, (index + 1).toString()]);
    } else if (line.match(/^\s*[\-\*\•]/)) {
      // Bullet point
      excelData.push(['Bullet Point', line, (index + 1).toString()]);
    } else if (line.length > 100) {
      // Long paragraph - split into chunks
      const chunks = splitTextIntoChunks(line, 100);
      chunks.forEach((chunk, chunkIndex) => {
        excelData.push(['Paragraph', chunk, `${index + 1}.${chunkIndex + 1}`]);
      });
    } else if (line.match(/^[A-Z\s]+$/) && line.length > 5) {
      // Potential heading (all caps)
      excelData.push(['Heading', line, (index + 1).toString()]);
    } else {
      // Regular text
      excelData.push(['Text', line, (index + 1).toString()]);
    }
  });

  // If we have very few rows, add some summary information
  if (excelData.length < 5) {
    excelData.push(['', '', '']);
    excelData.push(['Summary', 'Total Lines Processed', lines.length.toString()]);
    excelData.push(['Summary', 'Total Characters', text.length.toString()]);
    excelData.push(['Summary', 'Conversion Date', new Date().toISOString()]);
  }

  return excelData;
}

// Helper function to split long text into chunks
function splitTextIntoChunks(text: string, maxLength: number): string[] {
  const chunks: string[] = [];
  const words = text.split(' ');
  let currentChunk = '';

  for (const word of words) {
    if ((currentChunk + ' ' + word).length <= maxLength) {
      currentChunk = currentChunk ? currentChunk + ' ' + word : word;
    } else {
      if (currentChunk) {
        chunks.push(currentChunk);
      }
      currentChunk = word;
    }
  }

  if (currentChunk) {
    chunks.push(currentChunk);
  }

  return chunks;
}

export async function GET() {
  return NextResponse.json(
    { 
      message: 'PDF to Excel Conversion API',
      supportedInput: 'PDF',
      outputFormat: 'XLSX',
      maxFileSize: '10MB',
      note: 'Extracts text from PDF and structures it into Excel format. Works best with text-based PDFs.'
    },
    { status: 200 }
  );
}
