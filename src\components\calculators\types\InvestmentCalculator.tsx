"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

interface InvestmentResult {
  futureValue: number;
  totalContributions: number;
  totalInterest: number;
  monthlyBreakdown: Array<{
    month: number;
    contribution: number;
    interest: number;
    balance: number;
  }>;
}

export default function InvestmentCalculator() {
  const [initialAmount, setInitialAmount] = useState<number>(10000);
  const [monthlyContribution, setMonthlyContribution] = useState<number>(500);
  const [annualReturn, setAnnualReturn] = useState<number>(7);
  const [years, setYears] = useState<number>(10);
  const [compoundingFrequency, setCompoundingFrequency] = useState<string>("monthly");
  const [result, setResult] = useState<InvestmentResult | null>(null);

  const getCompoundingPeriodsPerYear = (frequency: string): number => {
    switch (frequency) {
      case "annually": return 1;
      case "semiannually": return 2;
      case "quarterly": return 4;
      case "monthly": return 12;
      case "weekly": return 52;
      case "daily": return 365;
      default: return 12;
    }
  };

  const calculateInvestment = () => {
    const periodsPerYear = getCompoundingPeriodsPerYear(compoundingFrequency);
    const totalPeriods = years * periodsPerYear;
    const periodRate = (annualReturn / 100) / periodsPerYear;
    const contributionPerPeriod = monthlyContribution * (12 / periodsPerYear);

    let balance = initialAmount;
    let totalContributions = initialAmount;
    const monthlyBreakdown: Array<{
      month: number;
      contribution: number;
      interest: number;
      balance: number;
    }> = [];

    // Calculate for each period
    for (let period = 1; period <= totalPeriods; period++) {
      const interestEarned = balance * periodRate;
      balance += interestEarned + contributionPerPeriod;
      totalContributions += contributionPerPeriod;

      // Store monthly data (convert periods to months for display)
      if (period % (periodsPerYear / 12) === 0 || periodsPerYear === 12) {
        const month = Math.ceil(period / (periodsPerYear / 12));
        monthlyBreakdown.push({
          month,
          contribution: contributionPerPeriod * (periodsPerYear / 12),
          interest: interestEarned * (periodsPerYear / 12),
          balance
        });
      }
    }

    const futureValue = balance;
    const totalInterest = futureValue - totalContributions;

    setResult({
      futureValue,
      totalContributions,
      totalInterest,
      monthlyBreakdown: monthlyBreakdown.slice(-12) // Show last 12 months
    });
  };

  const reset = () => {
    setInitialAmount(10000);
    setMonthlyContribution(500);
    setAnnualReturn(7);
    setYears(10);
    setCompoundingFrequency("monthly");
    setResult(null);
  };

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount);
  };

  const getReturnOnInvestment = (): number => {
    if (!result || result.totalContributions === 0) return 0;
    return ((result.futureValue - result.totalContributions) / result.totalContributions) * 100;
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Investment Calculator</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Input Section */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div>
                <Label htmlFor="initial-amount">Initial Investment</Label>
                <Input
                  id="initial-amount"
                  type="number"
                  value={initialAmount}
                  onChange={(e) => setInitialAmount(parseFloat(e.target.value) || 0)}
                  min="0"
                  step="100"
                />
              </div>

              <div>
                <Label htmlFor="monthly-contribution">Monthly Contribution</Label>
                <Input
                  id="monthly-contribution"
                  type="number"
                  value={monthlyContribution}
                  onChange={(e) => setMonthlyContribution(parseFloat(e.target.value) || 0)}
                  min="0"
                  step="50"
                />
              </div>

              <div>
                <Label htmlFor="annual-return">Expected Annual Return (%)</Label>
                <Input
                  id="annual-return"
                  type="number"
                  value={annualReturn}
                  onChange={(e) => setAnnualReturn(parseFloat(e.target.value) || 0)}
                  min="0"
                  max="50"
                  step="0.1"
                />
              </div>
            </div>

            <div className="space-y-4">
              <div>
                <Label htmlFor="years">Investment Period (Years)</Label>
                <Input
                  id="years"
                  type="number"
                  value={years}
                  onChange={(e) => setYears(parseInt(e.target.value) || 0)}
                  min="1"
                  max="50"
                />
              </div>

              <div>
                <Label htmlFor="compounding">Compounding Frequency</Label>
                <Select value={compoundingFrequency} onValueChange={setCompoundingFrequency}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="annually">Annually</SelectItem>
                    <SelectItem value="semiannually">Semi-annually</SelectItem>
                    <SelectItem value="quarterly">Quarterly</SelectItem>
                    <SelectItem value="monthly">Monthly</SelectItem>
                    <SelectItem value="weekly">Weekly</SelectItem>
                    <SelectItem value="daily">Daily</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <h3 className="font-semibold mb-2">Investment Summary</h3>
                <div className="space-y-1 text-sm">
                  <div className="flex justify-between">
                    <span>Initial:</span>
                    <span>{formatCurrency(initialAmount)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Monthly:</span>
                    <span>{formatCurrency(monthlyContribution)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Annual Return:</span>
                    <span>{annualReturn}%</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Duration:</span>
                    <span>{years} years</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Calculate Button */}
          <div className="flex gap-4">
            <Button onClick={calculateInvestment} className="flex-1">
              Calculate Investment Growth
            </Button>
            <Button onClick={reset} variant="outline">
              Reset
            </Button>
          </div>

          {/* Results */}
          {result && (
            <div className="space-y-6">
              {/* Summary Cards */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card className="bg-green-50 dark:bg-green-900/20">
                  <CardContent className="pt-6">
                    <div className="text-center space-y-2">
                      <div className="text-sm text-muted-foreground">Future Value</div>
                      <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                        {formatCurrency(result.futureValue)}
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-blue-50 dark:bg-blue-900/20">
                  <CardContent className="pt-6">
                    <div className="text-center space-y-2">
                      <div className="text-sm text-muted-foreground">Total Contributions</div>
                      <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                        {formatCurrency(result.totalContributions)}
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-purple-50 dark:bg-purple-900/20">
                  <CardContent className="pt-6">
                    <div className="text-center space-y-2">
                      <div className="text-sm text-muted-foreground">Total Interest</div>
                      <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                        {formatCurrency(result.totalInterest)}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Additional Metrics */}
              <Card>
                <CardContent className="pt-6">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
                    <div>
                      <div className="text-sm text-muted-foreground">Return on Investment</div>
                      <div className="text-xl font-semibold">
                        {getReturnOnInvestment().toFixed(2)}%
                      </div>
                    </div>
                    <div>
                      <div className="text-sm text-muted-foreground">Interest vs Contributions</div>
                      <div className="text-xl font-semibold">
                        {((result.totalInterest / result.totalContributions) * 100).toFixed(1)}%
                      </div>
                    </div>
                    <div>
                      <div className="text-sm text-muted-foreground">Monthly Growth Rate</div>
                      <div className="text-xl font-semibold">
                        {(annualReturn / 12).toFixed(2)}%
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Breakdown Table */}
              <Card>
                <CardHeader>
                  <CardTitle>Final Year Breakdown</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="overflow-x-auto">
                    <table className="w-full text-sm">
                      <thead>
                        <tr className="border-b">
                          <th className="text-left p-2">Month</th>
                          <th className="text-right p-2">Contribution</th>
                          <th className="text-right p-2">Interest</th>
                          <th className="text-right p-2">Balance</th>
                        </tr>
                      </thead>
                      <tbody>
                        {result.monthlyBreakdown.map((month, index) => (
                          <tr key={index} className="border-b">
                            <td className="p-2">{month.month}</td>
                            <td className="text-right p-2">{formatCurrency(month.contribution)}</td>
                            <td className="text-right p-2">{formatCurrency(month.interest)}</td>
                            <td className="text-right p-2 font-semibold">{formatCurrency(month.balance)}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* Instructions */}
          <div className="p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
            <h3 className="font-semibold mb-2">Investment Tips</h3>
            <ul className="text-sm space-y-1">
              <li>• Higher compounding frequency generally yields better returns</li>
              <li>• Consistent monthly contributions can significantly boost growth</li>
              <li>• Consider inflation when setting expected return rates</li>
              <li>• Diversify investments to manage risk</li>
              <li>• Start early to maximize compound interest benefits</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
