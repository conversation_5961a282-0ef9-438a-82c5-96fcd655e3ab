"use client";

import { useState, useEffect, useCallback } from "react";
import { motion } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { Save, RotateCcw, Layout, Navigation, Sidebar, Menu } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface LayoutSettings {
  // Header settings
  headerHeight: string;
  headerSticky: boolean;
  headerBackground: string;
  headerTextColor: string;
  showLogo: boolean;
  logoPosition: "left" | "center" | "right";
  
  // Navigation settings
  navStyle: "horizontal" | "vertical" | "dropdown";
  navPosition: "top" | "bottom" | "left" | "right";
  showSearchInNav: boolean;
  showUserMenuInNav: boolean;
  
  // Sidebar settings
  sidebarWidth: string;
  sidebarCollapsible: boolean;
  sidebarPosition: "left" | "right";
  sidebarBackground: string;
  
  // Footer settings
  footerHeight: string;
  footerBackground: string;
  footerTextColor: string;
  showFooter: boolean;
  footerContent: string;
  
  // Layout settings
  containerMaxWidth: string;
  contentPadding: string;
  borderRadius: string;
  boxShadow: boolean;
}

const defaultLayout: LayoutSettings = {
  headerHeight: "80px",
  headerSticky: true,
  headerBackground: "#ffffff",
  headerTextColor: "#1f2937",
  showLogo: true,
  logoPosition: "left",
  navStyle: "horizontal",
  navPosition: "top",
  showSearchInNav: true,
  showUserMenuInNav: true,
  sidebarWidth: "280px",
  sidebarCollapsible: true,
  sidebarPosition: "left",
  sidebarBackground: "#f8fafc",
  footerHeight: "120px",
  footerBackground: "#1f2937",
  footerTextColor: "#ffffff",
  showFooter: true,
  footerContent: "© 2024 ToolBox. All rights reserved.",
  containerMaxWidth: "1200px",
  contentPadding: "24px",
  borderRadius: "8px",
  boxShadow: true
};

export default function LayoutPage() {
  const [layout, setLayout] = useState<LayoutSettings>(defaultLayout);
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const { toast } = useToast();

  const loadLayout = useCallback(async () => {
    setIsLoading(true);
    try {
      const response = await fetch("/api/layout");
      if (response.ok) {
        const data = await response.json();
        setLayout({ ...defaultLayout, ...data });
      }
    } catch (error) {
      console.error("Failed to load layout:", error);
      toast({
        title: "Error",
        description: "Failed to load layout settings",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [toast]);

  // Load layout on component mount
  useEffect(() => {
    loadLayout();
  }, [loadLayout]);

  const saveLayout = async () => {
    setIsSaving(true);
    try {
      const response = await fetch("/api/layout", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(layout),
      });

      if (response.ok) {
        toast({
          title: "Success",
          description: "Layout settings saved successfully",
        });
      } else {
        throw new Error("Failed to save layout");
      }
    } catch (error) {
      console.error("Failed to save layout:", error);
      toast({
        title: "Error",
        description: "Failed to save layout settings",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  const resetLayout = () => {
    setLayout(defaultLayout);
    toast({
      title: "Layout Reset",
      description: "Layout has been reset to defaults",
    });
  };

  const handleInputChange = (field: keyof LayoutSettings, value: string | boolean) => {
    setLayout(prev => ({
      ...prev,
      [field]: value
    }));
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="flex items-center justify-between mb-8"
      >
        <div>
          <h1 className="text-3xl font-bold text-foreground flex items-center gap-2">
            <Layout className="h-8 w-8" />
            Layout Settings
          </h1>
          <p className="text-muted-foreground mt-2">
            Customize your website&rsquo;s layout and structure
          </p>
        </div>

        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={resetLayout}
            disabled={isSaving}
          >
            <RotateCcw className="h-4 w-4 mr-2" />
            Reset
          </Button>
          <Button
            onClick={saveLayout}
            disabled={isSaving}
          >
            {isSaving ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Saving...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Save Changes
              </>
            )}
          </Button>
        </div>
      </motion.div>

      {/* Layout Preview */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.1 }}
        className="mb-8"
      >
        <Card>
          <CardHeader>
            <CardTitle>Layout Preview</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="border rounded-lg overflow-hidden">
              {/* Header Preview */}
              <div 
                className="flex items-center justify-between px-4"
                style={{
                  height: layout.headerHeight,
                  backgroundColor: layout.headerBackground,
                  color: layout.headerTextColor
                }}
              >
                {layout.logoPosition === "left" && layout.showLogo && (
                  <div className="font-bold">Logo</div>
                )}
                {layout.logoPosition === "center" && layout.showLogo && (
                  <div></div>
                )}
                <div className="flex items-center gap-4">
                  <span className="text-sm">Home</span>
                  <span className="text-sm">About</span>
                  <span className="text-sm">Contact</span>
                </div>
                {layout.logoPosition === "center" && layout.showLogo && (
                  <div className="absolute left-1/2 transform -translate-x-1/2 font-bold">Logo</div>
                )}
                {layout.logoPosition === "right" && layout.showLogo && (
                  <div className="font-bold">Logo</div>
                )}
              </div>

              {/* Content Area Preview */}
              <div className="flex">
                {layout.sidebarPosition === "left" && (
                  <div 
                    className="p-4 border-r"
                    style={{
                      width: layout.sidebarWidth,
                      backgroundColor: layout.sidebarBackground
                    }}
                  >
                    <div className="text-sm text-muted-foreground">Sidebar</div>
                  </div>
                )}
                
                <div 
                  className="flex-1 p-4"
                  style={{
                    maxWidth: layout.containerMaxWidth,
                    padding: layout.contentPadding
                  }}
                >
                  <div className="text-sm text-muted-foreground">Main Content Area</div>
                </div>

                {layout.sidebarPosition === "right" && (
                  <div 
                    className="p-4 border-l"
                    style={{
                      width: layout.sidebarWidth,
                      backgroundColor: layout.sidebarBackground
                    }}
                  >
                    <div className="text-sm text-muted-foreground">Sidebar</div>
                  </div>
                )}
              </div>

              {/* Footer Preview */}
              {layout.showFooter && (
                <div 
                  className="px-4 flex items-center justify-center"
                  style={{
                    height: layout.footerHeight,
                    backgroundColor: layout.footerBackground,
                    color: layout.footerTextColor
                  }}
                >
                  <div className="text-sm">{layout.footerContent}</div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Layout Settings Tabs */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        <Tabs defaultValue="header" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="header" className="flex items-center gap-2">
              <Menu className="h-4 w-4" />
              Header
            </TabsTrigger>
            <TabsTrigger value="navigation" className="flex items-center gap-2">
              <Navigation className="h-4 w-4" />
              Navigation
            </TabsTrigger>
            <TabsTrigger value="sidebar" className="flex items-center gap-2">
              <Sidebar className="h-4 w-4" />
              Sidebar
            </TabsTrigger>
            <TabsTrigger value="layout" className="flex items-center gap-2">
              <Layout className="h-4 w-4" />
              Layout
            </TabsTrigger>
          </TabsList>

          {/* Header Tab */}
          <TabsContent value="header" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Header Settings</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="headerHeight">Header Height</Label>
                    <Input
                      id="headerHeight"
                      value={layout.headerHeight}
                      onChange={(e) => handleInputChange('headerHeight', e.target.value)}
                      placeholder="80px"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="headerBackground">Background Color</Label>
                    <div className="flex gap-2">
                      <Input
                        type="color"
                        value={layout.headerBackground}
                        onChange={(e) => handleInputChange('headerBackground', e.target.value)}
                        className="w-16 h-10 p-1"
                      />
                      <Input
                        value={layout.headerBackground}
                        onChange={(e) => handleInputChange('headerBackground', e.target.value)}
                        placeholder="#ffffff"
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="headerTextColor">Text Color</Label>
                    <div className="flex gap-2">
                      <Input
                        type="color"
                        value={layout.headerTextColor}
                        onChange={(e) => handleInputChange('headerTextColor', e.target.value)}
                        className="w-16 h-10 p-1"
                      />
                      <Input
                        value={layout.headerTextColor}
                        onChange={(e) => handleInputChange('headerTextColor', e.target.value)}
                        placeholder="#1f2937"
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Logo Position</Label>
                    <select
                      value={layout.logoPosition}
                      onChange={(e) => handleInputChange('logoPosition', e.target.value as any)}
                      className="w-full p-2 border rounded-md"
                    >
                      <option value="left">Left</option>
                      <option value="center">Center</option>
                      <option value="right">Right</option>
                    </select>
                  </div>
                </div>

                <Separator />

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>Sticky Header</Label>
                      <p className="text-xs text-muted-foreground">
                        Keep header visible when scrolling
                      </p>
                    </div>
                    <Switch
                      checked={layout.headerSticky}
                      onCheckedChange={(checked) => handleInputChange('headerSticky', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>Show Logo</Label>
                      <p className="text-xs text-muted-foreground">
                        Display logo in header
                      </p>
                    </div>
                    <Switch
                      checked={layout.showLogo}
                      onCheckedChange={(checked) => handleInputChange('showLogo', checked)}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Navigation Tab */}
          <TabsContent value="navigation" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Navigation Settings</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Navigation Style</Label>
                    <select
                      value={layout.navStyle}
                      onChange={(e) => handleInputChange('navStyle', e.target.value as any)}
                      className="w-full p-2 border rounded-md"
                    >
                      <option value="horizontal">Horizontal</option>
                      <option value="vertical">Vertical</option>
                      <option value="dropdown">Dropdown</option>
                    </select>
                  </div>

                  <div className="space-y-2">
                    <Label>Navigation Position</Label>
                    <select
                      value={layout.navPosition}
                      onChange={(e) => handleInputChange('navPosition', e.target.value as any)}
                      className="w-full p-2 border rounded-md"
                    >
                      <option value="top">Top</option>
                      <option value="bottom">Bottom</option>
                      <option value="left">Left</option>
                      <option value="right">Right</option>
                    </select>
                  </div>
                </div>

                <Separator />

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>Show Search in Navigation</Label>
                      <p className="text-xs text-muted-foreground">
                        Include search box in navigation
                      </p>
                    </div>
                    <Switch
                      checked={layout.showSearchInNav}
                      onCheckedChange={(checked) => handleInputChange('showSearchInNav', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>Show User Menu in Navigation</Label>
                      <p className="text-xs text-muted-foreground">
                        Include user menu in navigation
                      </p>
                    </div>
                    <Switch
                      checked={layout.showUserMenuInNav}
                      onCheckedChange={(checked) => handleInputChange('showUserMenuInNav', checked)}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Sidebar Tab */}
          <TabsContent value="sidebar" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Sidebar Settings</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="sidebarWidth">Sidebar Width</Label>
                    <Input
                      id="sidebarWidth"
                      value={layout.sidebarWidth}
                      onChange={(e) => handleInputChange('sidebarWidth', e.target.value)}
                      placeholder="280px"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label>Sidebar Position</Label>
                    <select
                      value={layout.sidebarPosition}
                      onChange={(e) => handleInputChange('sidebarPosition', e.target.value as any)}
                      className="w-full p-2 border rounded-md"
                    >
                      <option value="left">Left</option>
                      <option value="right">Right</option>
                    </select>
                  </div>

                  <div className="space-y-2 md:col-span-2">
                    <Label htmlFor="sidebarBackground">Background Color</Label>
                    <div className="flex gap-2">
                      <Input
                        type="color"
                        value={layout.sidebarBackground}
                        onChange={(e) => handleInputChange('sidebarBackground', e.target.value)}
                        className="w-16 h-10 p-1"
                      />
                      <Input
                        value={layout.sidebarBackground}
                        onChange={(e) => handleInputChange('sidebarBackground', e.target.value)}
                        placeholder="#f8fafc"
                      />
                    </div>
                  </div>
                </div>

                <Separator />

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Collapsible Sidebar</Label>
                    <p className="text-xs text-muted-foreground">
                      Allow sidebar to be collapsed
                    </p>
                  </div>
                  <Switch
                    checked={layout.sidebarCollapsible}
                    onCheckedChange={(checked) => handleInputChange('sidebarCollapsible', checked)}
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Layout Tab */}
          <TabsContent value="layout" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>General Layout</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="containerMaxWidth">Container Max Width</Label>
                    <Input
                      id="containerMaxWidth"
                      value={layout.containerMaxWidth}
                      onChange={(e) => handleInputChange('containerMaxWidth', e.target.value)}
                      placeholder="1200px"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="contentPadding">Content Padding</Label>
                    <Input
                      id="contentPadding"
                      value={layout.contentPadding}
                      onChange={(e) => handleInputChange('contentPadding', e.target.value)}
                      placeholder="24px"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="borderRadius">Border Radius</Label>
                    <Input
                      id="borderRadius"
                      value={layout.borderRadius}
                      onChange={(e) => handleInputChange('borderRadius', e.target.value)}
                      placeholder="8px"
                    />
                  </div>
                </div>

                <Separator />

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Box Shadow</Label>
                    <p className="text-xs text-muted-foreground">
                      Add subtle shadows to elements
                    </p>
                  </div>
                  <Switch
                    checked={layout.boxShadow}
                    onCheckedChange={(checked) => handleInputChange('boxShadow', checked)}
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Footer Settings</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between mb-4">
                  <div className="space-y-0.5">
                    <Label>Show Footer</Label>
                    <p className="text-xs text-muted-foreground">
                      Display footer on all pages
                    </p>
                  </div>
                  <Switch
                    checked={layout.showFooter}
                    onCheckedChange={(checked) => handleInputChange('showFooter', checked)}
                  />
                </div>

                {layout.showFooter && (
                  <>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="footerHeight">Footer Height</Label>
                        <Input
                          id="footerHeight"
                          value={layout.footerHeight}
                          onChange={(e) => handleInputChange('footerHeight', e.target.value)}
                          placeholder="120px"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="footerBackground">Background Color</Label>
                        <div className="flex gap-2">
                          <Input
                            type="color"
                            value={layout.footerBackground}
                            onChange={(e) => handleInputChange('footerBackground', e.target.value)}
                            className="w-16 h-10 p-1"
                          />
                          <Input
                            value={layout.footerBackground}
                            onChange={(e) => handleInputChange('footerBackground', e.target.value)}
                            placeholder="#1f2937"
                          />
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="footerTextColor">Text Color</Label>
                        <div className="flex gap-2">
                          <Input
                            type="color"
                            value={layout.footerTextColor}
                            onChange={(e) => handleInputChange('footerTextColor', e.target.value)}
                            className="w-16 h-10 p-1"
                          />
                          <Input
                            value={layout.footerTextColor}
                            onChange={(e) => handleInputChange('footerTextColor', e.target.value)}
                            placeholder="#ffffff"
                          />
                        </div>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="footerContent">Footer Content</Label>
                      <Textarea
                        id="footerContent"
                        value={layout.footerContent}
                        onChange={(e) => handleInputChange('footerContent', e.target.value)}
                        placeholder="Footer text content"
                        rows={3}
                      />
                    </div>
                  </>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </motion.div>
    </div>
  );
}
