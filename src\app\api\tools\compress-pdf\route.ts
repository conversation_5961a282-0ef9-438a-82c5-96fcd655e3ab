import { NextRequest, NextResponse } from 'next/server';
import { PDFDocument } from 'pdf-lib';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const compressionLevel = formData.get('compressionLevel') as string || 'medium';

    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }

    if (file.type !== 'application/pdf') {
      return NextResponse.json(
        { error: 'File must be a PDF' },
        { status: 400 }
      );
    }

    // Convert file to array buffer
    const arrayBuffer = await file.arrayBuffer();
    
    // Load the PDF document
    const pdfDoc = await PDFDocument.load(arrayBuffer);
    
    // Configure compression settings based on level
    let compressionOptions;
    switch (compressionLevel) {
      case 'low':
        compressionOptions = {
          useObjectStreams: false,
          addDefaultPage: false,
          objectsPerTick: 20,
          updateFieldAppearances: false,
        };
        break;
      case 'medium':
        compressionOptions = {
          useObjectStreams: true,
          addDefaultPage: false,
          objectsPerTick: 50,
          updateFieldAppearances: false,
        };
        break;
      case 'high':
        compressionOptions = {
          useObjectStreams: true,
          addDefaultPage: false,
          objectsPerTick: 100,
          updateFieldAppearances: false,
        };
        break;
      default:
        compressionOptions = {
          useObjectStreams: true,
          addDefaultPage: false,
          objectsPerTick: 50,
          updateFieldAppearances: false,
        };
    }

    // Save the compressed PDF
    const compressedPdfBytes = await pdfDoc.save(compressionOptions);
    
    // Calculate compression ratio
    const originalSize = arrayBuffer.byteLength;
    const compressedSize = compressedPdfBytes.length;
    const compressionRatio = ((originalSize - compressedSize) / originalSize * 100).toFixed(1);

    // Create response with compressed PDF
    const response = new NextResponse(compressedPdfBytes, {
      status: 200,
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `attachment; filename="${file.name.replace('.pdf', '_compressed.pdf')}"`,
        'X-Original-Size': originalSize.toString(),
        'X-Compressed-Size': compressedSize.toString(),
        'X-Compression-Ratio': compressionRatio,
      },
    });

    return response;

  } catch (error) {
    console.error('PDF compression error:', error);
    return NextResponse.json(
      { error: 'Failed to compress PDF. Please ensure the file is a valid PDF document.' },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json(
    { 
      message: 'PDF Compression API',
      supportedLevels: ['low', 'medium', 'high'],
      maxFileSize: '10MB'
    },
    { status: 200 }
  );
}
