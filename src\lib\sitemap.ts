// Sitemap generation utilities for ToolRapter

interface SitemapUrl {
  loc: string;
  lastmod?: string;
  changefreq?: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never';
  priority?: number;
}

interface SitemapConfig {
  baseUrl: string;
  defaultChangefreq: SitemapUrl['changefreq'];
  defaultPriority: number;
}

const defaultConfig: SitemapConfig = {
  baseUrl: 'https://toolrapter.com',
  defaultChangefreq: 'weekly',
  defaultPriority: 0.5
};

// Tool configurations for sitemap generation
const TOOL_CONFIGS = [
  { slug: 'excel-to-pdf', name: 'Excel to PDF Converter', priority: 0.9 },
  { slug: 'word-to-pdf', name: 'Word to PDF Converter', priority: 0.9 },
  { slug: 'pdf-to-word', name: 'PDF to Word Converter', priority: 0.9 },
  { slug: 'pdf-to-excel', name: 'PDF to Excel Converter', priority: 0.9 },
  { slug: 'powerpoint-to-pdf', name: 'PowerPoint to PDF Converter', priority: 0.8 },
  { slug: 'pdf-to-powerpoint', name: 'PDF to PowerPoint Converter', priority: 0.8 },
  { slug: 'jpg-to-pdf', name: 'JPG to PDF Converter', priority: 0.8 },
  { slug: 'pdf-to-jpg', name: 'PDF to JPG Converter', priority: 0.8 },
  { slug: 'png-to-pdf', name: 'PNG to PDF Converter', priority: 0.7 },
  { slug: 'html-to-pdf', name: 'HTML to PDF Converter', priority: 0.7 },
  { slug: 'merge-pdf', name: 'Merge PDF Files', priority: 0.9 },
  { slug: 'split-pdf', name: 'Split PDF Files', priority: 0.8 },
  { slug: 'compress-pdf', name: 'Compress PDF Files', priority: 0.8 },
  { slug: 'rotate-pdf', name: 'Rotate PDF Pages', priority: 0.6 },
  { slug: 'add-watermark', name: 'Add Watermark to PDF', priority: 0.6 },
  { slug: 'protect-pdf', name: 'Password Protect PDF', priority: 0.7 },
  { slug: 'pdf-to-pdfa', name: 'Convert PDF to PDF/A', priority: 0.6 }
];

// Static pages configuration
const STATIC_PAGES = [
  { path: '/', priority: 1.0, changefreq: 'daily' as const },
  { path: '/tools', priority: 0.9, changefreq: 'weekly' as const },
  { path: '/about', priority: 0.6, changefreq: 'monthly' as const },
  { path: '/contact', priority: 0.6, changefreq: 'monthly' as const },
  { path: '/privacy', priority: 0.4, changefreq: 'yearly' as const },
  { path: '/terms', priority: 0.4, changefreq: 'yearly' as const },
  { path: '/blog', priority: 0.7, changefreq: 'weekly' as const }
];

/**
 * Generate sitemap URLs for all tools
 */
export function generateToolUrls(config: Partial<SitemapConfig> = {}): SitemapUrl[] {
  const { baseUrl, defaultChangefreq, defaultPriority } = { ...defaultConfig, ...config };
  
  return TOOL_CONFIGS.map(tool => ({
    loc: `${baseUrl}/tools/${tool.slug}`,
    lastmod: new Date().toISOString().split('T')[0],
    changefreq: defaultChangefreq,
    priority: tool.priority
  }));
}

/**
 * Generate sitemap URLs for static pages
 */
export function generateStaticUrls(config: Partial<SitemapConfig> = {}): SitemapUrl[] {
  const { baseUrl } = { ...defaultConfig, ...config };
  
  return STATIC_PAGES.map(page => ({
    loc: `${baseUrl}${page.path}`,
    lastmod: new Date().toISOString().split('T')[0],
    changefreq: page.changefreq,
    priority: page.priority
  }));
}

/**
 * Generate sitemap URLs for blog posts
 */
export function generateBlogUrls(
  posts: Array<{ slug: string; publishDate: string; modifiedDate?: string }>,
  config: Partial<SitemapConfig> = {}
): SitemapUrl[] {
  const { baseUrl } = { ...defaultConfig, ...config };
  
  return posts.map(post => ({
    loc: `${baseUrl}/blog/${post.slug}`,
    lastmod: post.modifiedDate || post.publishDate,
    changefreq: 'monthly' as const,
    priority: 0.6
  }));
}

/**
 * Generate complete sitemap XML
 */
export function generateSitemapXML(urls: SitemapUrl[]): string {
  const urlElements = urls.map(url => {
    let urlXml = `  <url>\n    <loc>${url.loc}</loc>\n`;
    
    if (url.lastmod) {
      urlXml += `    <lastmod>${url.lastmod}</lastmod>\n`;
    }
    
    if (url.changefreq) {
      urlXml += `    <changefreq>${url.changefreq}</changefreq>\n`;
    }
    
    if (url.priority !== undefined) {
      urlXml += `    <priority>${url.priority.toFixed(1)}</priority>\n`;
    }
    
    urlXml += `  </url>`;
    return urlXml;
  }).join('\n');

  return `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${urlElements}
</urlset>`;
}

/**
 * Generate robots.txt content
 */
export function generateRobotsTxt(config: Partial<SitemapConfig> = {}): string {
  const { baseUrl } = { ...defaultConfig, ...config };
  
  return `User-agent: *
Allow: /

# Disallow admin and private areas
Disallow: /admin/
Disallow: /api/
Disallow: /_next/
Disallow: /private/

# Allow specific API endpoints for SEO
Allow: /api/sitemap
Allow: /api/robots

# Sitemap location
Sitemap: ${baseUrl}/sitemap.xml

# Crawl delay (optional)
Crawl-delay: 1`;
}

/**
 * Generate sitemap index for large sites
 */
export function generateSitemapIndex(sitemaps: Array<{ loc: string; lastmod?: string }>): string {
  const sitemapElements = sitemaps.map(sitemap => {
    let sitemapXml = `  <sitemap>\n    <loc>${sitemap.loc}</loc>\n`;
    
    if (sitemap.lastmod) {
      sitemapXml += `    <lastmod>${sitemap.lastmod}</lastmod>\n`;
    }
    
    sitemapXml += `  </sitemap>`;
    return sitemapXml;
  }).join('\n');

  return `<?xml version="1.0" encoding="UTF-8"?>
<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${sitemapElements}
</sitemapindex>`;
}

/**
 * Generate complete sitemap for ToolRapter
 */
export function generateCompleteSitemap(
  blogPosts: Array<{ slug: string; publishDate: string; modifiedDate?: string }> = [],
  config: Partial<SitemapConfig> = {}
): string {
  const staticUrls = generateStaticUrls(config);
  const toolUrls = generateToolUrls(config);
  const blogUrls = generateBlogUrls(blogPosts, config);
  
  const allUrls = [...staticUrls, ...toolUrls, ...blogUrls];
  
  // Sort by priority (descending) and then by URL
  allUrls.sort((a, b) => {
    if (a.priority !== b.priority) {
      return (b.priority || 0) - (a.priority || 0);
    }
    return a.loc.localeCompare(b.loc);
  });
  
  return generateSitemapXML(allUrls);
}

/**
 * Get tool metadata for SEO
 */
export function getToolMetadata(slug: string) {
  const tool = TOOL_CONFIGS.find(t => t.slug === slug);
  if (!tool) return null;
  
  return {
    name: tool.name,
    slug: tool.slug,
    priority: tool.priority,
    url: `https://toolrapter.com/tools/${tool.slug}`,
    category: getToolCategory(tool.slug)
  };
}

/**
 * Get tool category for organization
 */
function getToolCategory(slug: string): string {
  if (slug.includes('excel') || slug.includes('word') || slug.includes('powerpoint')) {
    return 'Office to PDF';
  }
  if (slug.includes('pdf-to-')) {
    return 'PDF to Office';
  }
  if (slug.includes('jpg') || slug.includes('png') || slug.includes('html')) {
    return 'Other to PDF';
  }
  if (slug.includes('merge') || slug.includes('split') || slug.includes('compress')) {
    return 'PDF Manipulation';
  }
  if (slug.includes('rotate') || slug.includes('watermark') || slug.includes('protect')) {
    return 'PDF Enhancement';
  }
  return 'PDF Tools';
}

/**
 * Generate structured data for tools listing page
 */
export function generateToolsListingStructuredData() {
  return {
    '@context': 'https://schema.org',
    '@type': 'ItemList',
    name: 'PDF Conversion Tools',
    description: 'Complete list of free online PDF conversion and manipulation tools',
    numberOfItems: TOOL_CONFIGS.length,
    itemListElement: TOOL_CONFIGS.map((tool, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      item: {
        '@type': 'WebApplication',
        name: tool.name,
        url: `https://toolrapter.com/tools/${tool.slug}`,
        applicationCategory: 'ProductivityApplication',
        operatingSystem: 'Web Browser'
      }
    }))
  };
}

/**
 * Get all tool slugs for static generation
 */
export function getAllToolSlugs(): string[] {
  return TOOL_CONFIGS.map(tool => tool.slug);
}

/**
 * Get tools by category for navigation
 */
export function getToolsByCategory() {
  const categories: Record<string, typeof TOOL_CONFIGS> = {};
  
  TOOL_CONFIGS.forEach(tool => {
    const category = getToolCategory(tool.slug);
    if (!categories[category]) {
      categories[category] = [];
    }
    categories[category].push(tool);
  });
  
  return categories;
}
