#!/usr/bin/env node

/**
 * ToolRapter Performance Optimization Script
 * 
 * This script analyzes and optimizes the application for production deployment
 * with specific focus on meeting the <20s build time and <5s page load requirements.
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Performance targets
const PERFORMANCE_TARGETS = {
  BUILD_TIME_MAX: 20, // seconds
  PAGE_LOAD_MAX: 5,   // seconds
  BUNDLE_SIZE_MAX: 5, // MB
  FIRST_CONTENTFUL_PAINT_MAX: 2, // seconds
  LARGEST_CONTENTFUL_PAINT_MAX: 4, // seconds
};

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSection(title) {
  log(`\n${'='.repeat(60)}`, 'cyan');
  log(`${title}`, 'cyan');
  log(`${'='.repeat(60)}`, 'cyan');
}

function logStep(step) {
  log(`\n🔧 ${step}`, 'blue');
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

// Check if file exists
function fileExists(filePath) {
  return fs.existsSync(filePath);
}

// Read JSON file safely
function readJsonFile(filePath) {
  try {
    return JSON.parse(fs.readFileSync(filePath, 'utf8'));
  } catch (error) {
    logError(`Failed to read ${filePath}: ${error.message}`);
    return null;
  }
}

// Write JSON file safely
function writeJsonFile(filePath, data) {
  try {
    fs.writeFileSync(filePath, JSON.stringify(data, null, 2));
    return true;
  } catch (error) {
    logError(`Failed to write ${filePath}: ${error.message}`);
    return false;
  }
}

// Execute command and return result
function executeCommand(command, options = {}) {
  try {
    const result = execSync(command, { 
      encoding: 'utf8', 
      stdio: options.silent ? 'pipe' : 'inherit',
      ...options 
    });
    return { success: true, output: result };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

// Analyze package.json for optimization opportunities
function analyzePackageJson() {
  logStep('Analyzing package.json for optimization opportunities');
  
  const packageJson = readJsonFile('package.json');
  if (!packageJson) return false;

  const issues = [];
  const recommendations = [];

  // Check for unnecessary dependencies
  const unnecessaryDeps = [
    'lodash', // Use native JS or lodash-es
    'moment', // Use date-fns instead
    'axios', // Use native fetch
  ];

  Object.keys(packageJson.dependencies || {}).forEach(dep => {
    if (unnecessaryDeps.includes(dep)) {
      issues.push(`Unnecessary dependency: ${dep}`);
      recommendations.push(`Consider removing ${dep} and using alternatives`);
    }
  });

  // Check for missing performance-related scripts
  const requiredScripts = ['build', 'start', 'type-check'];
  requiredScripts.forEach(script => {
    if (!packageJson.scripts?.[script]) {
      issues.push(`Missing script: ${script}`);
    }
  });

  if (issues.length > 0) {
    logWarning(`Found ${issues.length} optimization opportunities:`);
    issues.forEach(issue => log(`  - ${issue}`, 'yellow'));
    log('\nRecommendations:', 'blue');
    recommendations.forEach(rec => log(`  - ${rec}`, 'blue'));
  } else {
    logSuccess('Package.json is optimized');
  }

  return issues.length === 0;
}

// Analyze Next.js configuration
function analyzeNextConfig() {
  logStep('Analyzing Next.js configuration');
  
  if (!fileExists('next.config.js')) {
    logError('next.config.js not found');
    return false;
  }

  const configContent = fs.readFileSync('next.config.js', 'utf8');
  const optimizations = [];

  // Check for performance optimizations
  const requiredOptimizations = [
    { check: 'compress: true', name: 'Compression' },
    { check: 'optimizePackageImports', name: 'Package import optimization' },
    { check: 'splitChunks', name: 'Bundle splitting' },
    { check: 'removeConsole', name: 'Console removal in production' },
  ];

  requiredOptimizations.forEach(opt => {
    if (configContent.includes(opt.check)) {
      optimizations.push(opt.name);
    } else {
      logWarning(`Missing optimization: ${opt.name}`);
    }
  });

  logSuccess(`Found ${optimizations.length} performance optimizations`);
  return true;
}

// Check TypeScript configuration
function checkTypeScriptConfig() {
  logStep('Checking TypeScript configuration');
  
  const tsConfig = readJsonFile('tsconfig.json');
  if (!tsConfig) return false;

  const issues = [];

  // Check for performance-related compiler options
  const recommendedOptions = {
    'strict': true,
    'skipLibCheck': true,
    'incremental': true,
  };

  Object.entries(recommendedOptions).forEach(([option, value]) => {
    if (tsConfig.compilerOptions?.[option] !== value) {
      issues.push(`TypeScript option ${option} should be ${value}`);
    }
  });

  if (issues.length > 0) {
    logWarning('TypeScript configuration issues:');
    issues.forEach(issue => log(`  - ${issue}`, 'yellow'));
  } else {
    logSuccess('TypeScript configuration is optimized');
  }

  return issues.length === 0;
}

// Run build performance test
function testBuildPerformance() {
  logStep('Testing build performance');
  
  const startTime = Date.now();
  
  log('Running production build...', 'blue');
  const buildResult = executeCommand('npm run build', { silent: false });
  
  const buildTime = (Date.now() - startTime) / 1000;
  
  if (!buildResult.success) {
    logError('Build failed');
    return false;
  }

  log(`\nBuild completed in ${buildTime.toFixed(2)} seconds`, 'blue');
  
  if (buildTime <= PERFORMANCE_TARGETS.BUILD_TIME_MAX) {
    logSuccess(`Build time target met: ${buildTime.toFixed(2)}s <= ${PERFORMANCE_TARGETS.BUILD_TIME_MAX}s`);
  } else {
    logWarning(`Build time exceeds target: ${buildTime.toFixed(2)}s > ${PERFORMANCE_TARGETS.BUILD_TIME_MAX}s`);
  }

  return buildTime <= PERFORMANCE_TARGETS.BUILD_TIME_MAX;
}

// Analyze bundle size
function analyzeBundleSize() {
  logStep('Analyzing bundle size');
  
  const nextDir = '.next';
  if (!fileExists(nextDir)) {
    logWarning('No build found. Run npm run build first.');
    return false;
  }

  // Check if bundle analyzer is available
  const hasAnalyzer = executeCommand('npm list @next/bundle-analyzer', { silent: true }).success;
  
  if (hasAnalyzer) {
    log('Running bundle analysis...', 'blue');
    executeCommand('npm run analyze', { silent: false });
  } else {
    log('Bundle analyzer not installed. Install with: npm install --save-dev @next/bundle-analyzer', 'yellow');
  }

  // Basic size check
  try {
    const staticDir = path.join(nextDir, 'static');
    if (fileExists(staticDir)) {
      const { execSync } = require('child_process');
      const sizeOutput = execSync(`du -sh ${staticDir}`, { encoding: 'utf8' });
      const size = sizeOutput.split('\t')[0];
      log(`Static assets size: ${size}`, 'blue');
    }
  } catch (error) {
    logWarning('Could not analyze bundle size');
  }

  return true;
}

// Generate performance report
function generatePerformanceReport() {
  logStep('Generating performance report');
  
  const report = {
    timestamp: new Date().toISOString(),
    targets: PERFORMANCE_TARGETS,
    checks: {
      packageJson: analyzePackageJson(),
      nextConfig: analyzeNextConfig(),
      typeScript: checkTypeScriptConfig(),
    },
    recommendations: [
      'Use dynamic imports for large components',
      'Implement proper image optimization',
      'Enable compression and caching',
      'Minimize bundle size with tree shaking',
      'Use React.memo for expensive components',
      'Implement proper error boundaries',
      'Use service workers for caching',
    ],
  };

  const reportPath = 'performance-report.json';
  if (writeJsonFile(reportPath, report)) {
    logSuccess(`Performance report saved to ${reportPath}`);
  }

  return report;
}

// Main optimization function
function main() {
  logSection('ToolRapter Performance Optimization');
  
  log('🎯 Performance Targets:', 'bright');
  Object.entries(PERFORMANCE_TARGETS).forEach(([key, value]) => {
    log(`  - ${key}: ${value}${key.includes('TIME') ? 's' : key.includes('SIZE') ? 'MB' : ''}`, 'blue');
  });

  // Run all checks
  const results = {
    packageJson: analyzePackageJson(),
    nextConfig: analyzeNextConfig(),
    typeScript: checkTypeScriptConfig(),
    bundleSize: analyzeBundleSize(),
  };

  // Generate report
  const report = generatePerformanceReport();

  // Summary
  logSection('Optimization Summary');
  
  const passedChecks = Object.values(results).filter(Boolean).length;
  const totalChecks = Object.keys(results).length;
  
  log(`Passed: ${passedChecks}/${totalChecks} checks`, passedChecks === totalChecks ? 'green' : 'yellow');
  
  if (passedChecks === totalChecks) {
    logSuccess('🎉 All performance optimizations are in place!');
  } else {
    logWarning('⚠️  Some optimizations need attention. Check the report above.');
  }

  // Optional: Run build test
  const runBuildTest = process.argv.includes('--build-test');
  if (runBuildTest) {
    testBuildPerformance();
  } else {
    log('\nTo test build performance, run: node scripts/optimize-performance.js --build-test', 'blue');
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = {
  analyzePackageJson,
  analyzeNextConfig,
  checkTypeScriptConfig,
  testBuildPerformance,
  analyzeBundleSize,
  generatePerformanceReport,
};
