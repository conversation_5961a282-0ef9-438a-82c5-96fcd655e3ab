import * as XLSX from 'xlsx';
import fs from 'fs/promises';
import path from 'path';
import { connectToDatabase } from '@/lib/mongo';

interface DownloadRecord {
  _id: string;
  userId: string;
  userEmail: string;
  userName?: string;
  fileName: string;
  conversionType: string;
  originalFileSize?: number;
  convertedFileSize?: number;
  toolName?: string;
  userAgent?: string;
  clientIP?: string;
  timestamp: Date;
  createdAt: Date;
}

interface ArchivalResult {
  success: boolean;
  recordsArchived: number;
  filePath?: string;
  error?: string;
  executionTime: number;
}

/**
 * Download Archival System
 * Exports download records older than 24 hours to Excel files and removes them from database
 */
export class DownloadArchiver {
  private archiveDirectory: string;
  private hoursOld: number;

  constructor(archiveDirectory: string = './data/archives', hoursOld: number = 24) {
    this.archiveDirectory = archiveDirectory;
    this.hoursOld = hoursOld;
  }

  /**
   * Main archival process
   * Exports old records to Excel and removes them from database
   */
  async archiveDownloads(): Promise<ArchivalResult> {
    const startTime = Date.now();
    
    try {
      console.log(`[DownloadArchiver] Starting archival process for records older than ${this.hoursOld} hours`);

      // Ensure archive directory exists
      await this.ensureArchiveDirectory();

      // Get records to archive
      const recordsToArchive = await this.getRecordsForArchival();
      
      if (recordsToArchive.length === 0) {
        console.log('[DownloadArchiver] No records to archive');
        return {
          success: true,
          recordsArchived: 0,
          executionTime: Date.now() - startTime
        };
      }

      console.log(`[DownloadArchiver] Found ${recordsToArchive.length} records to archive`);

      // Export to Excel
      const filePath = await this.exportToExcel(recordsToArchive);

      // Verify Excel file was created successfully
      await this.verifyExcelFile(filePath);

      // Remove archived records from database
      await this.removeArchivedRecords(recordsToArchive.map(r => r._id));

      const executionTime = Date.now() - startTime;
      console.log(`[DownloadArchiver] Archival completed successfully in ${executionTime}ms`);
      console.log(`[DownloadArchiver] Archived ${recordsToArchive.length} records to: ${filePath}`);

      return {
        success: true,
        recordsArchived: recordsToArchive.length,
        filePath,
        executionTime
      };

    } catch (error) {
      const executionTime = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      
      console.error('[DownloadArchiver] Archival failed:', error);
      
      return {
        success: false,
        recordsArchived: 0,
        error: errorMessage,
        executionTime
      };
    }
  }

  /**
   * Ensures the archive directory exists
   */
  private async ensureArchiveDirectory(): Promise<void> {
    try {
      await fs.access(this.archiveDirectory);
    } catch {
      await fs.mkdir(this.archiveDirectory, { recursive: true });
      console.log(`[DownloadArchiver] Created archive directory: ${this.archiveDirectory}`);
    }
  }

  /**
   * Gets download records older than specified hours
   */
  private async getRecordsForArchival(): Promise<DownloadRecord[]> {
    const { db } = await connectToDatabase();
    
    const cutoffDate = new Date();
    cutoffDate.setHours(cutoffDate.getHours() - this.hoursOld);

    const records = await db.collection('downloads')
      .find({ createdAt: { $lt: cutoffDate } })
      .sort({ createdAt: 1 })
      .toArray();

    return records.map(record => ({
      _id: record._id.toString(),
      userId: record.userId,
      userEmail: record.userEmail,
      userName: record.userName,
      fileName: record.fileName,
      conversionType: record.conversionType,
      originalFileSize: record.originalFileSize,
      convertedFileSize: record.convertedFileSize,
      toolName: record.toolName,
      userAgent: record.userAgent,
      clientIP: record.clientIP,
      timestamp: record.timestamp,
      createdAt: record.createdAt
    }));
  }

  /**
   * Exports records to Excel file
   */
  private async exportToExcel(records: DownloadRecord[]): Promise<string> {
    // Prepare data for Excel export
    const excelData = records.map(record => ({
      'Record ID': record._id,
      'User ID': record.userId,
      'User Email': record.userEmail,
      'User Name': record.userName || '',
      'File Name': record.fileName,
      'Conversion Type': record.conversionType,
      'Original File Size (bytes)': record.originalFileSize || 0,
      'Converted File Size (bytes)': record.convertedFileSize || 0,
      'Tool Name': record.toolName || '',
      'User Agent': record.userAgent || '',
      'Client IP': record.clientIP || '',
      'Download Timestamp': record.timestamp.toISOString(),
      'Record Created': record.createdAt.toISOString()
    }));

    // Create workbook and worksheet
    const workbook = XLSX.utils.book_new();
    const worksheet = XLSX.utils.json_to_sheet(excelData);

    // Auto-size columns
    const columnWidths = [
      { wch: 25 }, // Record ID
      { wch: 25 }, // User ID
      { wch: 30 }, // User Email
      { wch: 20 }, // User Name
      { wch: 40 }, // File Name
      { wch: 20 }, // Conversion Type
      { wch: 20 }, // Original File Size
      { wch: 20 }, // Converted File Size
      { wch: 25 }, // Tool Name
      { wch: 50 }, // User Agent
      { wch: 15 }, // Client IP
      { wch: 25 }, // Download Timestamp
      { wch: 25 }  // Record Created
    ];
    worksheet['!cols'] = columnWidths;

    // Add worksheet to workbook
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Download Records');

    // Generate filename with timestamp
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `user_downloads_archive_${timestamp}.xlsx`;
    const filePath = path.join(this.archiveDirectory, filename);

    // Write Excel file
    XLSX.writeFile(workbook, filePath);

    return filePath;
  }

  /**
   * Verifies that the Excel file was created successfully
   */
  private async verifyExcelFile(filePath: string): Promise<void> {
    try {
      const stats = await fs.stat(filePath);
      if (stats.size === 0) {
        throw new Error('Excel file is empty');
      }
      console.log(`[DownloadArchiver] Excel file created successfully: ${filePath} (${stats.size} bytes)`);
    } catch (error) {
      throw new Error(`Failed to verify Excel file: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Removes archived records from the database
   */
  private async removeArchivedRecords(recordIds: string[]): Promise<void> {
    const { db } = await connectToDatabase();
    
    const result = await db.collection('downloads').deleteMany({
      _id: { $in: recordIds.map(id => ({ $oid: id })) }
    });

    if (result.deletedCount !== recordIds.length) {
      console.warn(`[DownloadArchiver] Expected to delete ${recordIds.length} records, but deleted ${result.deletedCount}`);
    } else {
      console.log(`[DownloadArchiver] Successfully deleted ${result.deletedCount} records from database`);
    }
  }

  /**
   * Gets archival statistics
   */
  async getArchivalStats(): Promise<{
    totalArchiveFiles: number;
    oldestArchive: string | null;
    newestArchive: string | null;
    totalArchiveSize: number;
  }> {
    try {
      const files = await fs.readdir(this.archiveDirectory);
      const archiveFiles = files.filter(file => file.startsWith('user_downloads_archive_') && file.endsWith('.xlsx'));
      
      if (archiveFiles.length === 0) {
        return {
          totalArchiveFiles: 0,
          oldestArchive: null,
          newestArchive: null,
          totalArchiveSize: 0
        };
      }

      let totalSize = 0;
      const fileStats = await Promise.all(
        archiveFiles.map(async (file) => {
          const filePath = path.join(this.archiveDirectory, file);
          const stats = await fs.stat(filePath);
          totalSize += stats.size;
          return { file, mtime: stats.mtime };
        })
      );

      fileStats.sort((a, b) => a.mtime.getTime() - b.mtime.getTime());

      return {
        totalArchiveFiles: archiveFiles.length,
        oldestArchive: fileStats[0].file,
        newestArchive: fileStats[fileStats.length - 1].file,
        totalArchiveSize: totalSize
      };
    } catch (error) {
      console.error('[DownloadArchiver] Failed to get archival stats:', error);
      return {
        totalArchiveFiles: 0,
        oldestArchive: null,
        newestArchive: null,
        totalArchiveSize: 0
      };
    }
  }
}
