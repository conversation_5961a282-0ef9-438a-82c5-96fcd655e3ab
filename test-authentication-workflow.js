/**
 * Automated Test Script for Authentication-Protected Download Workflow
 * 
 * This script tests the complete workflow programmatically
 * Run with: node test-authentication-workflow.js
 */

const fs = require('fs');
const path = require('path');

// Test configuration
const BASE_URL = 'http://localhost:3001';
const TEST_ENDPOINTS = {
  excelToPdf: '/api/tools/excel-to-pdf',
  downloadTrack: '/api/downloads/track',
  authCheck: '/api/auth/check'
};

/**
 * Create a test Excel file for testing
 */
function createTestExcelFile() {
  const testFilePath = path.join(__dirname, 'test-excel-file.xlsx');
  
  // Create a minimal Excel file (this is a simplified version)
  // In a real test, you'd use a proper Excel library or have a pre-made file
  const excelHeader = Buffer.from([
    0x50, 0x4B, 0x03, 0x04, // ZIP file signature
    0x14, 0x00, 0x00, 0x00, 0x08, 0x00, // ZIP header
  ]);
  
  // For testing purposes, create a simple file
  const testContent = 'Test Excel Content for Authentication Workflow Testing';
  fs.writeFileSync(testFilePath, testContent);
  
  console.log(`✅ Created test file: ${testFilePath}`);
  return testFilePath;
}

/**
 * Test API endpoint availability
 */
async function testAPIEndpoints() {
  console.log('\n🔍 Testing API Endpoints...');
  
  for (const [name, endpoint] of Object.entries(TEST_ENDPOINTS)) {
    try {
      const response = await fetch(`${BASE_URL}${endpoint}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      const status = response.status;
      console.log(`  ${name}: ${endpoint} - Status: ${status} ${status < 400 ? '✅' : '❌'}`);
      
      if (endpoint === '/api/auth/check') {
        const data = await response.json();
        console.log(`    Authentication Status: ${data.isAuthenticated ? 'Logged In' : 'Not Logged In'}`);
      }
      
    } catch (error) {
      console.log(`  ${name}: ${endpoint} - Error: ${error.message} ❌`);
    }
  }
}

/**
 * Test file upload and conversion (without authentication)
 */
async function testFileUploadAndConversion() {
  console.log('\n📤 Testing File Upload and Conversion...');
  
  try {
    const testFilePath = createTestExcelFile();
    
    // Create FormData for file upload
    const FormData = require('form-data');
    const formData = new FormData();
    formData.append('file', fs.createReadStream(testFilePath), {
      filename: 'test-excel.xlsx',
      contentType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    });
    
    console.log('  Uploading file to Excel-to-PDF converter...');
    
    const response = await fetch(`${BASE_URL}${TEST_ENDPOINTS.excelToPdf}`, {
      method: 'POST',
      body: formData,
      headers: formData.getHeaders(),
    });
    
    console.log(`  Upload Response Status: ${response.status}`);
    
    if (response.ok) {
      const contentType = response.headers.get('content-type');
      console.log(`  Response Content-Type: ${contentType}`);
      
      if (contentType && contentType.includes('application/pdf')) {
        console.log('  ✅ Conversion successful - PDF generated');
        
        // Save the PDF for verification
        const pdfBuffer = await response.arrayBuffer();
        const pdfPath = path.join(__dirname, 'test-output.pdf');
        fs.writeFileSync(pdfPath, Buffer.from(pdfBuffer));
        console.log(`  ✅ PDF saved to: ${pdfPath}`);
        
        return { success: true, pdfPath, fileSize: pdfBuffer.byteLength };
      } else {
        const responseText = await response.text();
        console.log(`  ❌ Unexpected response: ${responseText}`);
        return { success: false, error: 'Unexpected response type' };
      }
    } else {
      const errorText = await response.text();
      console.log(`  ❌ Upload failed: ${errorText}`);
      return { success: false, error: errorText };
    }
    
  } catch (error) {
    console.log(`  ❌ Upload error: ${error.message}`);
    return { success: false, error: error.message };
  }
}

/**
 * Test download tracking (requires authentication)
 */
async function testDownloadTracking() {
  console.log('\n📊 Testing Download Tracking...');
  
  try {
    const response = await fetch(`${BASE_URL}${TEST_ENDPOINTS.downloadTrack}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
    console.log(`  Download Tracking API Status: ${response.status}`);
    
    if (response.status === 401) {
      console.log('  ✅ Correctly requires authentication (401 Unauthorized)');
      return { success: true, requiresAuth: true };
    } else if (response.ok) {
      const data = await response.json();
      console.log('  ✅ Download tracking data retrieved:');
      console.log(`    Total Downloads: ${data.statistics?.totalDownloads || 0}`);
      console.log(`    Recent Downloads: ${data.downloads?.length || 0}`);
      return { success: true, data };
    } else {
      const errorText = await response.text();
      console.log(`  ❌ Unexpected response: ${errorText}`);
      return { success: false, error: errorText };
    }
    
  } catch (error) {
    console.log(`  ❌ Download tracking error: ${error.message}`);
    return { success: false, error: error.message };
  }
}

/**
 * Test authentication check
 */
async function testAuthenticationCheck() {
  console.log('\n🔐 Testing Authentication Check...');
  
  try {
    const response = await fetch(`${BASE_URL}${TEST_ENDPOINTS.authCheck}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
    if (response.ok) {
      const data = await response.json();
      console.log('  Authentication Status:');
      console.log(`    Authenticated: ${data.isAuthenticated ? '✅ Yes' : '❌ No'}`);
      console.log(`    Admin: ${data.isAdmin ? '✅ Yes' : '❌ No'}`);
      
      return { success: true, isAuthenticated: data.isAuthenticated, isAdmin: data.isAdmin };
    } else {
      console.log(`  ❌ Auth check failed: ${response.status}`);
      return { success: false, error: `HTTP ${response.status}` };
    }
    
  } catch (error) {
    console.log(`  ❌ Auth check error: ${error.message}`);
    return { success: false, error: error.message };
  }
}

/**
 * Cleanup test files
 */
function cleanup() {
  console.log('\n🧹 Cleaning up test files...');
  
  const filesToClean = [
    'test-excel-file.xlsx',
    'test-output.pdf'
  ];
  
  filesToClean.forEach(file => {
    const filePath = path.join(__dirname, file);
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
      console.log(`  ✅ Deleted: ${file}`);
    }
  });
}

/**
 * Main test runner
 */
async function runTests() {
  console.log('🧪 AUTHENTICATION-PROTECTED DOWNLOAD WORKFLOW TEST');
  console.log('=' .repeat(60));
  
  try {
    // Test 1: API Endpoints
    await testAPIEndpoints();
    
    // Test 2: Authentication Check
    const authResult = await testAuthenticationCheck();
    
    // Test 3: File Upload and Conversion
    const conversionResult = await testFileUploadAndConversion();
    
    // Test 4: Download Tracking
    const trackingResult = await testDownloadTracking();
    
    // Summary
    console.log('\n📋 TEST SUMMARY');
    console.log('=' .repeat(30));
    console.log(`API Endpoints: ${authResult.success ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`File Conversion: ${conversionResult.success ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`Download Tracking: ${trackingResult.success ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`Authentication: ${authResult.isAuthenticated ? '✅ LOGGED IN' : '⚠️ NOT LOGGED IN'}`);
    
    if (!authResult.isAuthenticated) {
      console.log('\n⚠️  NOTE: You are not logged in. To test the complete workflow:');
      console.log('   1. Open http://localhost:3002/login in your browser');
      console.log('   2. Log in with your credentials');
      console.log('   3. Run this test again to verify download tracking');
    }
    
    console.log('\n🎯 NEXT STEPS:');
    console.log('   1. Follow the manual testing steps in COMPLETE_AUTHENTICATION_WORKFLOW_TEST.md');
    console.log('   2. Test the complete user workflow in a browser');
    console.log('   3. Verify download blocking and authentication flow');
    
  } catch (error) {
    console.error('\n❌ Test execution failed:', error);
  } finally {
    cleanup();
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  runTests();
}

module.exports = {
  runTests,
  testAPIEndpoints,
  testFileUploadAndConversion,
  testDownloadTracking,
  testAuthenticationCheck
};
