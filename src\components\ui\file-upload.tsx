"use client";

import React, { useRef, useState, useCallback } from 'react';
import { cn } from '@/lib/utils';
import { Button } from './button';
import { Progress } from './progress';
import { validateFile, FileValidationOptions } from '@/utils/inputValidation';

interface FileUploadProps {
  accept?: string;
  multiple?: boolean;
  maxSize?: number; // in MB
  onFileSelect?: (files: File[]) => void;
  onError?: (error: string) => void;
  className?: string;
  disabled?: boolean;
  children?: React.ReactNode;
  validation?: FileValidationOptions;
  showProgress?: boolean;
  dragAndDrop?: boolean;
}

export function FileUpload({
  accept = "*",
  multiple = false,
  maxSize = 50,
  onFileSelect,
  onError,
  className,
  disabled = false,
  children,
  validation,
  showProgress = false,
  dragAndDrop = true,
  ...props
}: FileUploadProps) {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isDragOver, setIsDragOver] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isUploading, setIsUploading] = useState(false);

  const handleFileSelection = useCallback((files: FileList | null) => {
    if (!files || files.length === 0) return;

    const fileArray = Array.from(files);
    const validFiles: File[] = [];
    const errors: string[] = [];

    // Validate each file
    for (const file of fileArray) {
      const validationOptions = {
        maxSizeMB: maxSize,
        ...validation
      };

      const result = validateFile(file, validationOptions);
      
      if (result.isValid) {
        validFiles.push(file);
      } else if (result.error) {
        errors.push(`${file.name}: ${result.error}`);
      }
    }

    // Handle errors
    if (errors.length > 0) {
      onError?.(errors.join('\n'));
    }

    // Handle valid files
    if (validFiles.length > 0) {
      if (showProgress) {
        setIsUploading(true);
        setUploadProgress(0);
        
        // Simulate upload progress
        const interval = setInterval(() => {
          setUploadProgress(prev => {
            if (prev >= 100) {
              clearInterval(interval);
              setIsUploading(false);
              onFileSelect?.(validFiles);
              return 100;
            }
            return prev + 10;
          });
        }, 100);
      } else {
        onFileSelect?.(validFiles);
      }
    }
  }, [maxSize, validation, onError, onFileSelect, showProgress]);

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    handleFileSelection(event.target.files);
  };

  const handleDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    if (!disabled && dragAndDrop) {
      setIsDragOver(true);
    }
  }, [disabled, dragAndDrop]);

  const handleDragLeave = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleDrop = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    setIsDragOver(false);
    
    if (!disabled && dragAndDrop) {
      handleFileSelection(event.dataTransfer.files);
    }
  }, [disabled, dragAndDrop, handleFileSelection]);

  const openFileDialog = () => {
    if (!disabled) {
      fileInputRef.current?.click();
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className={cn("relative", className)}>
      <input
        ref={fileInputRef}
        type="file"
        accept={accept}
        multiple={multiple}
        onChange={handleInputChange}
        className="hidden"
        disabled={disabled}
        {...props}
      />
      
      {children ? (
        <div onClick={openFileDialog} className="cursor-pointer">
          {children}
        </div>
      ) : (
        <div
          onClick={openFileDialog}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          className={cn(
            "border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors",
            isDragOver
              ? "border-primary bg-primary/5"
              : "border-gray-300 dark:border-gray-600 hover:border-primary/50",
            disabled && "opacity-50 cursor-not-allowed",
            className
          )}
        >
          <div className="space-y-2">
            <div className="mx-auto w-12 h-12 text-gray-400">
              <svg
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                className="w-full h-full"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                />
              </svg>
            </div>
            
            <div>
              <p className="text-sm font-medium">
                {dragAndDrop ? "Drop files here or click to browse" : "Click to browse files"}
              </p>
              <p className="text-xs text-gray-500">
                {accept !== "*" && `Accepted formats: ${accept}`}
                {maxSize && ` • Max size: ${maxSize}MB`}
                {multiple && " • Multiple files allowed"}
              </p>
            </div>
          </div>
        </div>
      )}

      {showProgress && isUploading && (
        <div className="mt-4 space-y-2">
          <div className="flex justify-between text-sm">
            <span>Uploading...</span>
            <span>{uploadProgress}%</span>
          </div>
          <Progress value={uploadProgress} className="w-full" />
        </div>
      )}
    </div>
  );
}

// Preset configurations for common file types
export const FileUploadPresets = {
  pdf: {
    accept: ".pdf",
    validation: {
      allowedTypes: ["application/pdf"],
      allowedExtensions: ["pdf"]
    }
  },
  image: {
    accept: ".jpg,.jpeg,.png,.gif,.webp",
    validation: {
      allowedTypes: ["image/jpeg", "image/png", "image/gif", "image/webp"],
      allowedExtensions: ["jpg", "jpeg", "png", "gif", "webp"]
    }
  },
  document: {
    accept: ".pdf,.doc,.docx,.txt",
    validation: {
      allowedTypes: [
        "application/pdf",
        "application/msword",
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        "text/plain"
      ],
      allowedExtensions: ["pdf", "doc", "docx", "txt"]
    }
  },
  excel: {
    accept: ".xls,.xlsx,.csv",
    validation: {
      allowedTypes: [
        "application/vnd.ms-excel",
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        "text/csv"
      ],
      allowedExtensions: ["xls", "xlsx", "csv"]
    }
  }
};
