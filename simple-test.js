// Simple test to verify one tool is working
const http = require('http');

function testSingleTool(toolId) {
  return new Promise((resolve, reject) => {
    const url = `http://localhost:3002/tools/${toolId}`;
    
    console.log(`Testing: ${url}`);
    
    const req = http.get(url, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        console.log(`Status: ${res.statusCode}`);
        console.log(`Response size: ${data.length} bytes`);
        
        if (res.statusCode === 200) {
          console.log('✅ Tool page loaded successfully');
          
          // Check for specific components
          if (data.includes('ExcelToPdfConverter')) {
            console.log('✅ ExcelToPdfConverter component detected');
          } else if (data.includes('GenericConverter')) {
            console.log('🔄 GenericConverter component detected');
          } else {
            console.log('❓ Component type unclear');
          }
          
          // Check for errors
          if (data.includes('Error:') || data.includes('error')) {
            console.log('⚠️  Page contains error messages');
          } else {
            console.log('✅ No error messages detected');
          }
          
          resolve(true);
        } else {
          console.log(`❌ Failed with status: ${res.statusCode}`);
          resolve(false);
        }
      });
    });
    
    req.on('error', (err) => {
      console.log(`💥 Request failed: ${err.message}`);
      reject(err);
    });
    
    req.setTimeout(10000, () => {
      console.log('⏰ Request timeout');
      req.destroy();
      reject(new Error('Timeout'));
    });
  });
}

// Test excel-to-pdf tool
testSingleTool('excel-to-pdf')
  .then(success => {
    if (success) {
      console.log('\n🎉 Test completed successfully!');
      console.log('Next: Test other tools manually in browser');
    } else {
      console.log('\n❌ Test failed');
      console.log('Check server logs for errors');
    }
  })
  .catch(err => {
    console.log('\n💥 Test error:', err.message);
    console.log('Make sure development server is running on port 3002');
  });
