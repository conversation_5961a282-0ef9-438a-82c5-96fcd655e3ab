'use client';

import { Suspense, lazy } from 'react';
import Image from 'next/image';
import { PageData, ContentType } from '@/lib/routing';
import { CalculatorConfig } from '@/data/calculators';
import { useTheme } from '@/hooks/useTheme';

// Lazy load components for better performance
const CalculatorDialog = lazy(() => import('@/components/calculators/CalculatorDialog'));
const GenericConverter = lazy(() => import('@/components/tools/GenericConverter'));
const BlogContent = lazy(() => import('@/components/blog/BlogContent'));

interface DynamicContentProps {
  type: ContentType;
  data: PageData;
  slug: string;
}

// Loading skeleton component
function ContentSkeleton({ type }: { type: ContentType }) {
  const { theme } = useTheme();

  const skeletonClass = `animate-pulse ${
    theme === 'dark' ? 'bg-gray-800' : 'bg-gray-200'
  }`;

  switch (type) {
    case 'calculators':
      return (
        <div className="max-w-2xl mx-auto space-y-6">
          <div className={`h-64 rounded-lg ${skeletonClass}`} />
          <div className="space-y-4">
            <div className={`h-4 rounded ${skeletonClass} w-3/4`} />
            <div className={`h-4 rounded ${skeletonClass} w-1/2`} />
            <div className={`h-4 rounded ${skeletonClass} w-2/3`} />
          </div>
        </div>
      );

    case 'tools':
      return (
        <div className="max-w-4xl mx-auto space-y-6">
          <div className={`h-48 rounded-lg ${skeletonClass}`} />
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className={`h-32 rounded-lg ${skeletonClass}`} />
            <div className={`h-32 rounded-lg ${skeletonClass}`} />
          </div>
          <div className="space-y-4">
            <div className={`h-4 rounded ${skeletonClass} w-full`} />
            <div className={`h-4 rounded ${skeletonClass} w-3/4`} />
            <div className={`h-4 rounded ${skeletonClass} w-1/2`} />
          </div>
        </div>
      );

    case 'blogs':
      return (
        <div className="max-w-4xl mx-auto space-y-6">
          <div className={`h-64 rounded-lg ${skeletonClass}`} />
          <div className="space-y-4">
            {[...Array(8)].map((_, i) => (
              <div key={i} className={`h-4 rounded ${skeletonClass} ${
                i % 3 === 0 ? 'w-full' : i % 3 === 1 ? 'w-3/4' : 'w-5/6'
              }`} />
            ))}
          </div>
        </div>
      );

    default:
      return (
        <div className="max-w-2xl mx-auto">
          <div className={`h-64 rounded-lg ${skeletonClass}`} />
        </div>
      );
  }
}

// Blog content component (since it might not exist yet)
function BlogContentFallback({ data }: { data: PageData }) {
  const { theme } = useTheme();

  return (
    <div className={`max-w-4xl mx-auto ${
      theme === 'dark' ? 'text-gray-100' : 'text-gray-900'
    }`}>
      {/* Blog image */}
      {data.image && (
        <div className="mb-8 relative h-64 sm:h-80">
          <Image
            src={data.image}
            alt={data.title}
            fill
            className="object-cover rounded-lg shadow-lg"
          />
        </div>
      )}

      {/* Blog metadata */}
      <div className={`flex flex-wrap items-center gap-4 mb-8 text-sm ${
        theme === 'dark' ? 'text-gray-400' : 'text-gray-600'
      }`}>
        {data.author && (
          <span className="flex items-center gap-2">
            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
            </svg>
            {data.author}
          </span>
        )}
        {data.date && (
          <span className="flex items-center gap-2">
            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
            </svg>
            {data.date}
          </span>
        )}
        {data.category && (
          <span className={`px-2 py-1 rounded text-xs font-medium ${
            theme === 'dark'
              ? 'bg-gray-800 text-gray-300'
              : 'bg-gray-100 text-gray-700'
          }`}>
            {data.category}
          </span>
        )}
      </div>

      {/* Blog content */}
      <div className={`prose prose-lg max-w-none ${
        theme === 'dark'
          ? 'prose-invert prose-headings:text-gray-100 prose-p:text-gray-300 prose-a:text-blue-400'
          : 'prose-headings:text-gray-900 prose-p:text-gray-700 prose-a:text-blue-600'
      }`}>
        {data.content ? (
          <div dangerouslySetInnerHTML={{ __html: data.content }} />
        ) : (
          <p>Content coming soon...</p>
        )}
      </div>

      {/* Tags */}
      {data.tags && data.tags.length > 0 && (
        <div className="mt-8 pt-8 border-t border-gray-200 dark:border-gray-700">
          <h3 className={`text-lg font-semibold mb-4 ${
            theme === 'dark' ? 'text-gray-100' : 'text-gray-900'
          }`}>
            Tags
          </h3>
          <div className="flex flex-wrap gap-2">
            {data.tags.map((tag, index) => (
              <span
                key={index}
                className={`px-3 py-1 rounded-full text-sm ${
                  theme === 'dark'
                    ? 'bg-gray-800 text-gray-300 hover:bg-gray-700'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                } transition-colors cursor-pointer`}
              >
                #{tag}
              </span>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}

export default function DynamicContent({ type, data, slug }: DynamicContentProps) {
  const renderContent = () => {
    switch (type) {
      case 'calculators':
        if (data.comingSoon) {
          return (
            <div className="max-w-2xl mx-auto text-center py-12">
              <div className="text-6xl mb-4">🚧</div>
              <h3 className="text-2xl font-bold mb-4">Coming Soon</h3>
              <p className="text-gray-600 dark:text-gray-400">
                This calculator is currently under development. Check back soon!
              </p>
            </div>
          );
        }
        // Ensure data has required fields for CalculatorConfig
        if (!data.icon) {
          return (
            <div className="flex items-center justify-center min-h-[400px]">
              <div className="text-center">
                <p className="text-muted-foreground">Calculator configuration incomplete</p>
                <p className="text-sm text-muted-foreground mt-2">Missing icon configuration</p>
              </div>
            </div>
          );
        }

        // Type-safe conversion from PageData to CalculatorConfig
        const calculatorConfig: CalculatorConfig = {
          id: data.id,
          title: data.title,
          description: data.description,
          icon: data.icon, // Now guaranteed to be string
          category: (data.category as CalculatorConfig['category']) || 'math',
          popular: data.popular || false,
          comingSoon: data.comingSoon || false,
        };

        return (
          <Suspense fallback={<ContentSkeleton type="calculators" />}>
            <CalculatorDialog calculator={calculatorConfig} />
          </Suspense>
        );

      case 'tools':
        if (!data.hasConfig) {
          return (
            <div className="max-w-2xl mx-auto text-center py-12">
              <div className="text-6xl mb-4">🔧</div>
              <h3 className="text-2xl font-bold mb-4">Coming Soon</h3>
              <p className="text-gray-600 dark:text-gray-400">
                This tool is currently under development. Check back soon!
              </p>
            </div>
          );
        }
        return (
          <Suspense fallback={<ContentSkeleton type="tools" />}>
            <GenericConverter {...(data as any)} />
          </Suspense>
        );

      case 'blogs':
        return (
          <Suspense fallback={<ContentSkeleton type="blogs" />}>
            <BlogContent data={data} />
          </Suspense>
        );

      default:
        return (
          <div className="max-w-2xl mx-auto text-center py-12">
            <div className="text-6xl mb-4">❓</div>
            <h3 className="text-2xl font-bold mb-4">Content Not Found</h3>
            <p className="text-gray-600 dark:text-gray-400">
              The requested content could not be loaded.
            </p>
          </div>
        );
    }
  };

  return (
    <div className="w-full">
      {renderContent()}
    </div>
  );
}
