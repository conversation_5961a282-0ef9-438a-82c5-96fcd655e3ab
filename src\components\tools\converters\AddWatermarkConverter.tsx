"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Slider } from "@/components/ui/slider";
import { FileUpload } from "@/components/ui/file-upload";
import { Progress } from "@/components/ui/progress";

export default function AddWatermarkConverter() {
  const [file, setFile] = useState<File | null>(null);
  const [watermarkText, setWatermarkText] = useState("CONFIDENTIAL");
  const [opacity, setOpacity] = useState([30]);
  const [fontSize, setFontSize] = useState([48]);
  const [position, setPosition] = useState("center");
  const [rotation, setRotation] = useState([45]);
  const [isConverting, setIsConverting] = useState(false);
  const [conversionProgress, setConversionProgress] = useState(0);
  const [convertedFileUrl, setConvertedFileUrl] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  const handleFileSelect = (files: File[]) => {
    if (files.length > 0) {
      setFile(files[0]);
      setError(null);
      setConvertedFileUrl(null);
    }
  };

  const handleAddWatermark = async () => {
    if (!file) {
      setError("Please select a PDF file first.");
      return;
    }

    if (!watermarkText.trim()) {
      setError("Please enter watermark text.");
      return;
    }

    setIsConverting(true);
    setError(null);
    setConversionProgress(0);

    try {
      // Create FormData for file upload
      const formData = new FormData();
      formData.append('file', file);
      formData.append('watermarkText', watermarkText);
      formData.append('opacity', (opacity[0] / 100).toString());
      formData.append('fontSize', fontSize[0].toString());
      formData.append('position', position);
      formData.append('rotation', rotation[0].toString());

      // Start progress simulation
      const progressInterval = setInterval(() => {
        setConversionProgress(prev => {
          if (prev >= 90) return prev;
          return prev + Math.random() * 10;
        });
      }, 300);

      // Send file to watermark API
      const response = await fetch('/api/tools/add-watermark', {
        method: 'POST',
        body: formData,
      });

      clearInterval(progressInterval);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Watermark failed');
      }

      // Get the watermarked PDF blob
      const watermarkedBlob = await response.blob();
      setConvertedFileUrl(URL.createObjectURL(watermarkedBlob));
      setConversionProgress(100);

    } catch (err) {
      setError(err instanceof Error ? err.message : "An error occurred during watermarking. Please try again.");
      console.error(err);
    } finally {
      setIsConverting(false);
    }
  };

  const handleDownload = () => {
    if (convertedFileUrl) {
      const link = document.createElement("a");
      link.href = convertedFileUrl;
      link.download = file ? `${file.name.replace(".pdf", "")}_watermarked.pdf` : "watermarked.pdf";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  return (
    <div className="space-y-6">
      <div className="bg-blue-50 p-4 rounded-lg">
        <h2 className="text-lg font-semibold mb-2">How to Add Watermark to PDF</h2>
        <ol className="list-decimal list-inside space-y-2 text-gray-700">
          <li>Upload your PDF file using the uploader below.</li>
          <li>Enter your watermark text and customize the appearance.</li>
          <li>Click "Add Watermark" to process your file.</li>
          <li>Download your watermarked PDF when ready.</li>
        </ol>
      </div>

      <div className="space-y-4">
        <div>
          <Label htmlFor="file-upload">Select PDF File</Label>
          <FileUpload
            accept=".pdf"
            onFileSelect={handleFileSelect}
            maxSize={10 * 1024 * 1024} // 10MB
          />
          {file && (
            <p className="text-sm text-gray-600 mt-2">
              Selected: {file.name} ({(file.size / 1024 / 1024).toFixed(2)} MB)
            </p>
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="watermark-text">Watermark Text</Label>
            <Input
              id="watermark-text"
              value={watermarkText}
              onChange={(e) => setWatermarkText(e.target.value)}
              placeholder="Enter watermark text"
              maxLength={50}
            />
          </div>

          <div>
            <Label htmlFor="position">Position</Label>
            <Select value={position} onValueChange={setPosition}>
              <SelectTrigger>
                <SelectValue placeholder="Select position" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="center">Center</SelectItem>
                <SelectItem value="top-left">Top Left</SelectItem>
                <SelectItem value="top-right">Top Right</SelectItem>
                <SelectItem value="bottom-left">Bottom Left</SelectItem>
                <SelectItem value="bottom-right">Bottom Right</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <Label>Opacity: {opacity[0]}%</Label>
            <Slider
              value={opacity}
              onValueChange={setOpacity}
              max={100}
              min={10}
              step={5}
              className="mt-2"
            />
          </div>

          <div>
            <Label>Font Size: {fontSize[0]}px</Label>
            <Slider
              value={fontSize}
              onValueChange={setFontSize}
              max={120}
              min={12}
              step={4}
              className="mt-2"
            />
          </div>

          <div>
            <Label>Rotation: {rotation[0]}°</Label>
            <Slider
              value={rotation}
              onValueChange={setRotation}
              max={180}
              min={-180}
              step={15}
              className="mt-2"
            />
          </div>
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
            {error}
          </div>
        )}

        <Button
          onClick={handleAddWatermark}
          disabled={!file || isConverting || !watermarkText.trim()}
          className="w-full"
        >
          {isConverting ? "Adding Watermark..." : "Add Watermark"}
        </Button>

        {isConverting && (
          <div className="space-y-2">
            <Progress value={conversionProgress} className="w-full" />
            <p className="text-sm text-gray-600 text-center">
              Processing... {conversionProgress}%
            </p>
          </div>
        )}

        {convertedFileUrl && (
          <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded">
            <p className="font-semibold">Watermark added successfully!</p>
            <Button onClick={handleDownload} className="mt-2">
              Download Watermarked PDF
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}
