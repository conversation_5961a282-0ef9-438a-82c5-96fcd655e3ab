"use client";

import { Upload, Cog, Download, CheckCircle, Clock } from "lucide-react";

interface ProgressIndicatorProps {
  progress: number;
  stage: 'uploading' | 'converting' | 'preparing' | 'complete';
  estimatedTime?: number;
  showPercentage?: boolean;
  fileName?: string;
  className?: string;
}

const stageConfig = {
  uploading: {
    icon: Upload,
    label: 'Uploading',
    color: 'text-blue-600',
    bgColor: 'bg-blue-600',
    description: 'Uploading your file to our servers...'
  },
  converting: {
    icon: Cog,
    label: 'Converting',
    color: 'text-purple-600',
    bgColor: 'bg-purple-600',
    description: 'Processing and converting your file...'
  },
  preparing: {
    icon: Download,
    label: 'Preparing',
    color: 'text-green-600',
    bgColor: 'bg-green-600',
    description: 'Preparing your converted file for download...'
  },
  complete: {
    icon: CheckCircle,
    label: 'Complete',
    color: 'text-green-600',
    bgColor: 'bg-green-600',
    description: 'Conversion completed successfully!'
  }
};

export default function ProgressIndicator({
  progress,
  stage,
  estimatedTime,
  showPercentage = true,
  fileName,
  className = ""
}: ProgressIndicatorProps) {
  const config = stageConfig[stage];
  const IconComponent = config.icon;
  
  const formatTime = (seconds: number) => {
    if (seconds < 60) return `${seconds}s`;
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  };

  const clampedProgress = Math.min(Math.max(progress, 0), 100);

  return (
    <div
      className={`bg-white border border-gray-200 rounded-lg p-6 ${className}`}
      role="progressbar"
      aria-valuenow={clampedProgress}
      aria-valuemin={0}
      aria-valuemax={100}
      aria-labelledby="progress-label"
      aria-describedby="progress-description"
    >
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center">
          <div className={`p-2 rounded-full bg-gray-100 ${config.color}`} aria-hidden="true">
            <IconComponent className="h-5 w-5" />
          </div>
          <div className="ml-3">
            <h3
              id="progress-label"
              className="text-lg font-medium text-gray-900"
            >
              {config.label}
            </h3>
            <p
              id="progress-description"
              className="text-sm text-gray-500"
            >
              {config.description}
            </p>
          </div>
        </div>
        
        {showPercentage && (
          <div className="text-right">
            <div className={`text-2xl font-bold ${config.color}`}>
              {Math.round(clampedProgress)}%
            </div>
            {estimatedTime && estimatedTime > 0 && (
              <div className="flex items-center text-xs text-gray-500 mt-1">
                <Clock className="h-3 w-3 mr-1" aria-hidden="true" />
                <span aria-label={`Estimated time remaining: ${formatTime(estimatedTime)}`}>
                  {formatTime(estimatedTime)} remaining
                </span>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Progress Bar */}
      <div className="mb-4">
        <div className="flex justify-between text-sm text-gray-600 mb-2">
          <span>Progress</span>
          {fileName && (
            <span
              className="truncate ml-2 max-w-xs"
              title={fileName}
              aria-label={`Processing file: ${fileName}`}
            >
              {fileName}
            </span>
          )}
        </div>

        <div
          className="w-full bg-gray-200 rounded-full h-2.5"
          role="presentation"
        >
          <div
            className={`h-2.5 rounded-full transition-all duration-300 ease-out ${config.bgColor}`}
            style={{ width: `${clampedProgress}%` }}
            aria-hidden="true"
          >
            {/* Animated shimmer effect for active progress */}
            {stage !== 'complete' && clampedProgress > 0 && (
              <div className="h-full w-full rounded-full bg-gradient-to-r from-transparent via-white to-transparent opacity-30 animate-pulse"></div>
            )}
          </div>
        </div>
      </div>

      {/* Stage Indicators */}
      <div
        className="flex justify-between items-center"
        role="group"
        aria-label="Conversion stages"
      >
        {Object.entries(stageConfig).map(([key, stageInfo], index) => {
          const isActive = key === stage;
          const isCompleted = Object.keys(stageConfig).indexOf(stage) > index;
          const StageIcon = stageInfo.icon;

          return (
            <div
              key={key}
              className="flex flex-col items-center"
              role="presentation"
            >
              <div
                className={`w-8 h-8 rounded-full flex items-center justify-center border-2 transition-all duration-200 ${
                  isActive
                    ? `${stageInfo.bgColor} border-transparent text-white`
                    : isCompleted
                    ? 'bg-green-100 border-green-500 text-green-600'
                    : 'bg-gray-100 border-gray-300 text-gray-400'
                }`}
                aria-hidden="true"
              >
                <StageIcon className="h-4 w-4" />
              </div>
              <span
                className={`text-xs mt-1 font-medium ${
                  isActive
                    ? stageInfo.color
                    : isCompleted
                    ? 'text-green-600'
                    : 'text-gray-400'
                }`}
                aria-label={`${stageInfo.label} stage ${isCompleted ? 'completed' : isActive ? 'in progress' : 'pending'}`}
              >
                {stageInfo.label}
              </span>
            </div>
          );
        })}
      </div>
    </div>
  );
}

// Helper function to determine stage based on progress
export function getStageFromProgress(progress: number): ProgressIndicatorProps['stage'] {
  if (progress >= 100) return 'complete';
  if (progress >= 80) return 'preparing';
  if (progress >= 20) return 'converting';
  return 'uploading';
}

// Helper function to estimate remaining time
export function estimateRemainingTime(
  progress: number,
  startTime: number,
  currentTime: number = Date.now()
): number {
  if (progress <= 0) return 0;
  
  const elapsedTime = (currentTime - startTime) / 1000; // in seconds
  const progressRate = progress / elapsedTime;
  const remainingProgress = 100 - progress;
  
  return Math.round(remainingProgress / progressRate);
}
