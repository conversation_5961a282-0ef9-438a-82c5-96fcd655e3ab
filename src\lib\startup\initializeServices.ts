/**
 * Service Initialization
 * Initializes all background services when the application starts
 */

import { initializeArchivalScheduler } from '@/lib/cron/archivalScheduler';

/**
 * Initialize all application services
 * Call this function when the application starts (e.g., in layout.tsx or _app.tsx)
 */
export function initializeServices(): void {
  try {
    console.log('[Services] Initializing application services...');

    // Initialize download archival scheduler
    if (typeof window === 'undefined') {
      // Only run on server side
      initializeArchivalScheduler();
      console.log('[Services] Download archival scheduler initialized');
    }

    console.log('[Services] All services initialized successfully');
  } catch (error) {
    console.error('[Services] Failed to initialize services:', error);
  }
}

/**
 * Cleanup services on application shutdown
 */
export function cleanupServices(): void {
  try {
    console.log('[Services] Cleaning up application services...');

    // Stop archival scheduler
    if (typeof window === 'undefined') {
      const { getArchivalScheduler } = require('@/lib/cron/archivalScheduler');
      const scheduler = getArchivalScheduler();
      scheduler.stop();
      console.log('[Services] Download archival scheduler stopped');
    }

    console.log('[Services] All services cleaned up successfully');
  } catch (error) {
    console.error('[Services] Failed to cleanup services:', error);
  }
}
