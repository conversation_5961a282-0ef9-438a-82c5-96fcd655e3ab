# 🧪 Manual Testing Guide for ToolRapter PDF Tools

## 🚀 Quick Start

1. **Start Development Server**:
   ```bash
   pnpm run dev
   ```

2. **Open Browser**: Navigate to `http://localhost:3000` or `http://localhost:3001`

## 📋 Testing Checklist

### ✅ High Priority Tools (Real API Functionality)

#### 1. Excel to PDF (`/tools/excel-to-pdf`)
- [ ] Upload .xlsx file
- [ ] Conversion completes successfully  
- [ ] Download requires login
- [ ] Downloaded PDF contains Excel data in table format
- [ ] File is valid and opens correctly

#### 2. Word to PDF (`/tools/word-to-pdf`)
- [ ] Upload .docx file
- [ ] Conversion completes successfully
- [ ] Download requires login
- [ ] Downloaded PDF preserves Word formatting
- [ ] File is valid and opens correctly

#### 3. PDF to Word (`/tools/pdf-to-word`)
- [ ] Upload PDF file
- [ ] Conversion completes successfully
- [ ] Download requires login
- [ ] Downloaded .docx file is editable
- [ ] Content is properly extracted

#### 4. PDF to Excel (`/tools/pdf-to-excel`)
- [ ] Upload PDF with tables
- [ ] Conversion completes successfully
- [ ] Download requires login
- [ ] Downloaded .xlsx file contains extracted data
- [ ] Tables are properly structured

#### 5. Merge PDF (`/tools/merge-pdf`)
- [ ] Upload multiple PDF files
- [ ] Conversion completes successfully
- [ ] Download requires login
- [ ] Downloaded PDF contains all pages in order
- [ ] File is valid and complete

#### 6. Split PDF (`/tools/split-pdf`)
- [ ] Upload multi-page PDF
- [ ] Select split options
- [ ] Conversion completes successfully
- [ ] Download requires login
- [ ] Downloaded ZIP contains individual pages
- [ ] All pages are valid PDFs

#### 7. Compress PDF (`/tools/compress-pdf`)
- [ ] Upload large PDF file
- [ ] Conversion completes successfully
- [ ] Download requires login
- [ ] Downloaded PDF is smaller in size
- [ ] Quality is acceptable

#### 8. Rotate PDF (`/tools/rotate-pdf`)
- [ ] Upload PDF file
- [ ] Select rotation angle
- [ ] Conversion completes successfully
- [ ] Download requires login
- [ ] Downloaded PDF pages are rotated correctly

### ✅ Medium Priority Tools

#### 9. JPG to PDF (`/tools/jpg-to-pdf`)
- [ ] Upload JPG image
- [ ] Conversion completes successfully
- [ ] Download requires login
- [ ] Downloaded PDF contains the image
- [ ] Image quality is preserved

#### 10. PDF to JPG (`/tools/pdf-to-jpg`)
- [ ] Upload PDF file
- [ ] Conversion completes successfully
- [ ] Download requires login
- [ ] Downloaded ZIP contains JPG images
- [ ] One image per PDF page

#### 11. HTML to PDF (`/tools/html-to-pdf`)
- [ ] Enter URL or HTML content
- [ ] Conversion completes successfully
- [ ] Download requires login
- [ ] Downloaded PDF renders the content
- [ ] Formatting is preserved

#### 12. PowerPoint to PDF (`/tools/powerpoint-to-pdf`)
- [ ] Upload .pptx file
- [ ] Conversion completes successfully
- [ ] Download requires login
- [ ] Downloaded PDF contains all slides
- [ ] Layout is preserved

#### 13. PDF to PowerPoint (`/tools/pdf-to-powerpoint`)
- [ ] Upload PDF file
- [ ] Conversion completes successfully
- [ ] Download requires login
- [ ] Downloaded .pptx file is editable
- [ ] Content is properly extracted

### ✅ Lower Priority Tools

#### 14. Add Watermark (`/tools/add-watermark`)
- [ ] Upload PDF file
- [ ] Enter watermark text
- [ ] Conversion completes successfully
- [ ] Download requires login
- [ ] Downloaded PDF has watermark on all pages

#### 15. Protect PDF (`/tools/protect-pdf`)
- [ ] Upload PDF file
- [ ] Set password
- [ ] Conversion completes successfully
- [ ] Download requires login
- [ ] Downloaded PDF requires password to open

#### 16. PDF to PDF/A (`/tools/pdf-to-pdfa`)
- [ ] Upload PDF file
- [ ] Receives proper 501 error with alternatives
- [ ] Error message is helpful and informative

### ✅ Generic Converter Tool

#### 17. PNG to PDF (`/tools/png-to-pdf`)
- [ ] Uses GenericConverter (simulation)
- [ ] Shows simulation progress
- [ ] Creates mock download file
- [ ] Authentication protection works

## 🔍 What to Look For

### ✅ Success Indicators
- **No runtime errors** in browser console
- **Real conversion** (not simulation progress)
- **Authentication protection** working
- **Valid output files** that open correctly
- **Proper error handling** for invalid files
- **Download tracking** saves to database

### ❌ Failure Indicators
- Runtime errors in console
- Simulation progress instead of real conversion
- Downloads work without authentication
- Corrupted or invalid output files
- Generic error messages
- Missing download tracking

## 🐛 Common Issues to Check

1. **Component Loading**: Ensure specific converter loads, not GenericConverter
2. **API Endpoints**: Verify `/api/tools/[tool-name]` responds correctly
3. **File Validation**: Check file type and size restrictions
4. **Authentication**: Confirm login requirement for downloads
5. **Database**: Verify download records are saved to MongoDB

## 📊 Testing Results Template

```
Tool: [tool-name]
✅/❌ Page loads without errors
✅/❌ Specific component loads (not generic)
✅/❌ File upload works
✅/❌ Conversion completes
✅/❌ Download requires authentication
✅/❌ Output file is valid
✅/❌ Download tracking works

Notes: [any issues or observations]
```

## 🎯 Success Criteria

- **All 16 specific tools** load their proper converter components
- **Authentication protection** works on all tools
- **Real conversion functionality** works (not simulation)
- **Output files are valid** and properly formatted
- **Download tracking** saves to MongoDB database
- **Error handling** is graceful and informative

## 📞 Next Steps After Testing

1. Document any failing tools
2. Report specific error messages
3. Note any missing authentication protection
4. Verify file corruption issues
5. Proceed with UX improvements for working tools
