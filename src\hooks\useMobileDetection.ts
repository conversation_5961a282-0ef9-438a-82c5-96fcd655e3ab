"use client";

import { useState, useEffect } from 'react';

interface MobileDetectionResult {
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  isTouchDevice: boolean;
  screenSize: 'sm' | 'md' | 'lg' | 'xl' | '2xl';
  orientation: 'portrait' | 'landscape';
  deviceType: 'mobile' | 'tablet' | 'desktop';
}

/**
 * Hook for detecting mobile devices and screen characteristics
 */
export function useMobileDetection(): MobileDetectionResult {
  const [detection, setDetection] = useState<MobileDetectionResult>({
    isMobile: false,
    isTablet: false,
    isDesktop: true,
    isTouchDevice: false,
    screenSize: 'lg',
    orientation: 'landscape',
    deviceType: 'desktop'
  });

  useEffect(() => {
    const updateDetection = () => {
      const width = window.innerWidth;
      const height = window.innerHeight;
      
      // Screen size detection based on Tailwind breakpoints
      let screenSize: MobileDetectionResult['screenSize'] = 'sm';
      if (width >= 1536) screenSize = '2xl';
      else if (width >= 1280) screenSize = 'xl';
      else if (width >= 1024) screenSize = 'lg';
      else if (width >= 768) screenSize = 'md';
      else screenSize = 'sm';
      
      // Device type detection
      const isMobile = width < 768;
      const isTablet = width >= 768 && width < 1024;
      const isDesktop = width >= 1024;
      
      // Touch device detection
      const isTouchDevice = 'ontouchstart' in window || 
                           navigator.maxTouchPoints > 0 ||
                           (navigator as any).msMaxTouchPoints > 0;
      
      // Orientation detection
      const orientation = height > width ? 'portrait' : 'landscape';
      
      // Device type classification
      let deviceType: MobileDetectionResult['deviceType'] = 'desktop';
      if (isMobile) deviceType = 'mobile';
      else if (isTablet) deviceType = 'tablet';
      
      setDetection({
        isMobile,
        isTablet,
        isDesktop,
        isTouchDevice,
        screenSize,
        orientation,
        deviceType
      });
    };

    // Initial detection
    updateDetection();

    // Listen for resize events
    window.addEventListener('resize', updateDetection);
    window.addEventListener('orientationchange', updateDetection);

    return () => {
      window.removeEventListener('resize', updateDetection);
      window.removeEventListener('orientationchange', updateDetection);
    };
  }, []);

  return detection;
}

/**
 * Hook for responsive breakpoint detection
 */
export function useBreakpoint() {
  const { screenSize } = useMobileDetection();
  
  return {
    isSm: screenSize === 'sm',
    isMd: screenSize === 'md',
    isLg: screenSize === 'lg',
    isXl: screenSize === 'xl',
    is2Xl: screenSize === '2xl',
    isSmUp: ['sm', 'md', 'lg', 'xl', '2xl'].includes(screenSize),
    isMdUp: ['md', 'lg', 'xl', '2xl'].includes(screenSize),
    isLgUp: ['lg', 'xl', '2xl'].includes(screenSize),
    isXlUp: ['xl', '2xl'].includes(screenSize),
    screenSize
  };
}

/**
 * Hook for touch gesture detection
 */
export function useTouchGestures() {
  const [gestureState, setGestureState] = useState({
    isSwipeEnabled: false,
    isPinchEnabled: false,
    lastTap: 0,
    tapCount: 0
  });

  const enableSwipe = () => {
    setGestureState(prev => ({ ...prev, isSwipeEnabled: true }));
  };

  const disableSwipe = () => {
    setGestureState(prev => ({ ...prev, isSwipeEnabled: false }));
  };

  const enablePinch = () => {
    setGestureState(prev => ({ ...prev, isPinchEnabled: true }));
  };

  const disablePinch = () => {
    setGestureState(prev => ({ ...prev, isPinchEnabled: false }));
  };

  const handleDoubleTap = (callback: () => void, delay: number = 300) => {
    const now = Date.now();
    const timeDiff = now - gestureState.lastTap;
    
    if (timeDiff < delay && timeDiff > 0) {
      callback();
      setGestureState(prev => ({ ...prev, tapCount: 0, lastTap: 0 }));
    } else {
      setGestureState(prev => ({ ...prev, tapCount: 1, lastTap: now }));
    }
  };

  return {
    ...gestureState,
    enableSwipe,
    disableSwipe,
    enablePinch,
    disablePinch,
    handleDoubleTap
  };
}

/**
 * Hook for viewport-based responsive values
 */
export function useResponsiveValue<T>(values: {
  sm?: T;
  md?: T;
  lg?: T;
  xl?: T;
  '2xl'?: T;
  default: T;
}): T {
  const { screenSize } = useMobileDetection();
  
  // Return the value for current screen size, falling back to smaller sizes if not defined
  if (values[screenSize] !== undefined) {
    return values[screenSize]!;
  }
  
  // Fallback logic
  const fallbackOrder: Array<keyof typeof values> = ['2xl', 'xl', 'lg', 'md', 'sm'];
  const currentIndex = fallbackOrder.indexOf(screenSize);
  
  for (let i = currentIndex + 1; i < fallbackOrder.length; i++) {
    const fallbackSize = fallbackOrder[i];
    if (values[fallbackSize] !== undefined) {
      return values[fallbackSize]!;
    }
  }
  
  return values.default;
}

/**
 * Hook for PWA installation detection
 */
export function usePWAInstallation() {
  const [installPrompt, setInstallPrompt] = useState<any>(null);
  const [isInstallable, setIsInstallable] = useState(false);
  const [isInstalled, setIsInstalled] = useState(false);

  useEffect(() => {
    // Check if app is already installed
    const checkInstalled = () => {
      const isStandalone = window.matchMedia('(display-mode: standalone)').matches ||
                          (window.navigator as any).standalone ||
                          document.referrer.includes('android-app://');
      setIsInstalled(isStandalone);
    };

    checkInstalled();

    // Listen for install prompt
    const handleBeforeInstallPrompt = (e: Event) => {
      e.preventDefault();
      setInstallPrompt(e);
      setIsInstallable(true);
    };

    // Listen for app installed
    const handleAppInstalled = () => {
      setIsInstalled(true);
      setIsInstallable(false);
      setInstallPrompt(null);
    };

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
    window.addEventListener('appinstalled', handleAppInstalled);

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
      window.removeEventListener('appinstalled', handleAppInstalled);
    };
  }, []);

  const promptInstall = async () => {
    if (!installPrompt) return false;

    try {
      await installPrompt.prompt();
      const result = await installPrompt.userChoice;
      
      if (result.outcome === 'accepted') {
        setIsInstallable(false);
        setInstallPrompt(null);
        return true;
      }
    } catch (error) {
      console.error('Error prompting for install:', error);
    }
    
    return false;
  };

  return {
    isInstallable,
    isInstalled,
    promptInstall
  };
}

/**
 * Hook for device capabilities detection
 */
export function useDeviceCapabilities() {
  const [capabilities, setCapabilities] = useState({
    hasCamera: false,
    hasGeolocation: false,
    hasNotifications: false,
    hasVibration: false,
    hasFileSystem: false,
    hasClipboard: false
  });

  useEffect(() => {
    const checkCapabilities = async () => {
      const newCapabilities = {
        hasCamera: !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia),
        hasGeolocation: !!navigator.geolocation,
        hasNotifications: 'Notification' in window,
        hasVibration: 'vibrate' in navigator,
        hasFileSystem: 'showOpenFilePicker' in window,
        hasClipboard: !!(navigator.clipboard && navigator.clipboard.writeText)
      };
      
      setCapabilities(newCapabilities);
    };

    checkCapabilities();
  }, []);

  return capabilities;
}
