import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { connectToDatabase } from '@/lib/mongo';

interface DownloadTrackingData {
  fileName: string;
  conversionType: string;
  originalFileSize?: number;
  convertedFileSize?: number;
  toolName?: string;
  userAgent?: string;
  timestamp: string;
}

/**
 * POST /api/downloads/track
 * Tracks user download activity in MongoDB
 * Requires authentication
 */
export async function POST(request: NextRequest) {
  try {
    // Get session from NextAuth
    const session = await getServerSession(authOptions);

    if (!session || !session.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse request body
    const data: DownloadTrackingData = await request.json();

    // Validate required fields
    if (!data.fileName || !data.conversionType || !data.timestamp) {
      return NextResponse.json(
        { error: 'Missing required fields: fileName, conversionType, timestamp' },
        { status: 400 }
      );
    }

    // Get client IP address
    const clientIP = request.headers.get('x-forwarded-for') || 
                    request.headers.get('x-real-ip') || 
                    'unknown';

    // Connect to database
    const { db } = await connectToDatabase();

    // Create download record
    const downloadRecord = {
      userId: session.user.id,
      userEmail: session.user.email,
      userName: session.user.name,
      fileName: data.fileName,
      conversionType: data.conversionType,
      originalFileSize: data.originalFileSize || null,
      convertedFileSize: data.convertedFileSize || null,
      toolName: data.toolName || null,
      userAgent: data.userAgent || null,
      clientIP: clientIP,
      timestamp: new Date(data.timestamp),
      createdAt: new Date()
    };

    // Insert download record
    const result = await db.collection('downloads').insertOne(downloadRecord);

    if (!result.insertedId) {
      throw new Error('Failed to insert download record');
    }

    // Return success response
    return NextResponse.json({
      success: true,
      downloadId: result.insertedId.toString(),
      message: 'Download tracked successfully'
    });

  } catch (error) {
    console.error('Download tracking error:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to track download',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * GET /api/downloads/track
 * Returns download tracking statistics for the authenticated user
 */
export async function GET(request: NextRequest) {
  try {
    // Get session from NextAuth
    const session = await getServerSession(authOptions);

    if (!session || !session.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '10');
    const offset = parseInt(searchParams.get('offset') || '0');
    const conversionType = searchParams.get('conversionType');

    // Connect to database
    const { db } = await connectToDatabase();

    // Build query
    const query: any = { userId: session.user.id };
    if (conversionType) {
      query.conversionType = conversionType;
    }

    // Get user's download history
    const downloads = await db.collection('downloads')
      .find(query)
      .sort({ timestamp: -1 })
      .skip(offset)
      .limit(Math.min(limit, 100)) // Max 100 records per request
      .toArray();

    // Get total count
    const totalCount = await db.collection('downloads').countDocuments(query);

    // Get download statistics
    const stats = await db.collection('downloads').aggregate([
      { $match: { userId: session.user.id } },
      {
        $group: {
          _id: null,
          totalDownloads: { $sum: 1 },
          totalFileSize: { $sum: '$convertedFileSize' },
          conversionTypes: { $addToSet: '$conversionType' },
          toolsUsed: { $addToSet: '$toolName' }
        }
      }
    ]).toArray();

    const userStats = stats[0] || {
      totalDownloads: 0,
      totalFileSize: 0,
      conversionTypes: [],
      toolsUsed: []
    };

    // Format response
    const formattedDownloads = downloads.map(download => ({
      id: download._id.toString(),
      fileName: download.fileName,
      conversionType: download.conversionType,
      toolName: download.toolName,
      fileSize: download.convertedFileSize,
      timestamp: download.timestamp,
      createdAt: download.createdAt
    }));

    return NextResponse.json({
      downloads: formattedDownloads,
      pagination: {
        total: totalCount,
        limit,
        offset,
        hasMore: offset + limit < totalCount
      },
      statistics: {
        totalDownloads: userStats.totalDownloads,
        totalFileSize: userStats.totalFileSize,
        uniqueConversionTypes: userStats.conversionTypes.length,
        uniqueToolsUsed: userStats.toolsUsed.length,
        conversionTypes: userStats.conversionTypes,
        toolsUsed: userStats.toolsUsed
      }
    });

  } catch (error) {
    console.error('Download history error:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to retrieve download history',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
