import { NextRequest, NextResponse } from 'next/server';
import { PDFDocument, rgb, StandardFonts } from 'pdf-lib';
import J<PERSON><PERSON><PERSON> from 'jszip';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const quality = parseInt(formData.get('quality') as string || '80');
    const pageSelection = formData.get('pageSelection') as string || 'all';
    const specificPages = formData.get('specificPages') as string || '';

    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }

    if (file.type !== 'application/pdf') {
      return NextResponse.json(
        { error: 'File must be a PDF document' },
        { status: 400 }
      );
    }

    // Validate quality
    const validQuality = Math.max(10, Math.min(100, quality));

    // Convert file to buffer
    const arrayBuffer = await file.arrayBuffer();
    
    // Load the PDF document
    const pdfDoc = await PDFDocument.load(arrayBuffer);
    const totalPages = pdfDoc.getPageCount();

    if (totalPages === 0) {
      return NextResponse.json(
        { error: 'PDF document has no pages' },
        { status: 400 }
      );
    }

    let pagesToConvert: number[] = [];

    if (pageSelection === 'all') {
      // Convert all pages
      pagesToConvert = Array.from({ length: totalPages }, (_, i) => i);
    } else if (pageSelection === 'specific' && specificPages) {
      // Parse specific pages (e.g., "1,3,5-7")
      pagesToConvert = parsePageNumbers(specificPages, totalPages);
    } else {
      return NextResponse.json(
        { error: 'Invalid page selection. Use "all" or "specific" with page numbers.' },
        { status: 400 }
      );
    }

    if (pagesToConvert.length === 0) {
      return NextResponse.json(
        { error: 'No valid pages specified for conversion' },
        { status: 400 }
      );
    }

    // For this implementation, we'll create placeholder images
    // In a production environment, you would use a library like pdf2pic or similar
    // to actually render PDF pages as images
    
    const imageFiles: { name: string; data: Uint8Array }[] = [];
    
    // Create placeholder images for each page
    for (const pageIndex of pagesToConvert) {
      const pageNumber = pageIndex + 1;
      
      // Create a simple placeholder image (in a real implementation, this would be actual PDF rendering)
      const placeholderImage = await createPlaceholderImage(pageNumber, file.name);
      
      const fileName = `${file.name.replace('.pdf', '')}_page_${pageNumber}.jpg`;
      imageFiles.push({
        name: fileName,
        data: placeholderImage
      });
    }

    if (imageFiles.length === 1) {
      // Return single text file (corrected Content-Type since we're creating text placeholders)
      const imageFile = imageFiles[0];

      const response = new NextResponse(imageFile.data, {
        status: 200,
        headers: {
          'Content-Type': 'text/plain; charset=utf-8',
          'Content-Disposition': `attachment; filename="${file.name.replace('.pdf', '')}_page_1.txt"`,
          'X-Original-Pages': totalPages.toString(),
          'X-Converted-Pages': '1',
          'X-Quality': validQuality.toString(),
          'X-Note': 'Text representation - actual image rendering requires specialized libraries',
        },
      });

      return response;
    } else {
      // Return ZIP file with multiple images
      const zip = new JSZip();
      
      imageFiles.forEach((imageFile, index) => {
        // Change extension to .txt since we're creating text placeholders
        const txtFileName = imageFile.name.replace('.jpg', '.txt');
        zip.file(txtFileName, imageFile.data);
      });

      const zipBytes = await zip.generateAsync({ type: 'uint8array' });

      // Generate filename for ZIP (corrected to indicate text files)
      const originalName = file.name.replace(/\.pdf$/i, '');
      const zipFilename = `${originalName}_text_content_${imageFiles.length}_pages.zip`;

      const response = new NextResponse(zipBytes, {
        status: 200,
        headers: {
          'Content-Type': 'application/zip',
          'Content-Disposition': `attachment; filename="${zipFilename}"`,
          'X-Original-Pages': totalPages.toString(),
          'X-Converted-Pages': imageFiles.length.toString(),
          'X-Quality': validQuality.toString(),
          'X-Note': 'ZIP contains text representations - actual image rendering requires specialized libraries',
        },
      });

      return response;
    }

  } catch (error) {
    console.error('PDF to JPG conversion error:', error);
    
    if (error instanceof Error) {
      if (error.message.includes('Invalid PDF')) {
        return NextResponse.json(
          { error: 'Invalid PDF file. Please ensure the file is not corrupted.' },
          { status: 400 }
        );
      }
      if (error.message.includes('password')) {
        return NextResponse.json(
          { error: 'Password-protected PDF files are not supported.' },
          { status: 400 }
        );
      }
    }

    return NextResponse.json(
      { error: 'Failed to convert PDF to JPG. Please ensure the file is a valid PDF document.' },
      { status: 500 }
    );
  }
}

// Helper function to create a placeholder image (in production, use actual PDF rendering)
async function createPlaceholderImage(pageNumber: number, fileName: string): Promise<Uint8Array> {
  // This is a placeholder implementation
  // In production, you would use libraries like pdf2pic, puppeteer, or canvas to render actual PDF pages
  
  // Create a simple text-based placeholder
  const placeholderText = `PDF to JPG Conversion\n\nPage ${pageNumber}\nFrom: ${fileName}\n\nNote: This is a placeholder image.\nFor actual PDF page rendering, specialized\nlibraries like pdf2pic would be used.`;
  
  // Return a minimal JPEG-like structure (this is just for demonstration)
  // In reality, you would generate actual JPEG bytes
  const textBytes = new TextEncoder().encode(placeholderText);
  return new Uint8Array(textBytes);
}

// Helper function to parse page numbers like "1,3,5-7"
function parsePageNumbers(pageString: string, totalPages: number): number[] {
  const pageNumbers: number[] = [];
  const parts = pageString.split(',').map(s => s.trim());
  
  for (const part of parts) {
    if (part.includes('-')) {
      // Handle range like "5-7"
      const [startStr, endStr] = part.split('-').map(s => s.trim());
      const start = Math.max(1, parseInt(startStr) || 1);
      const end = Math.min(totalPages, parseInt(endStr) || totalPages);
      
      for (let i = start; i <= end; i++) {
        pageNumbers.push(i - 1); // Convert to 0-based index
      }
    } else {
      // Handle single page like "3"
      const pageNum = parseInt(part);
      if (pageNum >= 1 && pageNum <= totalPages) {
        pageNumbers.push(pageNum - 1); // Convert to 0-based index
      }
    }
  }
  
  // Remove duplicates and sort
  return Array.from(new Set(pageNumbers)).sort((a, b) => a - b);
}

export async function GET() {
  return NextResponse.json(
    { 
      message: 'PDF to JPG Conversion API',
      supportedInput: 'PDF',
      outputFormat: 'JPG images (single file or ZIP)',
      qualityRange: '10-100',
      pageSelection: ['all', 'specific'],
      maxFileSize: '10MB',
      note: 'Converts PDF pages to JPG images. Currently uses placeholder images - production version would render actual PDF content.'
    },
    { status: 200 }
  );
}
