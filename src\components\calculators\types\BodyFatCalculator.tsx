"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

interface BodyFatResult {
  bodyFatPercentage: number;
  category: string;
  leanBodyMass: number;
  fatMass: number;
  idealBodyFat: { min: number; max: number };
}

export default function BodyFatCalculator() {
  // Common inputs
  const [gender, setGender] = useState<string>("male");
  const [age, setAge] = useState<number>(30);
  const [weight, setWeight] = useState<number>(70);
  const [height, setHeight] = useState<number>(175);
  const [unit, setUnit] = useState<string>("metric");

  // Navy Method inputs
  const [neck, setNeck] = useState<number>(37);
  const [waist, setWaist] = useState<number>(85);
  const [hip, setHip] = useState<number>(95); // For females

  // Skinfold Method inputs
  const [tricep, setTricep] = useState<number>(15);
  const [subscapular, setSubscapular] = useState<number>(18);
  const [suprailiac, setSuprailiac] = useState<number>(20);

  const [navyResult, setNavyResult] = useState<BodyFatResult | null>(null);
  const [skinfoldResult, setSkinfoldResult] = useState<BodyFatResult | null>(null);

  const convertToMetric = (value: number, type: string): number => {
    if (unit === "metric") return value;
    if (type === "weight") return value * 0.453592; // lbs to kg
    if (type === "length") return value * 2.54; // inches to cm
    return value;
  };

  const convertFromMetric = (value: number, type: string): number => {
    if (unit === "metric") return value;
    if (type === "weight") return value / 0.453592; // kg to lbs
    if (type === "length") return value / 2.54; // cm to inches
    return value;
  };

  const calculateNavyMethod = () => {
    const heightCm = convertToMetric(height, "length");
    const neckCm = convertToMetric(neck, "length");
    const waistCm = convertToMetric(waist, "length");
    const hipCm = convertToMetric(hip, "length");
    const weightKg = convertToMetric(weight, "weight");

    let bodyFatPercentage: number;

    if (gender === "male") {
      // Navy formula for men: 495 / (1.0324 - 0.19077 * log10(waist - neck) + 0.15456 * log10(height)) - 450
      bodyFatPercentage = 495 / (1.0324 - 0.19077 * Math.log10(waistCm - neckCm) + 0.15456 * Math.log10(heightCm)) - 450;
    } else {
      // Navy formula for women: 495 / (1.29579 - 0.35004 * log10(waist + hip - neck) + 0.22100 * log10(height)) - 450
      bodyFatPercentage = 495 / (1.29579 - 0.35004 * Math.log10(waistCm + hipCm - neckCm) + 0.22100 * Math.log10(heightCm)) - 450;
    }

    const fatMass = (bodyFatPercentage / 100) * weightKg;
    const leanBodyMass = weightKg - fatMass;
    const category = getBodyFatCategory(bodyFatPercentage, gender, age);
    const idealBodyFat = getIdealBodyFatRange(gender, age);

    setNavyResult({
      bodyFatPercentage,
      category,
      leanBodyMass,
      fatMass,
      idealBodyFat
    });
  };

  const calculateSkinfoldMethod = () => {
    const weightKg = convertToMetric(weight, "weight");
    let bodyDensity: number;

    if (gender === "male") {
      // Jackson-Pollock 3-site formula for men (chest, abdomen, thigh)
      // Using tricep, subscapular, suprailiac as approximation
      const sum = tricep + subscapular + suprailiac;
      bodyDensity = 1.10938 - (0.0008267 * sum) + (0.0000016 * sum * sum) - (0.0002574 * age);
    } else {
      // Jackson-Pollock 3-site formula for women (tricep, suprailiac, thigh)
      const sum = tricep + subscapular + suprailiac;
      bodyDensity = 1.0994921 - (0.0009929 * sum) + (0.0000023 * sum * sum) - (0.0001392 * age);
    }

    // Siri equation: Body Fat % = (495 / Body Density) - 450
    const bodyFatPercentage = (495 / bodyDensity) - 450;

    const fatMass = (bodyFatPercentage / 100) * weightKg;
    const leanBodyMass = weightKg - fatMass;
    const category = getBodyFatCategory(bodyFatPercentage, gender, age);
    const idealBodyFat = getIdealBodyFatRange(gender, age);

    setSkinfoldResult({
      bodyFatPercentage,
      category,
      leanBodyMass,
      fatMass,
      idealBodyFat
    });
  };

  const getBodyFatCategory = (bodyFat: number, gender: string, age: number): string => {
    if (gender === "male") {
      if (bodyFat < 6) return "Essential Fat";
      if (bodyFat < 14) return "Athletes";
      if (bodyFat < 18) return "Fitness";
      if (bodyFat < 25) return "Average";
      return "Obese";
    } else {
      if (bodyFat < 14) return "Essential Fat";
      if (bodyFat < 21) return "Athletes";
      if (bodyFat < 25) return "Fitness";
      if (bodyFat < 32) return "Average";
      return "Obese";
    }
  };

  const getIdealBodyFatRange = (gender: string, age: number): { min: number; max: number } => {
    if (gender === "male") {
      if (age < 30) return { min: 14, max: 20 };
      if (age < 40) return { min: 16, max: 22 };
      if (age < 50) return { min: 18, max: 24 };
      return { min: 20, max: 26 };
    } else {
      if (age < 30) return { min: 16, max: 24 };
      if (age < 40) return { min: 18, max: 26 };
      if (age < 50) return { min: 20, max: 28 };
      return { min: 22, max: 30 };
    }
  };

  const reset = () => {
    setGender("male");
    setAge(30);
    setWeight(70);
    setHeight(175);
    setNeck(37);
    setWaist(85);
    setHip(95);
    setTricep(15);
    setSubscapular(18);
    setSuprailiac(20);
    setNavyResult(null);
    setSkinfoldResult(null);
  };

  const formatNumber = (num: number, decimals: number = 1): string => {
    return num.toFixed(decimals);
  };

  const getCategoryColor = (category: string): string => {
    switch (category) {
      case "Essential Fat": return "blue";
      case "Athletes": return "green";
      case "Fitness": return "green";
      case "Average": return "yellow";
      case "Obese": return "red";
      default: return "gray";
    }
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Body Fat Calculator</CardTitle>
        </CardHeader>
        <CardContent>
          {/* Common Inputs */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <div>
              <Label htmlFor="gender">Gender</Label>
              <Select value={gender} onValueChange={setGender}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="male">Male</SelectItem>
                  <SelectItem value="female">Female</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="unit">Unit System</Label>
              <Select value={unit} onValueChange={setUnit}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="metric">Metric</SelectItem>
                  <SelectItem value="imperial">Imperial</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="age">Age</Label>
              <Input
                id="age"
                type="number"
                value={age}
                onChange={(e) => setAge(parseInt(e.target.value) || 0)}
                min="10"
                max="100"
              />
            </div>

            <div>
              <Label htmlFor="weight">Weight ({unit === "metric" ? "kg" : "lbs"})</Label>
              <Input
                id="weight"
                type="number"
                value={weight}
                onChange={(e) => setWeight(parseFloat(e.target.value) || 0)}
                min="0"
                step="0.1"
              />
            </div>
          </div>

          <Tabs defaultValue="navy" className="space-y-6">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="navy">Navy Method</TabsTrigger>
              <TabsTrigger value="skinfold">Skinfold Method</TabsTrigger>
            </TabsList>

            {/* Navy Method */}
            <TabsContent value="navy" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                  <Label htmlFor="height">Height ({unit === "metric" ? "cm" : "inches"})</Label>
                  <Input
                    id="height"
                    type="number"
                    value={height}
                    onChange={(e) => setHeight(parseFloat(e.target.value) || 0)}
                    min="0"
                    step="0.1"
                  />
                </div>

                <div>
                  <Label htmlFor="neck">Neck ({unit === "metric" ? "cm" : "inches"})</Label>
                  <Input
                    id="neck"
                    type="number"
                    value={neck}
                    onChange={(e) => setNeck(parseFloat(e.target.value) || 0)}
                    min="0"
                    step="0.1"
                  />
                </div>

                <div>
                  <Label htmlFor="waist">Waist ({unit === "metric" ? "cm" : "inches"})</Label>
                  <Input
                    id="waist"
                    type="number"
                    value={waist}
                    onChange={(e) => setWaist(parseFloat(e.target.value) || 0)}
                    min="0"
                    step="0.1"
                  />
                </div>

                {gender === "female" && (
                  <div>
                    <Label htmlFor="hip">Hip ({unit === "metric" ? "cm" : "inches"})</Label>
                    <Input
                      id="hip"
                      type="number"
                      value={hip}
                      onChange={(e) => setHip(parseFloat(e.target.value) || 0)}
                      min="0"
                      step="0.1"
                    />
                  </div>
                )}
              </div>

              <Button onClick={calculateNavyMethod} className="w-full">
                Calculate Body Fat (Navy Method)
              </Button>

              {navyResult && (
                <div className="space-y-4">
                  <Card className={`bg-${getCategoryColor(navyResult.category)}-50 dark:bg-${getCategoryColor(navyResult.category)}-900/20`}>
                    <CardContent className="pt-6">
                      <div className="text-center space-y-2">
                        <div className="text-sm text-muted-foreground">Body Fat Percentage</div>
                        <div className={`text-3xl font-bold text-${getCategoryColor(navyResult.category)}-600 dark:text-${getCategoryColor(navyResult.category)}-400`}>
                          {formatNumber(navyResult.bodyFatPercentage)}%
                        </div>
                        <div className="text-lg font-semibold">{navyResult.category}</div>
                      </div>
                    </CardContent>
                  </Card>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <Card>
                      <CardContent className="pt-6">
                        <div className="text-center space-y-2">
                          <div className="text-sm text-muted-foreground">Fat Mass</div>
                          <div className="text-xl font-bold">
                            {formatNumber(convertFromMetric(navyResult.fatMass, "weight"))} {unit === "metric" ? "kg" : "lbs"}
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardContent className="pt-6">
                        <div className="text-center space-y-2">
                          <div className="text-sm text-muted-foreground">Lean Body Mass</div>
                          <div className="text-xl font-bold">
                            {formatNumber(convertFromMetric(navyResult.leanBodyMass, "weight"))} {unit === "metric" ? "kg" : "lbs"}
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardContent className="pt-6">
                        <div className="text-center space-y-2">
                          <div className="text-sm text-muted-foreground">Ideal Range</div>
                          <div className="text-xl font-bold">
                            {navyResult.idealBodyFat.min}% - {navyResult.idealBodyFat.max}%
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </div>
              )}
            </TabsContent>

            {/* Skinfold Method */}
            <TabsContent value="skinfold" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="tricep">Tricep Skinfold (mm)</Label>
                  <Input
                    id="tricep"
                    type="number"
                    value={tricep}
                    onChange={(e) => setTricep(parseFloat(e.target.value) || 0)}
                    min="0"
                    step="0.1"
                  />
                </div>

                <div>
                  <Label htmlFor="subscapular">Subscapular Skinfold (mm)</Label>
                  <Input
                    id="subscapular"
                    type="number"
                    value={subscapular}
                    onChange={(e) => setSubscapular(parseFloat(e.target.value) || 0)}
                    min="0"
                    step="0.1"
                  />
                </div>

                <div>
                  <Label htmlFor="suprailiac">Suprailiac Skinfold (mm)</Label>
                  <Input
                    id="suprailiac"
                    type="number"
                    value={suprailiac}
                    onChange={(e) => setSuprailiac(parseFloat(e.target.value) || 0)}
                    min="0"
                    step="0.1"
                  />
                </div>
              </div>

              <Button onClick={calculateSkinfoldMethod} className="w-full">
                Calculate Body Fat (Skinfold Method)
              </Button>

              {skinfoldResult && (
                <div className="space-y-4">
                  <Card className={`bg-${getCategoryColor(skinfoldResult.category)}-50 dark:bg-${getCategoryColor(skinfoldResult.category)}-900/20`}>
                    <CardContent className="pt-6">
                      <div className="text-center space-y-2">
                        <div className="text-sm text-muted-foreground">Body Fat Percentage</div>
                        <div className={`text-3xl font-bold text-${getCategoryColor(skinfoldResult.category)}-600 dark:text-${getCategoryColor(skinfoldResult.category)}-400`}>
                          {formatNumber(skinfoldResult.bodyFatPercentage)}%
                        </div>
                        <div className="text-lg font-semibold">{skinfoldResult.category}</div>
                      </div>
                    </CardContent>
                  </Card>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <Card>
                      <CardContent className="pt-6">
                        <div className="text-center space-y-2">
                          <div className="text-sm text-muted-foreground">Fat Mass</div>
                          <div className="text-xl font-bold">
                            {formatNumber(convertFromMetric(skinfoldResult.fatMass, "weight"))} {unit === "metric" ? "kg" : "lbs"}
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardContent className="pt-6">
                        <div className="text-center space-y-2">
                          <div className="text-sm text-muted-foreground">Lean Body Mass</div>
                          <div className="text-xl font-bold">
                            {formatNumber(convertFromMetric(skinfoldResult.leanBodyMass, "weight"))} {unit === "metric" ? "kg" : "lbs"}
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardContent className="pt-6">
                        <div className="text-center space-y-2">
                          <div className="text-sm text-muted-foreground">Ideal Range</div>
                          <div className="text-xl font-bold">
                            {skinfoldResult.idealBodyFat.min}% - {skinfoldResult.idealBodyFat.max}%
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </div>
              )}
            </TabsContent>
          </Tabs>

          <div className="flex gap-4 mt-6">
            <Button onClick={reset} variant="outline" className="flex-1">
              Reset All
            </Button>
          </div>

          {/* Tips */}
          <div className="p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
            <h3 className="font-semibold mb-2">Body Fat Measurement Tips</h3>
            <ul className="text-sm space-y-1">
              <li>• Navy method is more accessible but less precise than skinfold</li>
              <li>• Skinfold method requires calipers and proper technique</li>
              <li>• Take measurements at the same time of day for consistency</li>
              <li>• Body fat percentage naturally increases with age</li>
              <li>• Consider DEXA scan for most accurate measurement</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
