"use client";

import { useState, useEffect, useCallback } from "react";
import Image from "next/image";
import { motion } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import {
  Save,
  RotateCcw,
  Upload,
  Eye,
  Globe,
  Search,
  Share2,
  Twitter,
  Facebook,
  Settings,
  HelpCircle,
  CheckCircle,
  AlertCircle
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface SEOSettings {
  siteTitle: string;
  siteDescription: string;
  metaKeywords: string;
  defaultShareImage: string;
  robotsAllowed: boolean;
  canonicalUrl: string;
  ogTitle: string;
  ogDescription: string;
  twitterCardType: string;
  twitterSite: string;
  favicon: string;
  sitemapEnabled: boolean;
  googleAnalyticsId: string;
  googleSearchConsoleId: string;
}

const defaultSettings: SEOSettings = {
  siteTitle: "ToolBox - PDF Tools & Calculators",
  siteDescription: "Professional PDF tools and calculators for all your document needs. Convert, compress, merge, and split PDFs with ease.",
  metaKeywords: "PDF tools, PDF converter, PDF compressor, calculators, document tools",
  defaultShareImage: "",
  robotsAllowed: true,
  canonicalUrl: "https://toolbox.com",
  ogTitle: "",
  ogDescription: "",
  twitterCardType: "summary_large_image",
  twitterSite: "@toolbox",
  favicon: "",
  sitemapEnabled: true,
  googleAnalyticsId: "",
  googleSearchConsoleId: ""
};

export default function SEOSettingsPage() {
  const [settings, setSettings] = useState<SEOSettings>(defaultSettings);
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const { toast } = useToast();

  const loadSettings = useCallback(async () => {
    setIsLoading(true);
    try {
      const response = await fetch("/api/seo-settings");
      if (response.ok) {
        const data = await response.json();
        setSettings({ ...defaultSettings, ...data });
      }
    } catch (error) {
      console.error("Failed to load SEO settings:", error);
      toast({
        title: "Error",
        description: "Failed to load SEO settings",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [toast]);

  // Load settings on component mount
  useEffect(() => {
    loadSettings();
  }, [loadSettings]);

  const saveSettings = async () => {
    setIsSaving(true);
    try {
      console.log("Saving SEO settings:", settings); // Debug log
      const response = await fetch("/api/seo-settings", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(settings),
      });

      if (response.ok) {
        const result = await response.json();
        console.log("Save successful:", result); // Debug log
        toast({
          title: "Success",
          description: "SEO settings saved successfully",
        });
        // Reload settings to ensure UI is in sync
        await loadSettings();
      } else {
        const errorData = await response.json();
        console.error("Save failed:", errorData); // Debug log
        throw new Error(errorData.error || "Failed to save settings");
      }
    } catch (error) {
      console.error("Failed to save SEO settings:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to save SEO settings",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  const resetSettings = () => {
    setSettings(defaultSettings);
    toast({
      title: "Settings Reset",
      description: "SEO settings have been reset to defaults",
    });
  };

  const handleInputChange = (field: keyof SEOSettings, value: string | boolean) => {
    setSettings(prev => {
      const newSettings = {
        ...prev,
        [field]: value
      };
      console.log(`Updated ${field}:`, value); // Debug log
      return newSettings;
    });
  };

  const handleFileUpload = async (field: 'defaultShareImage' | 'favicon', file: File) => {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('type', field);

    try {
      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
      });

      if (response.ok) {
        const data = await response.json();
        handleInputChange(field, data.url);
        toast({
          title: "Success",
          description: `${field === 'favicon' ? 'Favicon' : 'Share image'} uploaded successfully`,
        });
      } else {
        throw new Error('Upload failed');
      }
    } catch (error) {
      console.error('Upload error:', error);
      toast({
        title: "Error",
        description: "Failed to upload file",
        variant: "destructive",
      });
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="flex items-center justify-between mb-8"
      >
        <div>
          <h1 className="text-3xl font-bold text-foreground flex items-center gap-2">
            <Settings className="h-8 w-8" />
            SEO Settings
          </h1>
          <p className="text-muted-foreground mt-2">
            Configure your website's search engine optimization settings
          </p>
        </div>

        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={resetSettings}
            disabled={isSaving}
          >
            <RotateCcw className="h-4 w-4 mr-2" />
            Reset
          </Button>
          <Button
            onClick={saveSettings}
            disabled={isSaving}
          >
            {isSaving ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Saving...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Save Changes
              </>
            )}
          </Button>
        </div>
      </motion.div>

      {/* SEO Preview */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.1 }}
        className="mb-8"
      >
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Eye className="h-5 w-5" />
              Search Engine Preview
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="border rounded-lg p-4 bg-muted/50">
              <div className="text-blue-600 text-lg font-medium hover:underline cursor-pointer">
                {settings.siteTitle || "Your Site Title"}
              </div>
              <div className="text-green-700 text-sm">
                {settings.canonicalUrl || "https://yoursite.com"}
              </div>
              <div className="text-gray-600 text-sm mt-1">
                {settings.siteDescription || "Your site description will appear here..."}
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Settings Tabs */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        <Tabs defaultValue="basic" className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="basic" className="flex items-center gap-2">
              <Search className="h-4 w-4" />
              Basic SEO
            </TabsTrigger>
            <TabsTrigger value="social" className="flex items-center gap-2">
              <Share2 className="h-4 w-4" />
              Social Media
            </TabsTrigger>
            <TabsTrigger value="advanced" className="flex items-center gap-2">
              <Globe className="h-4 w-4" />
              Advanced
            </TabsTrigger>
          </TabsList>

          {/* Basic SEO Tab */}
          <TabsContent value="basic" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Basic Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="siteTitle">Site Title</Label>
                  <Input
                    id="siteTitle"
                    value={settings.siteTitle}
                    onChange={(e) => handleInputChange('siteTitle', e.target.value)}
                    placeholder="Your website title"
                  />
                  <p className="text-xs text-muted-foreground">
                    Recommended: 50-60 characters
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="siteDescription">Site Description</Label>
                  <Textarea
                    id="siteDescription"
                    value={settings.siteDescription}
                    onChange={(e) => handleInputChange('siteDescription', e.target.value)}
                    placeholder="A brief description of your website"
                    rows={3}
                  />
                  <p className="text-xs text-muted-foreground">
                    Recommended: 150-160 characters
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="metaKeywords">Meta Keywords</Label>
                  <Input
                    id="metaKeywords"
                    value={settings.metaKeywords}
                    onChange={(e) => handleInputChange('metaKeywords', e.target.value)}
                    placeholder="keyword1, keyword2, keyword3"
                  />
                  <p className="text-xs text-muted-foreground">
                    Separate keywords with commas
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="canonicalUrl">Canonical URL</Label>
                  <Input
                    id="canonicalUrl"
                    value={settings.canonicalUrl}
                    onChange={(e) => handleInputChange('canonicalUrl', e.target.value)}
                    placeholder="https://yoursite.com"
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Social Media Tab */}
          <TabsContent value="social" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Facebook className="h-5 w-5" />
                  Open Graph (Facebook)
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="ogTitle">Open Graph Title</Label>
                  <Input
                    id="ogTitle"
                    value={settings.ogTitle}
                    onChange={(e) => handleInputChange('ogTitle', e.target.value)}
                    placeholder="Leave empty to use site title"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="ogDescription">Open Graph Description</Label>
                  <Textarea
                    id="ogDescription"
                    value={settings.ogDescription}
                    onChange={(e) => handleInputChange('ogDescription', e.target.value)}
                    placeholder="Leave empty to use site description"
                    rows={3}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="defaultShareImage">Default Share Image</Label>
                  <div className="flex items-center gap-4">
                    <Input
                      id="defaultShareImage"
                      value={settings.defaultShareImage}
                      onChange={(e) => handleInputChange('defaultShareImage', e.target.value)}
                      placeholder="https://yoursite.com/share-image.jpg"
                    />
                    <Button
                      variant="outline"
                      onClick={() => {
                        const input = document.createElement('input');
                        input.type = 'file';
                        input.accept = 'image/*';
                        input.onchange = (e) => {
                          const file = (e.target as HTMLInputElement).files?.[0];
                          if (file) handleFileUpload('defaultShareImage', file);
                        };
                        input.click();
                      }}
                    >
                      <Upload className="h-4 w-4 mr-2" />
                      Upload
                    </Button>
                  </div>
                  {settings.defaultShareImage && (
                    <div className="mt-2">
                      <Image
                        src={settings.defaultShareImage}
                        alt="Share preview"
                        width={128}
                        height={80}
                        className="object-cover rounded border"
                      />
                    </div>
                  )}
                  <p className="text-xs text-muted-foreground">
                    Recommended: 1200x630 pixels
                  </p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Twitter className="h-5 w-5" />
                  Twitter Cards
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="twitterCardType">Card Type</Label>
                  <select
                    id="twitterCardType"
                    value={settings.twitterCardType}
                    onChange={(e) => handleInputChange('twitterCardType', e.target.value)}
                    className="w-full p-2 border rounded-md"
                  >
                    <option value="summary">Summary</option>
                    <option value="summary_large_image">Summary Large Image</option>
                  </select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="twitterSite">Twitter Site Handle</Label>
                  <Input
                    id="twitterSite"
                    value={settings.twitterSite}
                    onChange={(e) => handleInputChange('twitterSite', e.target.value)}
                    placeholder="@yoursite"
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Advanced Tab */}
          <TabsContent value="advanced" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Search Engine Settings</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Allow Search Engine Indexing</Label>
                    <p className="text-xs text-muted-foreground">
                      Controls robots.txt behavior
                    </p>
                  </div>
                  <Switch
                    checked={settings.robotsAllowed}
                    onCheckedChange={(checked) => handleInputChange('robotsAllowed', checked)}
                  />
                </div>

                <Separator />

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Enable XML Sitemap</Label>
                    <p className="text-xs text-muted-foreground">
                      Automatically generate sitemap.xml
                    </p>
                  </div>
                  <Switch
                    checked={settings.sitemapEnabled}
                    onCheckedChange={(checked) => handleInputChange('sitemapEnabled', checked)}
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Analytics & Tracking</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="googleAnalyticsId">Google Analytics ID</Label>
                  <Input
                    id="googleAnalyticsId"
                    value={settings.googleAnalyticsId}
                    onChange={(e) => handleInputChange('googleAnalyticsId', e.target.value)}
                    placeholder="G-XXXXXXXXXX"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="googleSearchConsoleId">Google Search Console ID</Label>
                  <Input
                    id="googleSearchConsoleId"
                    value={settings.googleSearchConsoleId}
                    onChange={(e) => handleInputChange('googleSearchConsoleId', e.target.value)}
                    placeholder="google-site-verification=..."
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Favicon</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="favicon">Favicon URL</Label>
                  <div className="flex items-center gap-4">
                    <Input
                      id="favicon"
                      value={settings.favicon}
                      onChange={(e) => handleInputChange('favicon', e.target.value)}
                      placeholder="https://yoursite.com/favicon.ico"
                    />
                    <Button
                      variant="outline"
                      onClick={() => {
                        const input = document.createElement('input');
                        input.type = 'file';
                        input.accept = 'image/*,.ico';
                        input.onchange = (e) => {
                          const file = (e.target as HTMLInputElement).files?.[0];
                          if (file) handleFileUpload('favicon', file);
                        };
                        input.click();
                      }}
                    >
                      <Upload className="h-4 w-4 mr-2" />
                      Upload
                    </Button>
                  </div>
                  {settings.favicon && (
                    <div className="mt-2">
                      <Image
                        src={settings.favicon}
                        alt="Favicon preview"
                        width={32}
                        height={32}
                        className="rounded border"
                      />
                    </div>
                  )}
                  <p className="text-xs text-muted-foreground">
                    Recommended: 32x32 pixels, .ico format
                  </p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </motion.div>
    </div>
  );
}
