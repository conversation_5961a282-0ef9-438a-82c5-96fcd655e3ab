# 🔧 ToolRapter Environment Setup Guide

## 📋 Overview

This guide provides step-by-step instructions for setting up ToolRapter development, staging, and production environments with enterprise-grade security and performance standards.

## 🎯 Environment Types

### Development Environment
- **Purpose**: Local development and testing
- **URL**: http://localhost:3000
- **Database**: Local MongoDB or MongoDB Atlas
- **Security**: Basic development settings
- **Performance**: Hot reload enabled

### Staging Environment
- **Purpose**: Pre-production testing
- **URL**: https://staging.toolrapter.com
- **Database**: Staging MongoDB Atlas
- **Security**: Production-like security
- **Performance**: Production optimizations

### Production Environment
- **Purpose**: Live application
- **URL**: https://toolrapter.com
- **Database**: Production MongoDB Atlas
- **Security**: Enterprise-grade security
- **Performance**: Full optimizations

## 🚀 Quick Setup

Use the automated setup script for any environment:

```bash
# Development environment
./scripts/setup-environment.sh development

# Staging environment
./scripts/setup-environment.sh staging

# Production environment
./scripts/setup-environment.sh production
```

## 📋 Prerequisites

### System Requirements
- **Node.js**: Version 18.x or higher
- **npm**: Version 8.x or higher
- **Git**: Latest version
- **MongoDB**: Local installation or Atlas account

### Development Tools (Recommended)
- **VS Code**: With TypeScript and ESLint extensions
- **MongoDB Compass**: For database management
- **Postman**: For API testing
- **Chrome DevTools**: For debugging

## 🔧 Manual Setup Instructions

### 1. Clone Repository
```bash
git clone https://github.com/MuhammadShahbaz195/ToolCrush.git
cd ToolCrush
```

### 2. Install Dependencies
```bash
# Install all dependencies
npm install

# Or for production (dependencies only)
npm ci --production
```

### 3. Environment Configuration

#### Development Environment (.env.local)
```env
# Environment
NODE_ENV=development
NEXT_PUBLIC_BASE_URL=http://localhost:3000
NEXTAUTH_URL=http://localhost:3000

# Security (Generate secure secrets)
NEXTAUTH_SECRET=your-super-secure-secret-key-minimum-32-characters
CSRF_SECRET=your-csrf-secret-key-minimum-32-characters

# Database
MONGODB_URI=mongodb://localhost:27017/toolrapter-dev
# Or MongoDB Atlas:
# MONGODB_URI=mongodb+srv://username:<EMAIL>/toolrapter-dev

# Authentication (Optional for development)
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# Email (Optional for development)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
SMTP_FROM=<EMAIL>
```

#### Staging Environment (.env.staging)
```env
# Environment
NODE_ENV=production
NEXT_PUBLIC_BASE_URL=https://staging.toolrapter.com
NEXTAUTH_URL=https://staging.toolrapter.com

# Security (Use strong secrets)
NEXTAUTH_SECRET=staging-super-secure-secret-key-minimum-32-characters
CSRF_SECRET=staging-csrf-secret-key-minimum-32-characters

# Database
MONGODB_URI=mongodb+srv://username:<EMAIL>/toolrapter-staging

# Authentication
GOOGLE_CLIENT_ID=your-staging-google-client-id
GOOGLE_CLIENT_SECRET=your-staging-google-client-secret

# Email
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-staging-app-password
SMTP_FROM=<EMAIL>
```

#### Production Environment (.env.production)
```env
# Environment
NODE_ENV=production
NEXT_PUBLIC_BASE_URL=https://toolrapter.com
NEXTAUTH_URL=https://toolrapter.com

# Security (Use enterprise-grade secrets)
NEXTAUTH_SECRET=production-super-secure-secret-key-minimum-32-characters
CSRF_SECRET=production-csrf-secret-key-minimum-32-characters

# Database
MONGODB_URI=mongodb+srv://username:<EMAIL>/toolrapter-production

# Authentication
GOOGLE_CLIENT_ID=your-production-google-client-id
GOOGLE_CLIENT_SECRET=your-production-google-client-secret

# Email
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-production-app-password
SMTP_FROM=<EMAIL>
```

### 4. Download Required Fonts
```bash
npm run download-fonts
```

### 5. Database Setup

#### Local MongoDB
```bash
# Install MongoDB (Ubuntu/Debian)
wget -qO - https://www.mongodb.org/static/pgp/server-6.0.asc | sudo apt-key add -
echo "deb [ arch=amd64,arm64 ] https://repo.mongodb.org/apt/ubuntu focal/mongodb-org/6.0 multiverse" | sudo tee /etc/apt/sources.list.d/mongodb-org-6.0.list
sudo apt-get update
sudo apt-get install -y mongodb-org
sudo systemctl start mongod
sudo systemctl enable mongod
```

#### MongoDB Atlas
1. Create account at [MongoDB Atlas](https://www.mongodb.com/atlas)
2. Create new cluster
3. Create database user
4. Whitelist IP addresses
5. Get connection string

### 6. Google OAuth Setup (Optional)
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create new project or select existing
3. Enable Google+ API
4. Create OAuth 2.0 credentials
5. Add authorized redirect URIs:
   - Development: `http://localhost:3000/api/auth/callback/google`
   - Staging: `https://staging.toolrapter.com/api/auth/callback/google`
   - Production: `https://toolrapter.com/api/auth/callback/google`

### 7. Email Configuration (Optional)
For Gmail SMTP:
1. Enable 2-factor authentication
2. Generate app-specific password
3. Use app password in SMTP_PASS

## 🏃‍♂️ Running the Application

### Development Mode
```bash
npm run dev
```
Access at: http://localhost:3000

### Production Build
```bash
npm run build
npm start
```

### Type Checking
```bash
npm run type-check
```

### Linting
```bash
npm run lint
npm run lint:fix
```

## 🔐 Security Configuration

### Secret Generation
Generate secure secrets using:
```bash
# Using OpenSSL
openssl rand -base64 32

# Using Node.js
node -e "console.log(require('crypto').randomBytes(32).toString('base64'))"
```

### Environment Security Checklist
- [ ] All secrets are unique and secure (minimum 32 characters)
- [ ] No default or example values in production
- [ ] Environment files are not committed to Git
- [ ] Database credentials are secure
- [ ] OAuth credentials are properly configured
- [ ] SMTP credentials are secure

## 📊 Performance Configuration

### Development Performance
- Hot reload enabled
- Source maps enabled
- Detailed error messages
- Development-only packages included

### Production Performance
- Minification enabled
- Source maps disabled (or source-map for debugging)
- Console logs removed
- Production optimizations enabled
- Bundle analysis available

### Performance Monitoring
```bash
# Run performance analysis
node scripts/optimize-performance.js

# Build with bundle analysis
npm run analyze

# Test build performance
node scripts/optimize-performance.js --build-test
```

## 🧪 Testing Configuration

### Unit Tests
```bash
npm run test
npm run test:watch
npm run test:coverage
```

### E2E Tests
```bash
npm run test:e2e
```

### Type Checking
```bash
npm run type-check
npm run type-check:watch
```

## 🔍 Debugging

### Development Debugging
- Use browser DevTools
- Enable React DevTools
- Check console for errors
- Use VS Code debugger

### Production Debugging
- Check PM2 logs: `pm2 logs`
- Monitor performance: `/api/admin/performance`
- Check security events: `/api/admin/security`
- Review Nginx logs

## 🚨 Troubleshooting

### Common Issues

#### Build Errors
```bash
# Clear cache and reinstall
rm -rf .next node_modules package-lock.json
npm install
npm run build
```

#### Database Connection Issues
- Check MongoDB URI format
- Verify database credentials
- Check network connectivity
- Whitelist IP addresses (Atlas)

#### Authentication Issues
- Verify OAuth credentials
- Check redirect URIs
- Ensure NEXTAUTH_SECRET is set
- Check NEXTAUTH_URL matches domain

#### Performance Issues
- Run performance analysis
- Check bundle size
- Monitor memory usage
- Review caching strategy

### Environment-Specific Issues

#### Development
- Port 3000 already in use: `lsof -ti:3000 | xargs kill -9`
- Hot reload not working: Restart dev server
- TypeScript errors: Run `npm run type-check`

#### Staging/Production
- Build failures: Check Node.js version and dependencies
- SSL issues: Verify certificate configuration
- Performance issues: Check server resources

## 📞 Support

### Getting Help
1. Check this documentation
2. Review troubleshooting section
3. Check application logs
4. Contact development team

### Useful Commands
```bash
# Check environment
npm run env:check

# Health check
npm run health:check

# Performance test
npm run perf:test

# Security audit
npm audit

# Update dependencies
npm update
```

## 🔄 Maintenance

### Regular Tasks
- **Daily**: Check application health
- **Weekly**: Update dependencies
- **Monthly**: Security audit
- **Quarterly**: Performance review

### Environment Updates
1. Test in development
2. Deploy to staging
3. Validate staging environment
4. Deploy to production
5. Monitor production deployment
