# 🔐 Converter Authentication Update Template

## Required Changes for Each Converter

### 1. Add Import
```typescript
import { useProtectedDownload } from "@/hooks/useProtectedDownload";
```

### 2. Add Hook Implementation
```typescript
// Protected download hook
const {
  isAuthenticated,
  isLoading: authLoading,
  isDownloading,
  initiateDownload,
  processPendingDownload,
  canDownload,
  needsAuth
} = useProtectedDownload({
  requireAuth: true,
  onAuthRequired: () => {
    setError("Please log in to download converted files. You can still upload and convert files without logging in.");
  },
  onDownloadStart: (data) => {
    console.log("Starting download:", data);
  },
  onDownloadComplete: (data) => {
    console.log("Download completed:", data);
  },
  onError: (errorMsg) => {
    setError(`Download failed: ${errorMsg}`);
  }
});
```

### 3. Add useEffect for Pending Downloads
```typescript
// Process pending downloads after login
useEffect(() => {
  if (isAuthenticated) {
    processPendingDownload();
  }
}, [isAuthenticated, processPendingDownload]);
```

### 4. Replace handleDownload Function
```typescript
// OLD (Direct download):
const handleDownload = () => {
  if (convertedFileUrl) {
    const link = document.createElement("a");
    link.href = convertedFileUrl;
    link.download = "filename.ext";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
};

// NEW (Protected download):
const handleDownload = async () => {
  if (convertedFileUrl && file) {
    const downloadData = {
      fileName: file.name.replace(/\.[^/.]+$/, "") + "_converted.ext",
      conversionType: "tool-name",
      originalFileSize: file.size,
      convertedFileSize: 0, // Will be calculated
      toolName: "Tool Name"
    };
    
    await initiateDownload(convertedFileUrl, downloadData);
  }
};
```

### 5. Update Download Button
```typescript
// Update button to show authentication state
<button
  onClick={handleDownload}
  disabled={!convertedFileUrl || isDownloading || (needsAuth && authLoading)}
  className="w-full py-2 px-4 bg-green-600 hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white rounded-md font-medium flex items-center justify-center"
>
  {isDownloading ? (
    <>
      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
      Downloading...
    </>
  ) : needsAuth ? (
    "Login to Download"
  ) : (
    <>
      <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
      </svg>
      Download File
    </>
  )}
</button>
```

## Converters to Update (Priority Order)

### High Priority (Real Functionality)
1. ✅ ExcelToPdfConverter.tsx (Already updated)
2. ❌ WordToPdfConverter.tsx
3. ❌ PdfToWordConverter.tsx
4. ❌ PdfToExcelConverter.tsx
5. ❌ MergePdfConverter.tsx
6. ❌ SplitPdfConverter.tsx
7. ❌ CompressPdfConverter.tsx
8. ❌ RotatePdfConverter.tsx

### Medium Priority
9. ❌ JpgToPdfConverter.tsx
10. ❌ PdfToJpgConverter.tsx
11. ❌ HtmlToPdfConverter.tsx
12. ❌ PowerPointToPdfConverter.tsx
13. ❌ PdfToPowerPointConverter.tsx

### Lower Priority
14. ❌ AddWatermarkConverter.tsx
15. ❌ ProtectPdfConverter.tsx
16. ❌ PdfToPdfaConverter.tsx (Returns 501)

## Testing Checklist for Each Updated Converter

### Before Login:
- [ ] Can upload files
- [ ] Can start conversion
- [ ] Download button shows "Login to Download"
- [ ] Clicking download redirects to login

### After Login:
- [ ] Download button shows "Download [File Type]"
- [ ] Download works correctly
- [ ] File is valid and not corrupted
- [ ] Download is tracked in MongoDB

### Error Handling:
- [ ] Shows appropriate error messages
- [ ] Handles authentication failures gracefully
- [ ] Preserves pending downloads during login flow

## Implementation Strategy

1. **Update 2-3 converters at a time** to avoid overwhelming changes
2. **Test each batch** before proceeding to the next
3. **Start with high-priority tools** that have real functionality
4. **Verify authentication flow** works correctly for each tool
5. **Check download tracking** is working in MongoDB
