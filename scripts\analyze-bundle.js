#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

/**
 * Bundle Size Analyzer
 * Analyzes Next.js build output and provides bundle size insights
 */

const BUNDLE_SIZE_LIMITS = {
  'First Load JS': 250 * 1024, // 250KB
  'framework': 150 * 1024,     // 150KB
  'main': 50 * 1024,           // 50KB
  'vendors': 200 * 1024,       // 200KB
  'pdf-libs': 300 * 1024,      // 300KB
  'ui-libs': 100 * 1024,       // 100KB
  'heavy-utils': 250 * 1024,   // 250KB
};

function formatBytes(bytes) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function parseNextBuildOutput() {
  try {
    // Run Next.js build and capture output
    console.log('🔍 Building application for analysis...');
    const buildOutput = execSync('npm run build', { 
      encoding: 'utf8',
      stdio: 'pipe'
    });

    // Parse the build output for bundle information
    const lines = buildOutput.split('\n');
    const bundleInfo = [];
    let inBundleSection = false;

    for (const line of lines) {
      if (line.includes('Route (app)') || line.includes('Size')) {
        inBundleSection = true;
        continue;
      }

      if (inBundleSection && line.trim()) {
        // Parse bundle size information
        const match = line.match(/([^\s]+)\s+(\d+(?:\.\d+)?)\s*(B|kB|MB)\s+(\d+(?:\.\d+)?)\s*(B|kB|MB)/);
        if (match) {
          const [, route, size, sizeUnit, firstLoad, firstLoadUnit] = match;
          bundleInfo.push({
            route: route.trim(),
            size: parseSize(size, sizeUnit),
            firstLoad: parseSize(firstLoad, firstLoadUnit)
          });
        }
      }
    }

    return bundleInfo;
  } catch (error) {
    console.error('❌ Failed to build application:', error.message);
    return [];
  }
}

function parseSize(value, unit) {
  const num = parseFloat(value);
  switch (unit) {
    case 'B': return num;
    case 'kB': return num * 1024;
    case 'MB': return num * 1024 * 1024;
    default: return num;
  }
}

function analyzeBundleFiles() {
  const buildDir = path.join(process.cwd(), '.next');
  const staticDir = path.join(buildDir, 'static');
  
  if (!fs.existsSync(staticDir)) {
    console.log('⚠️  No build directory found. Run npm run build first.');
    return {};
  }

  const analysis = {
    chunks: [],
    totalSize: 0,
    warnings: []
  };

  // Analyze JavaScript chunks
  const chunksDir = path.join(staticDir, 'chunks');
  if (fs.existsSync(chunksDir)) {
    const chunkFiles = fs.readdirSync(chunksDir).filter(file => file.endsWith('.js'));
    
    for (const file of chunkFiles) {
      const filePath = path.join(chunksDir, file);
      const stats = fs.statSync(filePath);
      const size = stats.size;
      
      analysis.chunks.push({
        name: file,
        size: size,
        type: getChunkType(file)
      });
      
      analysis.totalSize += size;
      
      // Check against limits
      const chunkType = getChunkType(file);
      const limit = BUNDLE_SIZE_LIMITS[chunkType];
      if (limit && size > limit) {
        analysis.warnings.push({
          type: 'size_exceeded',
          chunk: file,
          size: size,
          limit: limit,
          message: `${file} (${formatBytes(size)}) exceeds limit (${formatBytes(limit)})`
        });
      }
    }
  }

  return analysis;
}

function getChunkType(filename) {
  if (filename.includes('framework')) return 'framework';
  if (filename.includes('main')) return 'main';
  if (filename.includes('vendors')) return 'vendors';
  if (filename.includes('pdf-libs')) return 'pdf-libs';
  if (filename.includes('ui-libs')) return 'ui-libs';
  if (filename.includes('heavy-utils')) return 'heavy-utils';
  return 'other';
}

function generateReport(bundleInfo, analysis) {
  console.log('\n📊 Bundle Size Analysis Report');
  console.log('================================\n');

  // Overall statistics
  console.log('📈 Overall Statistics:');
  console.log(`Total Bundle Size: ${formatBytes(analysis.totalSize)}`);
  console.log(`Number of Chunks: ${analysis.chunks.length}`);
  console.log(`Target Bundle Size: ${formatBytes(1.5 * 1024 * 1024)} (1.5MB)\n`);

  // Chunk breakdown
  console.log('📦 Chunk Breakdown:');
  const sortedChunks = analysis.chunks.sort((a, b) => b.size - a.size);
  
  for (const chunk of sortedChunks.slice(0, 10)) {
    const percentage = ((chunk.size / analysis.totalSize) * 100).toFixed(1);
    console.log(`  ${chunk.name}: ${formatBytes(chunk.size)} (${percentage}%)`);
  }

  if (sortedChunks.length > 10) {
    console.log(`  ... and ${sortedChunks.length - 10} more chunks\n`);
  } else {
    console.log('');
  }

  // Route analysis
  if (bundleInfo.length > 0) {
    console.log('🛣️  Route Analysis:');
    const sortedRoutes = bundleInfo.sort((a, b) => b.firstLoad - a.firstLoad);
    
    for (const route of sortedRoutes.slice(0, 10)) {
      console.log(`  ${route.route}: ${formatBytes(route.firstLoad)} first load`);
    }
    console.log('');
  }

  // Warnings
  if (analysis.warnings.length > 0) {
    console.log('⚠️  Warnings:');
    for (const warning of analysis.warnings) {
      console.log(`  ${warning.message}`);
    }
    console.log('');
  }

  // Recommendations
  console.log('💡 Recommendations:');
  
  if (analysis.totalSize > 1.5 * 1024 * 1024) {
    console.log('  • Bundle size exceeds 1.5MB target. Consider:');
    console.log('    - Dynamic imports for heavy components');
    console.log('    - Tree shaking unused dependencies');
    console.log('    - Code splitting by route');
  }
  
  const largeChunks = analysis.chunks.filter(chunk => chunk.size > 100 * 1024);
  if (largeChunks.length > 0) {
    console.log('  • Large chunks detected. Consider splitting:');
    largeChunks.forEach(chunk => {
      console.log(`    - ${chunk.name} (${formatBytes(chunk.size)})`);
    });
  }
  
  if (analysis.warnings.length === 0 && analysis.totalSize <= 1.5 * 1024 * 1024) {
    console.log('  ✅ Bundle size is within acceptable limits!');
  }
  
  console.log('');
}

function saveReport(bundleInfo, analysis) {
  const report = {
    timestamp: new Date().toISOString(),
    totalSize: analysis.totalSize,
    chunks: analysis.chunks,
    routes: bundleInfo,
    warnings: analysis.warnings,
    summary: {
      targetMet: analysis.totalSize <= 1.5 * 1024 * 1024,
      warningCount: analysis.warnings.length,
      chunkCount: analysis.chunks.length
    }
  };

  const reportPath = path.join(process.cwd(), 'bundle-analysis.json');
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  console.log(`📄 Detailed report saved to: ${reportPath}`);
}

// Main execution
async function main() {
  console.log('🚀 Starting bundle size analysis...\n');
  
  const bundleInfo = parseNextBuildOutput();
  const analysis = analyzeBundleFiles();
  
  generateReport(bundleInfo, analysis);
  saveReport(bundleInfo, analysis);
  
  // Exit with error code if warnings exist
  if (analysis.warnings.length > 0) {
    console.log('❌ Analysis completed with warnings.');
    process.exit(1);
  } else {
    console.log('✅ Analysis completed successfully.');
    process.exit(0);
  }
}

if (require.main === module) {
  main().catch(error => {
    console.error('❌ Analysis failed:', error);
    process.exit(1);
  });
}

module.exports = {
  analyzeBundleFiles,
  parseNextBuildOutput,
  generateReport,
  formatBytes
};
