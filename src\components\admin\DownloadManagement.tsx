"use client";

import { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';

interface DownloadRecord {
  id: string;
  fileName: string;
  conversionType: string;
  toolName: string;
  fileSize: number;
  timestamp: string;
  createdAt: string;
}

interface DownloadStats {
  totalDownloads: number;
  totalFileSize: number;
  uniqueConversionTypes: number;
  uniqueToolsUsed: number;
  conversionTypes: string[];
  toolsUsed: string[];
}

interface ArchivalStats {
  totalArchiveFiles: number;
  oldestArchive: string | null;
  newestArchive: string | null;
  totalArchiveSize: number;
}

interface DatabaseStats {
  totalRecords: number;
  recordsToArchive: number;
  recentRecords: Array<{
    fileName: string;
    conversionType: string;
    userEmail: string;
    createdAt: string;
  }>;
}

/**
 * Admin component for managing downloads and archival
 */
export default function DownloadManagement() {
  const { isAdmin, isAuthenticated } = useAuth();
  const [downloads, setDownloads] = useState<DownloadRecord[]>([]);
  const [stats, setStats] = useState<DownloadStats | null>(null);
  const [archivalStats, setArchivalStats] = useState<ArchivalStats | null>(null);
  const [databaseStats, setDatabaseStats] = useState<DatabaseStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [archiving, setArchiving] = useState(false);

  // Load download data
  useEffect(() => {
    if (isAdmin) {
      loadDownloadData();
      loadArchivalData();
    }
  }, [isAdmin]);

  // Check authorization
  if (!isAuthenticated || !isAdmin) {
    return (
      <div className="p-6 bg-red-50 rounded-lg">
        <h2 className="text-xl font-semibold text-red-800 mb-2">Access Denied</h2>
        <p className="text-red-700">You need admin privileges to access download management.</p>
      </div>
    );
  }

  const loadDownloadData = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/downloads/track?limit=50');
      
      if (!response.ok) {
        throw new Error('Failed to load download data');
      }

      const data = await response.json();
      setDownloads(data.downloads);
      setStats(data.statistics);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load downloads');
    } finally {
      setLoading(false);
    }
  };

  const loadArchivalData = async () => {
    try {
      const response = await fetch('/api/downloads/archive');
      
      if (!response.ok) {
        throw new Error('Failed to load archival data');
      }

      const data = await response.json();
      setArchivalStats(data.archivalStats);
      setDatabaseStats(data.databaseStats);
    } catch (err) {
      console.error('Failed to load archival data:', err);
    }
  };

  const triggerArchival = async () => {
    try {
      setArchiving(true);
      const response = await fetch('/api/downloads/archive', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({}),
      });

      if (!response.ok) {
        throw new Error('Failed to trigger archival');
      }

      const result = await response.json();
      
      if (result.success) {
        alert(`Archival completed successfully! ${result.data.recordsArchived} records archived.`);
        loadDownloadData();
        loadArchivalData();
      } else {
        throw new Error(result.error || 'Archival failed');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Archival failed');
    } finally {
      setArchiving(false);
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes < 1024) return `${bytes} B`;
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`;
    return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
  };

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleString();
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="space-y-3">
            <div className="h-4 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded w-5/6"></div>
            <div className="h-4 bg-gray-200 rounded w-4/6"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900">Download Management</h1>
        <button
          onClick={triggerArchival}
          disabled={archiving}
          className={`px-4 py-2 rounded-md font-medium ${
            archiving
              ? 'bg-gray-400 cursor-not-allowed'
              : 'bg-blue-600 hover:bg-blue-700 text-white'
          }`}
        >
          {archiving ? 'Archiving...' : 'Trigger Archival'}
        </button>
      </div>

      {error && (
        <div className="p-4 bg-red-50 text-red-700 rounded-lg">
          {error}
        </div>
      )}

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-sm font-medium text-gray-500">Total Downloads</h3>
          <p className="text-2xl font-bold text-gray-900">{stats?.totalDownloads || 0}</p>
        </div>
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-sm font-medium text-gray-500">Total File Size</h3>
          <p className="text-2xl font-bold text-gray-900">{formatFileSize(stats?.totalFileSize || 0)}</p>
        </div>
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-sm font-medium text-gray-500">Conversion Types</h3>
          <p className="text-2xl font-bold text-gray-900">{stats?.uniqueConversionTypes || 0}</p>
        </div>
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-sm font-medium text-gray-500">Tools Used</h3>
          <p className="text-2xl font-bold text-gray-900">{stats?.uniqueToolsUsed || 0}</p>
        </div>
      </div>

      {/* Database Status */}
      {databaseStats && (
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-lg font-semibold mb-4">Database Status</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <h3 className="text-sm font-medium text-gray-500">Current Records</h3>
              <p className="text-xl font-bold text-gray-900">{databaseStats.totalRecords}</p>
            </div>
            <div>
              <h3 className="text-sm font-medium text-gray-500">Ready for Archival</h3>
              <p className="text-xl font-bold text-orange-600">{databaseStats.recordsToArchive}</p>
            </div>
            <div>
              <h3 className="text-sm font-medium text-gray-500">Archive Files</h3>
              <p className="text-xl font-bold text-green-600">{archivalStats?.totalArchiveFiles || 0}</p>
            </div>
          </div>
        </div>
      )}

      {/* Recent Downloads */}
      <div className="bg-white rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold">Recent Downloads</h2>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  File Name
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Conversion Type
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Tool
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  File Size
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Date
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {downloads.map((download) => (
                <tr key={download.id}>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {download.fileName}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {download.conversionType}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {download.toolName}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {formatFileSize(download.fileSize)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {formatDate(download.timestamp)}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}
