import { getToken } from "next-auth/jwt";
import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
import { SLUG_REDIRECTS, ContentType, isValidRoute } from '@/lib/routing';
import { rateLimit, getRateLimitConfig, createRateLimitResponse } from '@/lib/rateLimiter';
import { applySecurityHeaders, getRouteSpecificCSP, applyAPISecurityHeaders } from '@/lib/security-headers';
import { addCSRFTokenToResponse } from '@/lib/csrf';
import { monitorRequest, logSecurityEvent, shouldBlockIP, getClientIP } from '@/lib/security-monitor';

// Enterprise security constants
const BLOCKED_USER_AGENTS = [
  'bot', 'crawler', 'spider', 'scraper', 'scanner',
  'curl', 'wget', 'python-requests', 'postman'
];

const SUSPICIOUS_PATTERNS = [
  /\.\./,  // Directory traversal
  /<script/i,  // XSS attempts
  /union.*select/i,  // SQL injection
  /javascript:/i,  // JavaScript protocol
  /data:/i,  // Data URLs
  /vbscript:/i,  // VBScript
];

const MAX_REQUEST_SIZE = 10 * 1024 * 1024; // 10MB

const secret = process.env.NEXTAUTH_SECRET;

// Enterprise security validation functions
function isBlockedUserAgent(userAgent: string): boolean {
  const ua = userAgent.toLowerCase();
  return BLOCKED_USER_AGENTS.some(blocked => ua.includes(blocked));
}

function containsSuspiciousPatterns(url: string): boolean {
  return SUSPICIOUS_PATTERNS.some(pattern => pattern.test(url));
}

function validateRequestSize(req: NextRequest): boolean {
  const contentLength = req.headers.get('content-length');
  if (contentLength) {
    const size = parseInt(contentLength, 10);
    return size <= MAX_REQUEST_SIZE;
  }
  return true;
}

export async function middleware(req: NextRequest) {
  const token = await getToken({ req, secret });
  const pathname = req.nextUrl.pathname;
  const userAgent = req.headers.get('user-agent') || '';
  const clientIP = getClientIP(req);

  // Monitor request for security threats
  const threats = monitorRequest(req);

  // Block IP if it has too many security violations
  if (shouldBlockIP(clientIP)) {
    logSecurityEvent({
      type: 'blocked_user_agent',
      severity: 'high',
      ip: clientIP,
      userAgent,
      path: pathname,
      timestamp: Date.now(),
      details: {
        reason: 'IP blocked due to excessive security violations',
      },
    });

    return new NextResponse('Access Denied', { status: 403 });
  }

  // Block requests with critical threats immediately
  const criticalThreats = threats.filter(threat => threat.severity === 'critical');
  if (criticalThreats.length > 0) {
    return new NextResponse('Request Blocked', { status: 403 });
  }

  // Enterprise Security Checks
  // 1. Block suspicious user agents (except for legitimate crawlers)
  if (isBlockedUserAgent(userAgent) && !pathname.startsWith('/api/health')) {
    console.warn(`Blocked suspicious user agent: ${userAgent} for ${pathname}`);
    return new NextResponse('Forbidden', { status: 403 });
  }

  // 2. Check for suspicious patterns in URL
  if (containsSuspiciousPatterns(pathname + req.nextUrl.search)) {
    console.warn(`Blocked suspicious request pattern: ${pathname}`);
    return new NextResponse('Bad Request', { status: 400 });
  }

  // 3. Validate request size
  if (!validateRequestSize(req)) {
    console.warn(`Request too large: ${req.headers.get('content-length')} bytes`);
    return new NextResponse('Payload Too Large', { status: 413 });
  }

  // 4. Apply rate limiting to API routes
  if (pathname.startsWith("/api")) {
    const rateLimitConfig = getRateLimitConfig(pathname);
    const rateLimitResult = await rateLimit(req, rateLimitConfig);

    if (!rateLimitResult.success) {
      return createRateLimitResponse(rateLimitResult);
    }
  }

  // Handle unified dynamic routes and redirects FIRST (before auth checks)
  const unifiedRouteMatch = pathname.match(/^\/([^\/]+)\/([^\/]+)$/);

  if (unifiedRouteMatch) {
    const [, type, slug] = unifiedRouteMatch;

    // Check if this is a valid content type
    if (['calculators', 'tools', 'blogs'].includes(type)) {
      const contentType = type as ContentType;

      // Check for slug redirects
      const redirectSlug = SLUG_REDIRECTS[slug];
      if (redirectSlug) {
        const redirectUrl = new URL(`/${type}/${redirectSlug}`, req.url);
        return NextResponse.redirect(redirectUrl, 301); // Permanent redirect
      }

      // For valid content types, let the request continue (route validation happens in the page component)
      // This allows the unified page to handle 404s properly with Next.js notFound()
    }
  }

  // Handle legacy routes and redirect to unified system
  const legacyRedirects: Record<string, string> = {
    // Calculator legacy routes
    '/calculator/mortgage': '/calculators/mortgage-calculator',
    '/calculator/bmi': '/calculators/bmi-calculator',
    '/calculator/tip': '/calculators/tip-calculator',

    // Tool legacy routes
    '/tool/pdf-compress': '/tools/compress-pdf',
    '/tool/pdf-merge': '/tools/merge-pdf',
    '/tool/word-pdf': '/tools/word-to-pdf',

    // Blog legacy routes
    '/article/pdf-tips': '/blogs/pdf-management-tips',
    '/post/word-pdf-guide': '/blogs/word-to-pdf-benefits',
  };

  if (legacyRedirects[pathname]) {
    const redirectUrl = new URL(legacyRedirects[pathname], req.url);
    return NextResponse.redirect(redirectUrl, 301);
  }

  // Handle API routes - add user headers for authenticated requests
  if (pathname.startsWith("/api")) {
    const requestHeaders = new Headers(req.headers);

    if (token) {
      requestHeaders.set('x-user-id', token.sub as string);
      requestHeaders.set('x-user-role', token.role as string || 'user');
    }

    const response = NextResponse.next({
      request: {
        headers: requestHeaders,
      },
    });

    // Apply API security headers
    return applyAPISecurityHeaders(response);
  }

  // Public routes accessible by anyone
  const publicRoutes = [
    "/",
    "/tools",
    "/blog",
    "/blogs",
    "/login",
    "/register",
    "/calculators",
    "/privacy-policy",
    "/terms",
    "/contact",
    "/about"
  ];

  // Allow access to public routes and their sub-routes without token
  // Also allow unified dynamic routes for calculators, tools, and blogs
  if (publicRoutes.some(route => pathname === route || pathname.startsWith(route + "/")) ||
      pathname.match(/^\/(calculators|tools|blogs)\/[^\/]+$/)) {
    const response = NextResponse.next();

    // Apply security headers
    const routeSpecificCSP = getRouteSpecificCSP(pathname);
    const securityConfig = routeSpecificCSP ? { contentSecurityPolicy: routeSpecificCSP } : undefined;

    return applySecurityHeaders(response, securityConfig);
  }

  // Protect /user routes for logged-in users only
  if (pathname.startsWith("/user")) {
    if (!token) {
      // Redirect to login if no valid token
      return NextResponse.redirect(new URL("/login", req.url));
    }

    const response = NextResponse.next();

    // Apply security headers and CSRF token
    const routeSpecificCSP = getRouteSpecificCSP(pathname);
    const securityConfig = routeSpecificCSP ? { contentSecurityPolicy: routeSpecificCSP } : undefined;
    const securedResponse = applySecurityHeaders(response, securityConfig);

    return await addCSRFTokenToResponse(req, securedResponse);
  }

  // Protect /admin routes for admin users only
  if (pathname.startsWith("/admin")) {
    if (!token) {
      return NextResponse.redirect(new URL("/login", req.url));
    }

    if (token.role !== "admin") {
      // Redirect non-admin users to home
      return NextResponse.redirect(new URL("/", req.url));
    }

    const response = NextResponse.next();

    // Apply security headers and CSRF token for admin routes
    const routeSpecificCSP = getRouteSpecificCSP(pathname);
    const securityConfig = routeSpecificCSP ? { contentSecurityPolicy: routeSpecificCSP } : undefined;
    const securedResponse = applySecurityHeaders(response, securityConfig);

    return await addCSRFTokenToResponse(req, securedResponse);
  }

  // Allow all other routes by default (don't block unknown routes)
  const response = NextResponse.next();

  // Apply basic security headers to all other routes
  return applySecurityHeaders(response);
}

// Apply middleware only to specific route patterns
export const config = {
  matcher: [
    "/admin/:path*",
    "/user/:path*",
    "/api/:path*", // Add API routes to middleware
    "/((?!_next/static|_next/image|favicon.ico).*)", // Apply to all routes except Next.js internals
  ],
};
