"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs";

interface PregnancyResult {
  dueDate: Date;
  currentWeek: number;
  currentDay: number;
  trimester: number;
  daysRemaining: number;
  conceptionDate: Date;
  milestones: Array<{
    week: number;
    title: string;
    description: string;
    completed: boolean;
  }>;
}

export default function PregnancyCalculator() {
  const [lastPeriodDate, setLastPeriodDate] = useState<string>("");
  const [cycleLength, setCycleLength] = useState<number>(28);
  const [calculationMethod, setCalculationMethod] = useState<string>("lmp");
  const [conceptionDate, setConceptionDate] = useState<string>("");
  const [dueDate, setDueDate] = useState<string>("");
  const [result, setResult] = useState<PregnancyResult | null>(null);

  const calculatePregnancy = () => {
    let estimatedConceptionDate: Date;
    let estimatedDueDate: Date;

    const today = new Date();

    switch (calculationMethod) {
      case "lmp":
        if (!lastPeriodDate) {
          alert("Please enter your last menstrual period date");
          return;
        }
        const lmpDate = new Date(lastPeriodDate);
        // Conception typically occurs 14 days after LMP (ovulation)
        estimatedConceptionDate = new Date(lmpDate.getTime() + (14 * 24 * 60 * 60 * 1000));
        // Due date is 280 days (40 weeks) from LMP
        estimatedDueDate = new Date(lmpDate.getTime() + (280 * 24 * 60 * 60 * 1000));
        break;

      case "conception":
        if (!conceptionDate) {
          alert("Please enter your conception date");
          return;
        }
        estimatedConceptionDate = new Date(conceptionDate);
        // Due date is 266 days (38 weeks) from conception
        estimatedDueDate = new Date(estimatedConceptionDate.getTime() + (266 * 24 * 60 * 60 * 1000));
        break;

      case "due_date":
        if (!dueDate) {
          alert("Please enter your due date");
          return;
        }
        estimatedDueDate = new Date(dueDate);
        // Conception date is 266 days before due date
        estimatedConceptionDate = new Date(estimatedDueDate.getTime() - (266 * 24 * 60 * 60 * 1000));
        break;

      default:
        return;
    }

    // Calculate current pregnancy week and day
    const daysSinceConception = Math.floor((today.getTime() - estimatedConceptionDate.getTime()) / (24 * 60 * 60 * 1000));
    const currentWeek = Math.floor(daysSinceConception / 7) + 2; // Add 2 weeks for gestational age
    const currentDay = (daysSinceConception % 7) + 1;

    // Calculate trimester
    let trimester: number;
    if (currentWeek <= 12) trimester = 1;
    else if (currentWeek <= 27) trimester = 2;
    else trimester = 3;

    // Calculate days remaining
    const daysRemaining = Math.max(0, Math.floor((estimatedDueDate.getTime() - today.getTime()) / (24 * 60 * 60 * 1000)));

    // Generate milestones
    const milestones = getPregnancyMilestones(currentWeek);

    setResult({
      dueDate: estimatedDueDate,
      currentWeek: Math.max(0, currentWeek),
      currentDay,
      trimester,
      daysRemaining,
      conceptionDate: estimatedConceptionDate,
      milestones
    });
  };

  const getPregnancyMilestones = (currentWeek: number) => [
    { week: 4, title: "Missed Period", description: "First sign of pregnancy", completed: currentWeek >= 4 },
    { week: 6, title: "Heartbeat Detectable", description: "Baby's heart starts beating", completed: currentWeek >= 6 },
    { week: 8, title: "First Prenatal Visit", description: "Initial doctor appointment", completed: currentWeek >= 8 },
    { week: 12, title: "End of First Trimester", description: "Risk of miscarriage decreases", completed: currentWeek >= 12 },
    { week: 16, title: "Gender Determination", description: "Baby's sex can be determined", completed: currentWeek >= 16 },
    { week: 18, title: "Anatomy Scan", description: "Detailed ultrasound examination", completed: currentWeek >= 18 },
    { week: 20, title: "Halfway Point", description: "Midpoint of pregnancy", completed: currentWeek >= 20 },
    { week: 24, title: "Viability Milestone", description: "Baby could survive outside womb", completed: currentWeek >= 24 },
    { week: 28, title: "Third Trimester", description: "Final stretch begins", completed: currentWeek >= 28 },
    { week: 32, title: "Rapid Growth", description: "Baby gains weight quickly", completed: currentWeek >= 32 },
    { week: 36, title: "Full Term Approaching", description: "Baby is nearly ready", completed: currentWeek >= 36 },
    { week: 40, title: "Due Date", description: "Expected delivery date", completed: currentWeek >= 40 }
  ];

  const reset = () => {
    setLastPeriodDate("");
    setCycleLength(28);
    setCalculationMethod("lmp");
    setConceptionDate("");
    setDueDate("");
    setResult(null);
  };

  const formatDate = (date: Date): string => {
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getTrimesterInfo = (trimester: number) => {
    switch (trimester) {
      case 1:
        return {
          name: "First Trimester",
          weeks: "1-12 weeks",
          description: "Organ development and early growth",
          color: "blue"
        };
      case 2:
        return {
          name: "Second Trimester",
          weeks: "13-27 weeks",
          description: "Rapid growth and movement",
          color: "green"
        };
      case 3:
        return {
          name: "Third Trimester",
          weeks: "28-40 weeks",
          description: "Final development and preparation for birth",
          color: "purple"
        };
      default:
        return {
          name: "Pre-pregnancy",
          weeks: "0 weeks",
          description: "Not yet pregnant",
          color: "gray"
        };
    }
  };

  const getWeekDescription = (week: number): string => {
    if (week < 4) return "Very early pregnancy";
    if (week < 8) return "Embryonic period";
    if (week < 12) return "Late first trimester";
    if (week < 16) return "Early second trimester";
    if (week < 20) return "Mid second trimester";
    if (week < 24) return "Late second trimester";
    if (week < 28) return "Viability period";
    if (week < 32) return "Early third trimester";
    if (week < 36) return "Mid third trimester";
    if (week < 40) return "Late third trimester";
    if (week >= 40) return "Full term";
    return "Pre-pregnancy";
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Pregnancy Calculator</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Calculation Method */}
          <div>
            <Label htmlFor="calculation-method">Calculation Method</Label>
            <Select value={calculationMethod} onValueChange={setCalculationMethod}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="lmp">Last Menstrual Period (LMP)</SelectItem>
                <SelectItem value="conception">Conception Date</SelectItem>
                <SelectItem value="due_date">Known Due Date</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Input Fields Based on Method */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              {calculationMethod === "lmp" && (
                <>
                  <div>
                    <Label htmlFor="last-period">Last Menstrual Period Date</Label>
                    <Input
                      id="last-period"
                      type="date"
                      value={lastPeriodDate}
                      onChange={(e) => setLastPeriodDate(e.target.value)}
                    />
                  </div>
                  <div>
                    <Label htmlFor="cycle-length">Average Cycle Length (days)</Label>
                    <Input
                      id="cycle-length"
                      type="number"
                      value={cycleLength}
                      onChange={(e) => setCycleLength(parseInt(e.target.value) || 28)}
                      min="21"
                      max="35"
                    />
                  </div>
                </>
              )}

              {calculationMethod === "conception" && (
                <div>
                  <Label htmlFor="conception-date">Conception Date</Label>
                  <Input
                    id="conception-date"
                    type="date"
                    value={conceptionDate}
                    onChange={(e) => setConceptionDate(e.target.value)}
                  />
                </div>
              )}

              {calculationMethod === "due_date" && (
                <div>
                  <Label htmlFor="due-date">Due Date</Label>
                  <Input
                    id="due-date"
                    type="date"
                    value={dueDate}
                    onChange={(e) => setDueDate(e.target.value)}
                  />
                </div>
              )}
            </div>

            <div className="p-4 bg-pink-50 dark:bg-pink-900/20 rounded-lg">
              <h3 className="font-semibold mb-2">Calculation Methods</h3>
              <div className="space-y-2 text-sm">
                <div><strong>LMP:</strong> Most common method, adds 280 days to last period</div>
                <div><strong>Conception:</strong> If you know when conception occurred</div>
                <div><strong>Due Date:</strong> If you already know your due date</div>
              </div>
            </div>
          </div>

          {/* Calculate Button */}
          <div className="flex gap-4">
            <Button onClick={calculatePregnancy} className="flex-1">
              Calculate Pregnancy Details
            </Button>
            <Button onClick={reset} variant="outline">
              Reset
            </Button>
          </div>

          {/* Results */}
          {result && (
            <div className="space-y-6">
              {/* Current Status */}
              <Card className={`bg-${getTrimesterInfo(result.trimester).color}-50 dark:bg-${getTrimesterInfo(result.trimester).color}-900/20`}>
                <CardContent className="pt-6">
                  <div className="text-center space-y-2">
                    <div className="text-sm text-muted-foreground">Current Status</div>
                    <div className={`text-2xl font-bold text-${getTrimesterInfo(result.trimester).color}-600 dark:text-${getTrimesterInfo(result.trimester).color}-400`}>
                      {result.currentWeek} weeks, {result.currentDay} days
                    </div>
                    <div className="text-lg font-semibold">{getTrimesterInfo(result.trimester).name}</div>
                    <div className="text-sm">{getWeekDescription(result.currentWeek)}</div>
                  </div>
                </CardContent>
              </Card>

              {/* Key Dates */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card className="bg-blue-50 dark:bg-blue-900/20">
                  <CardContent className="pt-6">
                    <div className="text-center space-y-2">
                      <div className="text-sm text-muted-foreground">Estimated Due Date</div>
                      <div className="text-xl font-bold text-blue-600 dark:text-blue-400">
                        {formatDate(result.dueDate)}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {result.daysRemaining} days remaining
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-green-50 dark:bg-green-900/20">
                  <CardContent className="pt-6">
                    <div className="text-center space-y-2">
                      <div className="text-sm text-muted-foreground">Estimated Conception</div>
                      <div className="text-xl font-bold text-green-600 dark:text-green-400">
                        {formatDate(result.conceptionDate)}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        Fertilization date
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Trimester Progress */}
              <Card>
                <CardHeader>
                  <CardTitle>Trimester Information</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="font-semibold">{getTrimesterInfo(result.trimester).name}</span>
                      <span className="text-sm text-muted-foreground">{getTrimesterInfo(result.trimester).weeks}</span>
                    </div>
                    <div className="text-sm">{getTrimesterInfo(result.trimester).description}</div>
                    
                    {/* Progress bar */}
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className={`bg-${getTrimesterInfo(result.trimester).color}-600 h-2 rounded-full`}
                        style={{ width: `${Math.min(100, (result.currentWeek / 40) * 100)}%` }}
                      ></div>
                    </div>
                    <div className="text-xs text-muted-foreground text-center">
                      {Math.min(100, Math.round((result.currentWeek / 40) * 100))}% complete
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Milestones */}
              <Card>
                <CardHeader>
                  <CardTitle>Pregnancy Milestones</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {result.milestones.map((milestone, index) => (
                      <div 
                        key={index} 
                        className={`flex items-center space-x-3 p-3 rounded-lg ${
                          milestone.completed 
                            ? 'bg-green-50 dark:bg-green-900/20' 
                            : 'bg-gray-50 dark:bg-gray-800'
                        }`}
                      >
                        <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold ${
                          milestone.completed 
                            ? 'bg-green-600 text-white' 
                            : 'bg-gray-300 text-gray-600'
                        }`}>
                          {milestone.completed ? '✓' : milestone.week}
                        </div>
                        <div className="flex-1">
                          <div className="font-semibold">Week {milestone.week}: {milestone.title}</div>
                          <div className="text-sm text-muted-foreground">{milestone.description}</div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* Important Notes */}
          <div className="p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
            <h3 className="font-semibold mb-2">Important Notes</h3>
            <ul className="text-sm space-y-1">
              <li>• These calculations are estimates - actual due dates may vary</li>
              <li>• Only 5% of babies are born on their exact due date</li>
              <li>• Full-term pregnancy is 37-42 weeks</li>
              <li>• Regular prenatal care is essential for healthy pregnancy</li>
              <li>• Consult your healthcare provider for personalized advice</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
