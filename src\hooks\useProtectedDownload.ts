"use client";

import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useState } from "react";

interface DownloadData {
  fileName: string;
  conversionType: string;
  originalFileSize?: number;
  convertedFileSize?: number;
  toolName?: string;
}

interface UseProtectedDownloadOptions {
  requireAuth?: boolean;
  redirectUrl?: string;
  onAuthRequired?: () => void;
  onDownloadStart?: (data: DownloadData) => void;
  onDownloadComplete?: (data: DownloadData) => void;
  onError?: (error: string) => void;
}

/**
 * Hook for handling authentication-protected file downloads
 * Ensures users are logged in before allowing downloads and tracks download activity
 */
export function useProtectedDownload(options: UseProtectedDownloadOptions = {}) {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [isDownloading, setIsDownloading] = useState(false);
  
  const {
    requireAuth = true,
    redirectUrl = "/login",
    onAuthRequired,
    onDownloadStart,
    onDownloadComplete,
    onError
  } = options;

  const isAuthenticated = status === "authenticated" && !!session;
  const isLoading = status === "loading";

  /**
   * Initiates a protected download
   * @param fileUrl - The blob URL or file URL to download
   * @param downloadData - Metadata about the download
   */
  const initiateDownload = async (fileUrl: string, downloadData: DownloadData) => {
    try {
      setIsDownloading(true);

      // Check authentication if required
      if (requireAuth && !isAuthenticated) {
        if (onAuthRequired) {
          onAuthRequired();
        } else {
          // Store the intended download for after login
          sessionStorage.setItem('pendingDownload', JSON.stringify({
            fileUrl,
            downloadData,
            timestamp: Date.now()
          }));
          
          // Redirect to login with return URL
          const currentUrl = window.location.pathname + window.location.search;
          router.push(`${redirectUrl}?callbackUrl=${encodeURIComponent(currentUrl)}`);
        }
        return false;
      }

      // Notify download start
      if (onDownloadStart) {
        onDownloadStart(downloadData);
      }

      // Track download in database if user is authenticated
      if (isAuthenticated) {
        await trackDownload(downloadData);
      }

      // Perform the actual download
      const link = document.createElement("a");
      link.href = fileUrl;
      link.download = downloadData.fileName;
      link.style.display = "none";
      
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Notify download complete
      if (onDownloadComplete) {
        onDownloadComplete(downloadData);
      }

      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Download failed";
      console.error("Protected download error:", error);
      
      if (onError) {
        onError(errorMessage);
      }
      
      return false;
    } finally {
      setIsDownloading(false);
    }
  };

  /**
   * Tracks download activity in the database
   */
  const trackDownload = async (downloadData: DownloadData) => {
    try {
      const response = await fetch('/api/downloads/track', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          fileName: downloadData.fileName,
          conversionType: downloadData.conversionType,
          originalFileSize: downloadData.originalFileSize,
          convertedFileSize: downloadData.convertedFileSize,
          toolName: downloadData.toolName,
          userAgent: navigator.userAgent,
          timestamp: new Date().toISOString()
        }),
      });

      if (!response.ok) {
        console.warn('Failed to track download:', await response.text());
      }
    } catch (error) {
      console.warn('Failed to track download:', error);
      // Don't fail the download if tracking fails
    }
  };

  /**
   * Checks for and processes any pending downloads after login
   */
  const processPendingDownload = async () => {
    if (!isAuthenticated) return;

    try {
      const pendingDownloadStr = sessionStorage.getItem('pendingDownload');
      if (!pendingDownloadStr) return;

      const pendingDownload = JSON.parse(pendingDownloadStr);
      
      // Check if the pending download is not too old (max 1 hour)
      const maxAge = 60 * 60 * 1000; // 1 hour
      if (Date.now() - pendingDownload.timestamp > maxAge) {
        sessionStorage.removeItem('pendingDownload');
        return;
      }

      // Clear the pending download
      sessionStorage.removeItem('pendingDownload');

      // Process the download
      await initiateDownload(pendingDownload.fileUrl, pendingDownload.downloadData);
    } catch (error) {
      console.error('Failed to process pending download:', error);
      sessionStorage.removeItem('pendingDownload');
    }
  };

  /**
   * Creates a download button component with authentication protection
   */
  const createDownloadButton = (
    fileUrl: string | null,
    downloadData: DownloadData,
    buttonProps: {
      className?: string;
      children?: React.ReactNode;
      disabled?: boolean;
    } = {}
  ) => {
    const { className = "", children = "Download", disabled = false } = buttonProps;
    
    const isDisabled = disabled || !fileUrl || isDownloading || (requireAuth && isLoading);
    
    const handleClick = () => {
      if (fileUrl) {
        initiateDownload(fileUrl, downloadData);
      }
    };

    const getButtonText = () => {
      if (isDownloading) return "Downloading...";
      if (requireAuth && !isAuthenticated && !isLoading) return "Login to Download";
      return children;
    };

    return {
      onClick: handleClick,
      disabled: isDisabled,
      className: `${className} ${isDisabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`,
      children: getButtonText()
    };
  };

  return {
    // State
    isAuthenticated,
    isLoading,
    isDownloading,
    user: session?.user || null,
    
    // Actions
    initiateDownload,
    processPendingDownload,
    createDownloadButton,
    
    // Utilities
    canDownload: !requireAuth || isAuthenticated,
    needsAuth: requireAuth && !isAuthenticated && !isLoading
  };
}
