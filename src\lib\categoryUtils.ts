// lib/categoryUtils.ts
import { cache } from 'react';

export interface Category {
  _id: string;
  name: string;
  slug: string;
  description?: string;
  count?: number;
}

// Cache for category data to avoid repeated API calls
let categoryCache: Map<string, Category> = new Map();
let categoriesCache: Category[] = [];
let lastFetch = 0;
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

// Default category mappings for fallback
const DEFAULT_CATEGORIES: Record<string, string> = {
  'technology': 'Technology',
  'food': 'Food',
  'automotive': 'Automotive',
  'general': 'General',
  'health': 'Health',
  'finance': 'Finance',
  'education': 'Education',
  'entertainment': 'Entertainment',
  'sports': 'Sports',
  'travel': 'Travel',
  'lifestyle': 'Lifestyle',
  'business': 'Business',
  'science': 'Science',
  'politics': 'Politics',
  'art': 'Art',
  'music': 'Music',
  'fashion': 'Fashion',
  'gaming': 'Gaming',
  'photography': 'Photography',
  'cooking': 'Cooking',
};

// Fetch categories from API with caching
export const fetchCategories = cache(async (): Promise<Category[]> => {
  const now = Date.now();
  
  // Return cached data if still valid
  if (categoriesCache.length > 0 && (now - lastFetch) < CACHE_DURATION) {
    return categoriesCache;
  }

  try {
    const response = await fetch('/api/categories', {
      next: { revalidate: 300 }, // Revalidate every 5 minutes
    });
    
    if (response.ok) {
      const categories = await response.json();
      categoriesCache = categories;
      lastFetch = now;
      
      // Update individual category cache
      categories.forEach((cat: Category) => {
        categoryCache.set(cat._id, cat);
        categoryCache.set(cat.name.toLowerCase(), cat);
        if (cat.slug) {
          categoryCache.set(cat.slug, cat);
        }
      });
      
      return categories;
    }
  } catch (error) {
    console.warn('Failed to fetch categories from API:', error);
  }

  // Return empty array if API fails
  return [];
});

// Get category name by ID, name, or slug
export const getCategoryName = async (identifier: string): Promise<string> => {
  if (!identifier) return 'General';

  // Check cache first
  const cached = categoryCache.get(identifier) || categoryCache.get(identifier.toLowerCase());
  if (cached) {
    return cached.name;
  }

  // Try to fetch categories if not in cache
  try {
    const categories = await fetchCategories();
    
    // Find by ID, name, or slug
    const category = categories.find(cat => 
      cat._id === identifier || 
      cat.name.toLowerCase() === identifier.toLowerCase() ||
      cat.slug === identifier.toLowerCase()
    );
    
    if (category) {
      return category.name;
    }
  } catch (error) {
    console.warn('Error fetching category name:', error);
  }

  // Fallback to default mappings
  const defaultName = DEFAULT_CATEGORIES[identifier.toLowerCase()];
  if (defaultName) {
    return defaultName;
  }

  // Final fallback: capitalize the identifier
  return identifier.charAt(0).toUpperCase() + identifier.slice(1);
};

// Get multiple category names
export const getCategoryNames = async (identifiers: string[]): Promise<string[]> => {
  if (!identifiers || identifiers.length === 0) return [];
  
  const names = await Promise.all(
    identifiers.map(id => getCategoryName(id))
  );
  
  return names;
};

// Client-side category name resolution (for components)
export const useCategoryName = (identifier: string): string => {
  // Check cache first
  const cached = categoryCache.get(identifier) || categoryCache.get(identifier.toLowerCase());
  if (cached) {
    return cached.name;
  }

  // Fallback to default mappings
  const defaultName = DEFAULT_CATEGORIES[identifier.toLowerCase()];
  if (defaultName) {
    return defaultName;
  }

  // Final fallback: capitalize the identifier
  return identifier ? identifier.charAt(0).toUpperCase() + identifier.slice(1) : 'General';
};

// Get category by ID with full details
export const getCategoryById = async (id: string): Promise<Category | null> => {
  // Check cache first
  const cached = categoryCache.get(id);
  if (cached) {
    return cached;
  }

  try {
    const categories = await fetchCategories();
    const category = categories.find(cat => cat._id === id);
    return category || null;
  } catch (error) {
    console.warn('Error fetching category by ID:', error);
    return null;
  }
};

// Initialize category cache (call this on app startup)
export const initializeCategoryCache = async (): Promise<void> => {
  try {
    await fetchCategories();
  } catch (error) {
    console.warn('Failed to initialize category cache:', error);
  }
};

// Clear category cache (useful for testing or manual refresh)
export const clearCategoryCache = (): void => {
  categoryCache.clear();
  categoriesCache = [];
  lastFetch = 0;
};

// Get all categories with counts
export const getCategoriesWithCounts = async (): Promise<Category[]> => {
  return await fetchCategories();
};

// Create a new category (admin only)
export const createCategory = async (name: string, description?: string): Promise<Category | null> => {
  try {
    const response = await fetch('/api/categories', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ name, description }),
    });

    if (response.ok) {
      const newCategory = await response.json();
      
      // Update cache
      categoryCache.set(newCategory._id, newCategory);
      categoryCache.set(newCategory.name.toLowerCase(), newCategory);
      if (newCategory.slug) {
        categoryCache.set(newCategory.slug, newCategory);
      }
      
      // Clear categories cache to force refresh
      categoriesCache = [];
      lastFetch = 0;
      
      return newCategory;
    }
  } catch (error) {
    console.error('Error creating category:', error);
  }

  return null;
};

// Helper function to format category for display
// Note: This function should not use hooks. Use useCategoryName hook directly in components.
export const formatCategoryForDisplay = (category: string | Category): string => {
  if (typeof category === 'string') {
    // Return the string as-is, components should use useCategoryName hook
    return category;
  }
  return category.name;
};

// Helper function to get category slug
export const getCategorySlug = (category: string | Category): string => {
  if (typeof category === 'string') {
    return category.toLowerCase().replace(/\s+/g, '-').replace(/[^\w-]+/g, '');
  }
  return category.slug || category.name.toLowerCase().replace(/\s+/g, '-').replace(/[^\w-]+/g, '');
};
