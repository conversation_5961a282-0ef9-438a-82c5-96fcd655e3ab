"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

export default function ProbabilityCalculator() {
  // Basic Probability
  const [favorableOutcomes, setFavorableOutcomes] = useState<number>(1);
  const [totalOutcomes, setTotalOutcomes] = useState<number>(6);
  const [basicResult, setBasicResult] = useState<number | null>(null);

  // Combinations & Permutations
  const [n, setN] = useState<number>(10);
  const [r, setR] = useState<number>(3);
  const [combinationResult, setCombinationResult] = useState<number | null>(null);
  const [permutationResult, setPermutationResult] = useState<number | null>(null);

  // Binomial Probability
  const [trials, setTrials] = useState<number>(10);
  const [successes, setSuccesses] = useState<number>(3);
  const [successProbability, setSuccessProbability] = useState<number>(0.5);
  const [binomialResult, setBinomialResult] = useState<number | null>(null);

  // Normal Distribution
  const [mean, setMean] = useState<number>(0);
  const [stdDev, setStdDev] = useState<number>(1);
  const [xValue, setXValue] = useState<number>(0);
  const [normalResult, setNormalResult] = useState<number | null>(null);

  // Factorial function
  const factorial = (n: number): number => {
    if (n < 0) return NaN;
    if (n === 0 || n === 1) return 1;
    let result = 1;
    for (let i = 2; i <= n; i++) {
      result *= i;
    }
    return result;
  };

  // Combination function: nCr = n! / (r! * (n-r)!)
  const combination = (n: number, r: number): number => {
    if (r > n || r < 0) return 0;
    if (r === 0 || r === n) return 1;
    
    // Use the more efficient formula to avoid large factorials
    let result = 1;
    for (let i = 0; i < r; i++) {
      result = result * (n - i) / (i + 1);
    }
    return Math.round(result);
  };

  // Permutation function: nPr = n! / (n-r)!
  const permutation = (n: number, r: number): number => {
    if (r > n || r < 0) return 0;
    let result = 1;
    for (let i = 0; i < r; i++) {
      result *= (n - i);
    }
    return result;
  };

  // Binomial probability: P(X = k) = C(n,k) * p^k * (1-p)^(n-k)
  const binomialProbability = (n: number, k: number, p: number): number => {
    const coeff = combination(n, k);
    return coeff * Math.pow(p, k) * Math.pow(1 - p, n - k);
  };

  // Standard normal cumulative distribution function (approximation)
  const standardNormalCDF = (z: number): number => {
    // Abramowitz and Stegun approximation
    const a1 =  0.254829592;
    const a2 = -0.284496736;
    const a3 =  1.421413741;
    const a4 = -1.453152027;
    const a5 =  1.061405429;
    const p  =  0.3275911;

    const sign = z < 0 ? -1 : 1;
    z = Math.abs(z) / Math.sqrt(2.0);

    const t = 1.0 / (1.0 + p * z);
    const y = 1.0 - (((((a5 * t + a4) * t) + a3) * t + a2) * t + a1) * t * Math.exp(-z * z);

    return 0.5 * (1.0 + sign * y);
  };

  const calculateBasicProbability = () => {
    if (totalOutcomes <= 0) {
      alert("Total outcomes must be greater than 0");
      return;
    }
    const probability = favorableOutcomes / totalOutcomes;
    setBasicResult(probability);
  };

  const calculateCombinationsPermutations = () => {
    if (n < 0 || r < 0) {
      alert("Values must be non-negative");
      return;
    }
    setCombinationResult(combination(n, r));
    setPermutationResult(permutation(n, r));
  };

  const calculateBinomial = () => {
    if (trials < 0 || successes < 0 || successes > trials) {
      alert("Invalid input values");
      return;
    }
    if (successProbability < 0 || successProbability > 1) {
      alert("Probability must be between 0 and 1");
      return;
    }
    const result = binomialProbability(trials, successes, successProbability);
    setBinomialResult(result);
  };

  const calculateNormal = () => {
    if (stdDev <= 0) {
      alert("Standard deviation must be positive");
      return;
    }
    const z = (xValue - mean) / stdDev;
    const probability = standardNormalCDF(z);
    setNormalResult(probability);
  };

  const reset = () => {
    setFavorableOutcomes(1);
    setTotalOutcomes(6);
    setBasicResult(null);
    setN(10);
    setR(3);
    setCombinationResult(null);
    setPermutationResult(null);
    setTrials(10);
    setSuccesses(3);
    setSuccessProbability(0.5);
    setBinomialResult(null);
    setMean(0);
    setStdDev(1);
    setXValue(0);
    setNormalResult(null);
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Probability Calculator</CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="basic" className="space-y-6">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="basic">Basic</TabsTrigger>
              <TabsTrigger value="combinations">Combinations</TabsTrigger>
              <TabsTrigger value="binomial">Binomial</TabsTrigger>
              <TabsTrigger value="normal">Normal</TabsTrigger>
            </TabsList>

            {/* Basic Probability */}
            <TabsContent value="basic" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="favorable">Favorable Outcomes</Label>
                  <Input
                    id="favorable"
                    type="number"
                    value={favorableOutcomes}
                    onChange={(e) => setFavorableOutcomes(parseInt(e.target.value) || 0)}
                    min="0"
                  />
                </div>
                <div>
                  <Label htmlFor="total">Total Possible Outcomes</Label>
                  <Input
                    id="total"
                    type="number"
                    value={totalOutcomes}
                    onChange={(e) => setTotalOutcomes(parseInt(e.target.value) || 1)}
                    min="1"
                  />
                </div>
              </div>

              <Button onClick={calculateBasicProbability} className="w-full">
                Calculate Probability
              </Button>

              {basicResult !== null && (
                <Card className="bg-green-50 dark:bg-green-900/20">
                  <CardContent className="pt-6">
                    <div className="text-center space-y-2">
                      <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                        {(basicResult * 100).toFixed(4)}%
                      </div>
                      <div className="text-sm text-muted-foreground">
                        Probability: {basicResult.toFixed(6)}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        Odds: {favorableOutcomes}:{totalOutcomes - favorableOutcomes}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            {/* Combinations & Permutations */}
            <TabsContent value="combinations" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="n-value">n (Total Items)</Label>
                  <Input
                    id="n-value"
                    type="number"
                    value={n}
                    onChange={(e) => setN(parseInt(e.target.value) || 0)}
                    min="0"
                  />
                </div>
                <div>
                  <Label htmlFor="r-value">r (Items to Choose)</Label>
                  <Input
                    id="r-value"
                    type="number"
                    value={r}
                    onChange={(e) => setR(parseInt(e.target.value) || 0)}
                    min="0"
                  />
                </div>
              </div>

              <Button onClick={calculateCombinationsPermutations} className="w-full">
                Calculate
              </Button>

              {(combinationResult !== null || permutationResult !== null) && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Card className="bg-blue-50 dark:bg-blue-900/20">
                    <CardContent className="pt-6">
                      <div className="text-center space-y-2">
                        <div className="text-lg font-semibold">Combinations (nCr)</div>
                        <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                          {combinationResult?.toLocaleString()}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          Order doesn't matter
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="bg-purple-50 dark:bg-purple-900/20">
                    <CardContent className="pt-6">
                      <div className="text-center space-y-2">
                        <div className="text-lg font-semibold">Permutations (nPr)</div>
                        <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                          {permutationResult?.toLocaleString()}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          Order matters
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              )}
            </TabsContent>

            {/* Binomial Probability */}
            <TabsContent value="binomial" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="trials">Number of Trials (n)</Label>
                  <Input
                    id="trials"
                    type="number"
                    value={trials}
                    onChange={(e) => setTrials(parseInt(e.target.value) || 0)}
                    min="0"
                  />
                </div>
                <div>
                  <Label htmlFor="successes">Number of Successes (k)</Label>
                  <Input
                    id="successes"
                    type="number"
                    value={successes}
                    onChange={(e) => setSuccesses(parseInt(e.target.value) || 0)}
                    min="0"
                  />
                </div>
                <div>
                  <Label htmlFor="success-prob">Success Probability (p)</Label>
                  <Input
                    id="success-prob"
                    type="number"
                    step="0.01"
                    value={successProbability}
                    onChange={(e) => setSuccessProbability(parseFloat(e.target.value) || 0)}
                    min="0"
                    max="1"
                  />
                </div>
              </div>

              <Button onClick={calculateBinomial} className="w-full">
                Calculate Binomial Probability
              </Button>

              {binomialResult !== null && (
                <Card className="bg-orange-50 dark:bg-orange-900/20">
                  <CardContent className="pt-6">
                    <div className="text-center space-y-2">
                      <div className="text-lg font-semibold">P(X = {successes})</div>
                      <div className="text-2xl font-bold text-orange-600 dark:text-orange-400">
                        {(binomialResult * 100).toFixed(4)}%
                      </div>
                      <div className="text-sm text-muted-foreground">
                        Probability: {binomialResult.toFixed(8)}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            {/* Normal Distribution */}
            <TabsContent value="normal" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="mean">Mean (μ)</Label>
                  <Input
                    id="mean"
                    type="number"
                    step="0.1"
                    value={mean}
                    onChange={(e) => setMean(parseFloat(e.target.value) || 0)}
                  />
                </div>
                <div>
                  <Label htmlFor="std-dev">Standard Deviation (σ)</Label>
                  <Input
                    id="std-dev"
                    type="number"
                    step="0.1"
                    value={stdDev}
                    onChange={(e) => setStdDev(parseFloat(e.target.value) || 1)}
                    min="0.01"
                  />
                </div>
                <div>
                  <Label htmlFor="x-value">X Value</Label>
                  <Input
                    id="x-value"
                    type="number"
                    step="0.1"
                    value={xValue}
                    onChange={(e) => setXValue(parseFloat(e.target.value) || 0)}
                  />
                </div>
              </div>

              <Button onClick={calculateNormal} className="w-full">
                Calculate P(X ≤ x)
              </Button>

              {normalResult !== null && (
                <Card className="bg-teal-50 dark:bg-teal-900/20">
                  <CardContent className="pt-6">
                    <div className="text-center space-y-2">
                      <div className="text-lg font-semibold">P(X ≤ {xValue})</div>
                      <div className="text-2xl font-bold text-teal-600 dark:text-teal-400">
                        {(normalResult * 100).toFixed(4)}%
                      </div>
                      <div className="text-sm text-muted-foreground">
                        Z-score: {((xValue - mean) / stdDev).toFixed(4)}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}
            </TabsContent>
          </Tabs>

          <div className="flex gap-4 mt-6">
            <Button onClick={reset} variant="outline" className="flex-1">
              Reset All
            </Button>
          </div>

          {/* Instructions */}
          <div className="p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg mt-6">
            <h3 className="font-semibold mb-2">Probability Types</h3>
            <ul className="text-sm space-y-1">
              <li>• <strong>Basic:</strong> P(Event) = Favorable outcomes / Total outcomes</li>
              <li>• <strong>Combinations:</strong> nCr - choosing r items from n (order doesn't matter)</li>
              <li>• <strong>Permutations:</strong> nPr - arranging r items from n (order matters)</li>
              <li>• <strong>Binomial:</strong> Probability of exactly k successes in n trials</li>
              <li>• <strong>Normal:</strong> Cumulative probability for normal distribution</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
