# 🔧 GitHub Repository Setup Guide

## 📋 Overview

Complete guide for configuring GitHub repository for automated VPS deployment with enterprise-grade CI/CD pipeline.

**Repository:** `https://github.com/MuhammadShahbaz195/ToolCrush.git`
**Target Domain:** `toolrapter.com`
**VPS:** Hostinger Ubuntu 22.04 LTS (************)

## 🔐 Step 1: SSH Key Generation

### 1.1 Generate SSH Key Pair
```bash
# Generate RSA 4096-bit key (no passphrase for automation)
ssh-keygen -t rsa -b 4096 -C "<EMAIL>" -f ~/.ssh/toolrapter_deploy

# This creates:
# - ~/.ssh/toolrapter_deploy (private key)
# - ~/.ssh/toolrapter_deploy.pub (public key)
```

### 1.2 Add Public Key to VPS
```bash
# Copy public key to VPS
ssh-copy-id -i ~/.ssh/toolrapter_deploy.pub root@************

# Or manually add to authorized_keys:
cat ~/.ssh/toolrapter_deploy.pub | ssh root@************ "mkdir -p ~/.ssh && cat >> ~/.ssh/authorized_keys"
```

### 1.3 Test SSH Connection
```bash
# Test connection with private key
ssh -i ~/.ssh/toolrapter_deploy root@************

# Should connect without password prompt
```

## 🔑 Step 2: GitHub Secrets Configuration

### 2.1 Required Repository Secrets

Navigate to: **Repository Settings → Secrets and Variables → Actions**

Add the following secrets:

#### **VPS Connection Secrets**
```
VPS_SSH_KEY
```
**Value:** Content of private key file (`~/.ssh/toolrapter_deploy`)
```bash
cat ~/.ssh/toolrapter_deploy
```
Copy the entire content including `-----BEGIN OPENSSH PRIVATE KEY-----` and `-----END OPENSSH PRIVATE KEY-----`

#### **Database Secrets**
```
MONGODB_URI
```
**Value:** MongoDB connection string
```
*****************************************************************************
```

#### **Authentication Secrets**
```
NEXTAUTH_SECRET
```
**Value:** Random 32-character string
```bash
# Generate secure secret
openssl rand -base64 32
```

```
NEXTAUTH_URL
```
**Value:** Production URL
```
https://toolrapter.com
```

#### **Email Configuration**
```
EMAIL_SERVER_HOST
```
**Value:** SMTP server hostname
```
smtp.gmail.com
```

```
EMAIL_SERVER_PORT
```
**Value:** SMTP port
```
587
```

```
EMAIL_SERVER_USER
```
**Value:** Email username
```
<EMAIL>
```

```
EMAIL_SERVER_PASSWORD
```
**Value:** Email password or app password
```
your-app-password
```

```
EMAIL_FROM
```
**Value:** From email address
```
<EMAIL>
```

#### **Rate Limiting Configuration**
Rate limiting is now handled in-memory without external dependencies.
No additional configuration required - enterprise-grade rate limiting is built-in.

#### **OAuth Providers (Optional)**
```
GOOGLE_CLIENT_ID
```
**Value:** Google OAuth client ID

```
GOOGLE_CLIENT_SECRET
```
**Value:** Google OAuth client secret

```
GITHUB_CLIENT_ID
```
**Value:** GitHub OAuth client ID

```
GITHUB_CLIENT_SECRET
```
**Value:** GitHub OAuth client secret

### 2.2 Environment Variables (Repository Variables)

Navigate to: **Repository Settings → Secrets and Variables → Actions → Variables**

Add the following variables:

```
NODE_ENV=production
NEXT_TELEMETRY_DISABLED=1
DEPLOYMENT_PLATFORM=hostinger
NEXT_PUBLIC_DOMAIN=toolrapter.com
NEXT_PUBLIC_APP_NAME=ToolRapter
```

## ⚙️ Step 3: Repository Settings Configuration

### 3.1 General Settings
- **Repository name:** `ToolCrush`
- **Description:** `Enterprise-ready Next.js 14 app with admin panel, dynamic tools, blog & security`
- **Website:** `https://toolrapter.com`
- **Topics:** `nextjs`, `tailwindcss`, `typescript`, `admin-panel`, `tools`, `framer-motion`, `secure`, `vps-deployment`

### 3.2 Features Configuration
- ✅ **Issues** - Enable for bug tracking
- ✅ **Discussions** - Enable for community
- ✅ **Projects** - Enable for project management
- ✅ **Wiki** - Enable for documentation
- ❌ **Sponsorships** - Disable unless needed

### 3.3 Pull Requests Settings
- ✅ **Allow merge commits**
- ✅ **Allow squash merging**
- ❌ **Allow rebase merging**
- ✅ **Always suggest updating pull request branches**
- ✅ **Automatically delete head branches**

### 3.4 Branch Protection Rules

#### **Main Branch Protection**
Navigate to: **Settings → Branches → Add rule**

**Branch name pattern:** `main`

**Protection settings:**
- ✅ **Require a pull request before merging**
  - ✅ **Require approvals** (1 approval)
  - ✅ **Dismiss stale PR approvals when new commits are pushed**
  - ✅ **Require review from code owners**
- ✅ **Require status checks to pass before merging**
  - ✅ **Require branches to be up to date before merging**
  - **Required status checks:**
    - `Security Scan`
    - `Build & Test`
- ✅ **Require conversation resolution before merging**
- ✅ **Require signed commits**
- ✅ **Include administrators**
- ✅ **Restrict pushes that create files larger than 100MB**

## 🔄 Step 4: Workflow Configuration

### 4.1 Workflow Permissions
Navigate to: **Settings → Actions → General**

**Actions permissions:**
- ✅ **Allow all actions and reusable workflows**

**Workflow permissions:**
- ✅ **Read and write permissions**
- ✅ **Allow GitHub Actions to create and approve pull requests**

### 4.2 Environment Configuration
Navigate to: **Settings → Environments**

#### **Production Environment**
- **Environment name:** `production`
- **Protection rules:**
  - ✅ **Required reviewers** (add team members)
  - ✅ **Wait timer** (5 minutes)
  - ✅ **Deployment branches** (Selected branches: `main`)

#### **Staging Environment** (Optional)
- **Environment name:** `staging`
- **Protection rules:**
  - ✅ **Deployment branches** (Selected branches: `develop`, `staging`)

## 📊 Step 5: Monitoring & Notifications

### 5.1 Webhook Configuration (Optional)
Navigate to: **Settings → Webhooks**

Add webhook for deployment notifications:
- **Payload URL:** Your monitoring service URL
- **Content type:** `application/json`
- **Events:** Push, Pull request, Deployment

### 5.2 Email Notifications
Navigate to: **Settings → Notifications**

Configure email notifications for:
- ✅ **Actions workflow runs**
- ✅ **Deployment reviews**
- ✅ **Security alerts**

## 🔒 Step 6: Security Configuration

### 6.1 Security & Analysis
Navigate to: **Settings → Security & analysis**

Enable:
- ✅ **Dependency graph**
- ✅ **Dependabot alerts**
- ✅ **Dependabot security updates**
- ✅ **Secret scanning**
- ✅ **Push protection**

### 6.2 Deploy Keys (Alternative to SSH)
Navigate to: **Settings → Deploy keys**

If using deploy keys instead of SSH:
1. Generate deploy key: `ssh-keygen -t rsa -b 4096 -f deploy_key`
2. Add public key to VPS: `~/.ssh/authorized_keys`
3. Add private key to GitHub deploy keys
4. ✅ **Allow write access**

## 📝 Step 7: Documentation Setup

### 7.1 Repository Documentation
Ensure these files exist in repository root:
- ✅ `README.md` - Project overview and setup
- ✅ `LICENSE` - MIT or appropriate license
- ✅ `CONTRIBUTING.md` - Contribution guidelines
- ✅ `SECURITY.md` - Security policy
- ✅ `CODE_OF_CONDUCT.md` - Code of conduct

### 7.2 Issue Templates
Create `.github/ISSUE_TEMPLATE/` with:
- `bug_report.md` - Bug report template
- `feature_request.md` - Feature request template
- `security_issue.md` - Security issue template

### 7.3 Pull Request Template
Create `.github/pull_request_template.md`:
```markdown
## Description
Brief description of changes

## Type of Change
- [ ] Bug fix
- [ ] New feature
- [ ] Breaking change
- [ ] Documentation update

## Testing
- [ ] Tests pass locally
- [ ] Added new tests for changes
- [ ] Manual testing completed

## Checklist
- [ ] Code follows style guidelines
- [ ] Self-review completed
- [ ] Documentation updated
- [ ] No new warnings introduced
```

## ✅ Step 8: Verification Checklist

### 8.1 Pre-Deployment Verification
- [ ] All secrets configured correctly
- [ ] SSH key authentication working
- [ ] VPS infrastructure ready
- [ ] Domain DNS configured
- [ ] SSL certificates ready

### 8.2 Post-Setup Verification
- [ ] Workflow triggers on push to main
- [ ] Security scans pass
- [ ] Build and test jobs complete
- [ ] Deployment to VPS successful
- [ ] Application accessible at domain
- [ ] SSL certificate valid
- [ ] Performance benchmarks met

## 🚀 Step 9: First Deployment

### 9.1 Manual Trigger
1. Navigate to **Actions** tab
2. Select **Enterprise VPS Deployment** workflow
3. Click **Run workflow**
4. Select `production` environment
5. Click **Run workflow**

### 9.2 Monitor Deployment
- Watch workflow progress in Actions tab
- Check deployment logs for any issues
- Verify application is accessible
- Test key functionality

## 📞 Troubleshooting

### Common Issues:
1. **SSH Connection Failed**
   - Verify SSH key format
   - Check VPS firewall settings
   - Ensure key is added to authorized_keys

2. **Build Failures**
   - Check Node.js version compatibility
   - Verify all dependencies are available
   - Review build logs for specific errors

3. **Deployment Failures**
   - Check VPS disk space
   - Verify PM2 is installed
   - Check application permissions

4. **SSL Issues**
   - Verify domain DNS configuration
   - Check Certbot certificate status
   - Review Nginx configuration

## 📚 Additional Resources

- [GitHub Actions Documentation](https://docs.github.com/en/actions)
- [SSH Key Management](https://docs.github.com/en/authentication/connecting-to-github-with-ssh)
- [Repository Security](https://docs.github.com/en/code-security)
- [Environment Protection Rules](https://docs.github.com/en/actions/deployment/targeting-different-environments)

---

**⚠️ Security Note:** Never commit secrets to repository. Always use GitHub Secrets for sensitive information.
