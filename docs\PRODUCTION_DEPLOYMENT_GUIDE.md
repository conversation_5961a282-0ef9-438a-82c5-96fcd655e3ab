# 🚀 ToolRapter Production Deployment Guide

## 📋 Overview

This guide provides comprehensive instructions for deploying ToolRapter to production on Hostinger VPS at `toolrapter.com` with enterprise-grade performance, security, and reliability standards.

## 🎯 Performance Targets

- **Build Time**: < 20 seconds
- **Page Load Time**: < 5 seconds  
- **API Response Time**: < 3 seconds
- **Uptime**: > 99.9%
- **Security**: Enterprise-grade headers and monitoring

## 🏗️ Architecture Overview

```
Internet → Cloudflare → Nginx → PM2 Cluster → Next.js App
                     ↓
                 SSL/TLS + Security Headers
                     ↓
                 Rate Limiting + Monitoring
```

## 📋 Prerequisites

### System Requirements
- **VPS**: Hostinger VPS (************)
- **OS**: Ubuntu 20.04+ LTS
- **RAM**: Minimum 2GB (4GB recommended)
- **Storage**: Minimum 20GB SSD
- **Node.js**: Version 18.x or higher
- **Domain**: toolrapter.com (configured with Cloudflare)

### Required Accounts & Services
- GitHub account with repository access
- Hostinger VPS access
- Cloudflare account (for DNS and CDN)
- MongoDB Atlas account (or self-hosted MongoDB)
- Google OAuth credentials (optional)

## 🔧 Initial VPS Setup

### 1. Connect to VPS
```bash
ssh root@************
```

### 2. Update System
```bash
apt update && apt upgrade -y
apt install -y curl wget git nginx certbot python3-certbot-nginx
```

### 3. Install Node.js
```bash
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
apt install -y nodejs
npm install -g pm2
```

### 4. Create Application User
```bash
adduser --system --group --home /var/www/toolrapter toolrapter
usermod -aG www-data toolrapter
```

### 5. Setup Application Directory
```bash
mkdir -p /var/www/toolrapter
chown -R toolrapter:toolrapter /var/www/toolrapter
chmod -R 755 /var/www/toolrapter
```

## 🔐 Security Configuration

### 1. Configure Firewall
```bash
ufw allow OpenSSH
ufw allow 'Nginx Full'
ufw enable
```

### 2. Setup SSH Key Authentication
```bash
# On your local machine
ssh-copy-id root@************

# On VPS - disable password authentication
sed -i 's/#PasswordAuthentication yes/PasswordAuthentication no/' /etc/ssh/sshd_config
systemctl restart ssh
```

### 3. Install Fail2Ban
```bash
apt install -y fail2ban
systemctl enable fail2ban
systemctl start fail2ban
```

## 🌐 Nginx Configuration

### 1. Create Nginx Configuration
```bash
cat > /etc/nginx/sites-available/toolrapter.com << 'EOF'
server {
    listen 80;
    server_name toolrapter.com www.toolrapter.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name toolrapter.com www.toolrapter.com;

    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/toolrapter.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/toolrapter.com/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;

    # Security Headers
    add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload" always;
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Permissions-Policy "camera=(), microphone=(), geolocation=()" always;

    # Gzip Compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

    # Rate Limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=general:10m rate=30r/s;

    location / {
        limit_req zone=general burst=20 nodelay;
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
    }

    location /api/ {
        limit_req zone=api burst=10 nodelay;
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Static Assets Caching
    location /_next/static/ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        proxy_pass http://localhost:3000;
    }

    location /favicon.ico {
        expires 1y;
        add_header Cache-Control "public, immutable";
        proxy_pass http://localhost:3000;
    }
}
EOF
```

### 2. Enable Site and Test Configuration
```bash
ln -s /etc/nginx/sites-available/toolrapter.com /etc/nginx/sites-enabled/
nginx -t
systemctl reload nginx
```

### 3. Obtain SSL Certificate
```bash
certbot --nginx -d toolrapter.com -d www.toolrapter.com
```

## 🚀 Application Deployment

### 1. Clone Repository
```bash
cd /var/www/toolrapter
git clone https://github.com/MuhammadShahbaz195/ToolCrush.git .
chown -R toolrapter:toolrapter /var/www/toolrapter
```

### 2. Setup Environment Variables
```bash
cp .env.example .env.production
nano .env.production
```

Required environment variables:
```env
NODE_ENV=production
NEXT_PUBLIC_BASE_URL=https://toolrapter.com
NEXTAUTH_URL=https://toolrapter.com
NEXTAUTH_SECRET=your-super-secure-secret-key-minimum-32-characters
CSRF_SECRET=your-csrf-secret-key-minimum-32-characters
MONGODB_URI=mongodb+srv://username:<EMAIL>/database
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
SMTP_FROM=<EMAIL>
```

### 3. Install Dependencies and Build
```bash
su - toolrapter
cd /var/www/toolrapter
npm ci --production
npm run download-fonts
npm run build
```

### 4. Configure PM2
```bash
# ecosystem.config.js is already configured in the repository
pm2 start ecosystem.config.js --env production
pm2 save
pm2 startup
```

## 🔄 GitHub Actions CI/CD Setup

### 1. Configure GitHub Secrets
Add the following secrets to your GitHub repository:

- `VPS_HOST`: ************
- `VPS_USER`: root
- `VPS_SSH_KEY`: Your private SSH key
- `MONGODB_URI`: MongoDB connection string
- `NEXTAUTH_SECRET`: NextAuth secret
- `CSRF_SECRET`: CSRF protection secret
- `GOOGLE_CLIENT_ID`: Google OAuth client ID
- `GOOGLE_CLIENT_SECRET`: Google OAuth client secret

### 2. Deployment Workflow
The GitHub Actions workflow (`.github/workflows/deploy-vps.yml`) is already configured and will:

1. Run TypeScript compilation checks
2. Run security audits
3. Build the application
4. Deploy to VPS with zero downtime
5. Run health checks and performance validation
6. Rollback on failure

### 3. Manual Deployment
For manual deployment, use the production deployment script:
```bash
./scripts/deploy-production.sh
```

## 📊 Monitoring and Maintenance

### 1. Application Monitoring
```bash
# Check PM2 status
pm2 status
pm2 logs toolrapter

# Check Nginx status
systemctl status nginx
nginx -t

# Check system resources
htop
df -h
free -h
```

### 2. Performance Monitoring
- Access admin dashboard: `https://toolrapter.com/admin`
- Security monitoring: `https://toolrapter.com/api/admin/security`
- Performance metrics: `https://toolrapter.com/api/admin/performance`

### 3. Log Management
```bash
# Application logs
pm2 logs toolrapter --lines 100

# Nginx logs
tail -f /var/log/nginx/access.log
tail -f /var/log/nginx/error.log

# System logs
journalctl -u nginx -f
```

### 4. Backup Strategy
```bash
# Create backup script
cat > /root/backup-toolrapter.sh << 'EOF'
#!/bin/bash
BACKUP_DIR="/var/backups/toolrapter"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

mkdir -p $BACKUP_DIR

# Backup application
tar -czf $BACKUP_DIR/app_$TIMESTAMP.tar.gz -C /var/www/toolrapter .

# Keep only last 7 backups
find $BACKUP_DIR -name "app_*.tar.gz" -type f | sort -r | tail -n +8 | xargs rm -f

echo "Backup completed: app_$TIMESTAMP.tar.gz"
EOF

chmod +x /root/backup-toolrapter.sh

# Add to crontab for daily backups
echo "0 2 * * * /root/backup-toolrapter.sh" | crontab -
```

## 🔧 Troubleshooting

### Common Issues

1. **Build Failures**
   ```bash
   # Check Node.js version
   node --version
   
   # Clear cache and rebuild
   rm -rf .next node_modules
   npm ci
   npm run build
   ```

2. **PM2 Issues**
   ```bash
   # Restart application
   pm2 restart toolrapter
   
   # Check logs
   pm2 logs toolrapter --lines 50
   ```

3. **Nginx Issues**
   ```bash
   # Test configuration
   nginx -t
   
   # Reload configuration
   systemctl reload nginx
   ```

4. **SSL Certificate Issues**
   ```bash
   # Renew certificate
   certbot renew --dry-run
   certbot renew
   ```

### Performance Issues
- Check memory usage: `free -h`
- Check disk space: `df -h`
- Monitor CPU: `htop`
- Check application metrics: Visit `/api/admin/performance`

### Security Issues
- Check security events: Visit `/api/admin/security`
- Review Nginx logs: `tail -f /var/log/nginx/access.log`
- Check fail2ban: `fail2ban-client status`

## 📞 Support

For deployment issues or questions:
1. Check the troubleshooting section above
2. Review application logs: `pm2 logs toolrapter`
3. Check system logs: `journalctl -u nginx`
4. Contact system administrator

## 🔄 Updates and Maintenance

### Regular Maintenance Tasks
- **Daily**: Monitor application health and performance
- **Weekly**: Review security logs and update dependencies
- **Monthly**: Update system packages and renew SSL certificates
- **Quarterly**: Full security audit and performance optimization

### Update Process
1. Test changes in development environment
2. Create backup: `/root/backup-toolrapter.sh`
3. Deploy via GitHub Actions or manual script
4. Verify deployment with health checks
5. Monitor for 24 hours post-deployment

## 📈 Performance Optimization

### Build Time Optimization
- Current target: < 20 seconds
- Use `npm ci --production` for faster installs
- Enable parallel builds in Next.js config
- Optimize package imports with tree shaking

### Runtime Performance
- Current target: < 5 seconds page load
- Enable Nginx gzip compression
- Use CDN for static assets
- Implement proper caching strategies
- Monitor with `/api/admin/performance`

### Security Monitoring
- Real-time threat detection
- Rate limiting with tiered limits
- CSRF protection with double-submit cookies
- Security headers validation
- Monitor with `/api/admin/security`
