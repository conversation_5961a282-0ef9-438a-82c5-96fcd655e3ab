/**
 * Browser-Based Authentication Workflow Test Script
 * 
 * Run this script in your browser's developer console to test the authentication system
 * 
 * Instructions:
 * 1. Open http://localhost:3001 in your browser
 * 2. Open Developer Tools (F12)
 * 3. Go to Console tab
 * 4. Copy and paste this entire script
 * 5. Press Enter to run
 */

(function() {
  console.log('🧪 AUTHENTICATION WORKFLOW BROWSER TEST');
  console.log('=' .repeat(50));

  // Test configuration
  const BASE_URL = window.location.origin;
  
  /**
   * Test API endpoints
   */
  async function testAPIEndpoints() {
    console.log('\n🔍 Testing API Endpoints...');
    
    const endpoints = [
      '/api/auth/check',
      '/api/downloads/track',
      '/api/tools/excel-to-pdf'
    ];
    
    for (const endpoint of endpoints) {
      try {
        const response = await fetch(`${BASE_URL}${endpoint}`, {
          method: 'GET',
          credentials: 'include'
        });
        
        const status = response.status;
        const statusText = status < 400 ? '✅' : '❌';
        console.log(`  ${endpoint}: ${status} ${statusText}`);
        
        if (endpoint === '/api/auth/check' && response.ok) {
          const data = await response.json();
          console.log(`    Authenticated: ${data.isAuthenticated ? '✅' : '❌'}`);
          console.log(`    Admin: ${data.isAdmin ? '✅' : '❌'}`);
        }
        
      } catch (error) {
        console.log(`  ${endpoint}: Error - ${error.message} ❌`);
      }
    }
  }

  /**
   * Test download tracking API
   */
  async function testDownloadTracking() {
    console.log('\n📊 Testing Download Tracking...');
    
    try {
      const response = await fetch(`${BASE_URL}/api/downloads/track?limit=5`, {
        method: 'GET',
        credentials: 'include'
      });
      
      if (response.status === 401) {
        console.log('  ✅ Correctly requires authentication (401)');
        return;
      }
      
      if (response.ok) {
        const data = await response.json();
        console.log('  ✅ Download tracking data retrieved:');
        console.log(`    Total Downloads: ${data.statistics?.totalDownloads || 0}`);
        console.log(`    Recent Downloads: ${data.downloads?.length || 0}`);
        
        if (data.downloads && data.downloads.length > 0) {
          console.log('    Latest Download:');
          const latest = data.downloads[0];
          console.log(`      File: ${latest.fileName}`);
          console.log(`      Type: ${latest.conversionType}`);
          console.log(`      Date: ${new Date(latest.timestamp).toLocaleString()}`);
        }
      } else {
        console.log(`  ❌ Unexpected response: ${response.status}`);
      }
      
    } catch (error) {
      console.log(`  ❌ Error: ${error.message}`);
    }
  }

  /**
   * Test protected download hook functionality
   */
  function testProtectedDownloadHook() {
    console.log('\n🔐 Testing Protected Download Hook...');
    
    // Check if useProtectedDownload hook is available
    if (typeof window.React !== 'undefined') {
      console.log('  ✅ React is available');
    } else {
      console.log('  ⚠️ React not detected in global scope');
    }
    
    // Check for NextAuth session
    if (typeof window.__NEXT_DATA__ !== 'undefined') {
      console.log('  ✅ Next.js data available');
    } else {
      console.log('  ⚠️ Next.js data not detected');
    }
    
    // Check for session storage (pending downloads)
    const pendingDownload = sessionStorage.getItem('pendingDownload');
    if (pendingDownload) {
      console.log('  ⚠️ Pending download found in session storage:');
      try {
        const data = JSON.parse(pendingDownload);
        console.log(`    File: ${data.downloadData?.fileName || 'Unknown'}`);
        console.log(`    Type: ${data.downloadData?.conversionType || 'Unknown'}`);
        console.log(`    Timestamp: ${new Date(data.timestamp).toLocaleString()}`);
      } catch (e) {
        console.log('    Invalid pending download data');
      }
    } else {
      console.log('  ✅ No pending downloads');
    }
  }

  /**
   * Test current page for converter functionality
   */
  function testCurrentPage() {
    console.log('\n📄 Testing Current Page...');
    
    const url = window.location.pathname;
    console.log(`  Current URL: ${url}`);
    
    // Check if we're on a converter page
    if (url.includes('/tools/')) {
      console.log('  ✅ On a converter tool page');
      
      // Check for file upload elements
      const fileUploader = document.querySelector('input[type="file"]');
      if (fileUploader) {
        console.log('  ✅ File uploader found');
      } else {
        console.log('  ❌ File uploader not found');
      }
      
      // Check for convert button
      const convertButton = document.querySelector('button[type="button"]');
      if (convertButton) {
        console.log('  ✅ Convert button found');
      } else {
        console.log('  ❌ Convert button not found');
      }
      
      // Check for download button
      const downloadButton = document.querySelector('button:contains("Download"), button:contains("Login")');
      if (downloadButton) {
        console.log('  ✅ Download/Login button found');
      } else {
        console.log('  ⚠️ Download button not visible (may appear after conversion)');
      }
      
    } else {
      console.log('  ⚠️ Not on a converter tool page');
      console.log('  💡 Navigate to /tools/excel-to-pdf to test conversion');
    }
  }

  /**
   * Simulate file upload test
   */
  function simulateFileUploadTest() {
    console.log('\n📤 File Upload Simulation Test...');
    
    // Create a test file
    const testContent = 'Test file content for authentication workflow testing';
    const testFile = new File([testContent], 'test-file.txt', { type: 'text/plain' });
    
    console.log('  ✅ Test file created:');
    console.log(`    Name: ${testFile.name}`);
    console.log(`    Size: ${testFile.size} bytes`);
    console.log(`    Type: ${testFile.type}`);
    
    // Check if we can access file upload
    const fileInput = document.querySelector('input[type="file"]');
    if (fileInput) {
      console.log('  ✅ File input found - ready for manual upload test');
      console.log('  💡 You can now manually upload a file to test the workflow');
    } else {
      console.log('  ❌ File input not found on current page');
    }
  }

  /**
   * Check authentication status
   */
  async function checkAuthStatus() {
    console.log('\n👤 Checking Authentication Status...');
    
    try {
      const response = await fetch(`${BASE_URL}/api/auth/check`, {
        credentials: 'include'
      });
      
      if (response.ok) {
        const data = await response.json();
        console.log(`  Authentication: ${data.isAuthenticated ? '✅ LOGGED IN' : '❌ NOT LOGGED IN'}`);
        console.log(`  Admin Access: ${data.isAdmin ? '✅ YES' : '❌ NO'}`);
        
        if (data.isAuthenticated) {
          console.log('  💡 You can test download functionality');
        } else {
          console.log('  💡 Log in to test protected downloads');
          console.log(`  🔗 Login URL: ${BASE_URL}/login`);
        }
      } else {
        console.log('  ❌ Auth check failed');
      }
    } catch (error) {
      console.log(`  ❌ Auth check error: ${error.message}`);
    }
  }

  /**
   * Provide testing instructions
   */
  function showTestingInstructions() {
    console.log('\n📋 MANUAL TESTING INSTRUCTIONS');
    console.log('=' .repeat(40));
    console.log('1. Navigate to: /tools/excel-to-pdf');
    console.log('2. Upload the test Excel file (test-excel-sample.xlsx)');
    console.log('3. Click "Convert to PDF"');
    console.log('4. Try to download (should require login)');
    console.log('5. Log in and verify download works');
    console.log('6. Check database for download tracking');
    console.log('\n🔗 Quick Links:');
    console.log(`   Excel to PDF: ${BASE_URL}/tools/excel-to-pdf`);
    console.log(`   Login Page: ${BASE_URL}/login`);
    console.log(`   Admin Dashboard: ${BASE_URL}/admin`);
  }

  /**
   * Main test runner
   */
  async function runAllTests() {
    try {
      await testAPIEndpoints();
      await checkAuthStatus();
      await testDownloadTracking();
      testProtectedDownloadHook();
      testCurrentPage();
      simulateFileUploadTest();
      showTestingInstructions();
      
      console.log('\n✅ Browser tests completed!');
      console.log('📖 Follow the manual testing instructions above');
      
    } catch (error) {
      console.error('\n❌ Test execution failed:', error);
    }
  }

  // Run all tests
  runAllTests();

  // Make functions available globally for manual testing
  window.authWorkflowTest = {
    testAPIEndpoints,
    testDownloadTracking,
    checkAuthStatus,
    testCurrentPage,
    runAllTests
  };

  console.log('\n💡 Functions available for manual testing:');
  console.log('   authWorkflowTest.checkAuthStatus()');
  console.log('   authWorkflowTest.testDownloadTracking()');
  console.log('   authWorkflowTest.runAllTests()');

})();
