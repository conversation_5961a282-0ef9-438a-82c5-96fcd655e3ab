import { NextRequest, NextResponse } from 'next/server';
import { PDFDocument, degrees } from 'pdf-lib';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const rotation = parseInt(formData.get('rotation') as string || '90');
    const pageSelection = formData.get('pageSelection') as string || 'all';
    const specificPages = formData.get('specificPages') as string || '';

    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }

    if (file.type !== 'application/pdf') {
      return NextResponse.json(
        { error: 'File must be a PDF document' },
        { status: 400 }
      );
    }

    // Validate rotation angle
    const validRotations = [90, 180, 270, -90, -180, -270];
    if (!validRotations.includes(rotation)) {
      return NextResponse.json(
        { error: 'Invalid rotation angle. Must be 90, 180, 270, -90, -180, or -270 degrees' },
        { status: 400 }
      );
    }

    // Convert file to buffer
    const arrayBuffer = await file.arrayBuffer();
    
    // Load the PDF document
    const pdfDoc = await PDFDocument.load(arrayBuffer);
    const totalPages = pdfDoc.getPageCount();

    if (totalPages === 0) {
      return NextResponse.json(
        { error: 'PDF document has no pages' },
        { status: 400 }
      );
    }

    let pagesToRotate: number[] = [];

    if (pageSelection === 'all') {
      // Rotate all pages
      pagesToRotate = Array.from({ length: totalPages }, (_, i) => i);
    } else if (pageSelection === 'specific' && specificPages) {
      // Parse specific pages (e.g., "1,3,5-7")
      pagesToRotate = parsePageNumbers(specificPages, totalPages);
    } else {
      return NextResponse.json(
        { error: 'Invalid page selection. Use "all" or "specific" with page numbers.' },
        { status: 400 }
      );
    }

    if (pagesToRotate.length === 0) {
      return NextResponse.json(
        { error: 'No valid pages specified for rotation' },
        { status: 400 }
      );
    }

    // Get all pages
    const pages = pdfDoc.getPages();
    let rotatedCount = 0;

    // Rotate specified pages
    for (const pageIndex of pagesToRotate) {
      if (pageIndex >= 0 && pageIndex < pages.length) {
        const page = pages[pageIndex];
        
        // Get current rotation and add new rotation
        const currentRotation = page.getRotation();
        const newRotation = currentRotation.angle + rotation;
        
        // Normalize rotation to 0-359 degrees
        const normalizedRotation = ((newRotation % 360) + 360) % 360;
        
        page.setRotation(degrees(normalizedRotation));
        rotatedCount++;
      }
    }

    if (rotatedCount === 0) {
      return NextResponse.json(
        { error: 'No pages were rotated. Please check your page selection.' },
        { status: 400 }
      );
    }

    // Save the rotated PDF
    const pdfBytes = await pdfDoc.save({
      useObjectStreams: true,
      addDefaultPage: false,
    });

    // Generate filename
    const originalName = file.name.replace(/\.pdf$/i, '');
    const rotationLabel = rotation > 0 ? `${rotation}deg_clockwise` : `${Math.abs(rotation)}deg_counterclockwise`;
    const pdfFilename = `${originalName}_rotated_${rotationLabel}.pdf`;

    // Create response with rotated PDF
    const response = new NextResponse(pdfBytes, {
      status: 200,
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `attachment; filename="${pdfFilename}"`,
        'X-Original-Size': arrayBuffer.byteLength.toString(),
        'X-Rotated-Size': pdfBytes.length.toString(),
        'X-Total-Pages': totalPages.toString(),
        'X-Rotated-Pages': rotatedCount.toString(),
        'X-Rotation-Angle': rotation.toString(),
      },
    });

    return response;

  } catch (error) {
    console.error('PDF rotation error:', error);
    
    if (error instanceof Error) {
      if (error.message.includes('Invalid PDF')) {
        return NextResponse.json(
          { error: 'Invalid PDF file. Please ensure the file is not corrupted.' },
          { status: 400 }
        );
      }
      if (error.message.includes('password')) {
        return NextResponse.json(
          { error: 'Password-protected PDF files are not supported.' },
          { status: 400 }
        );
      }
    }

    return NextResponse.json(
      { error: 'Failed to rotate PDF. Please ensure the file is a valid PDF document.' },
      { status: 500 }
    );
  }
}

// Helper function to parse page numbers like "1,3,5-7"
function parsePageNumbers(pageString: string, totalPages: number): number[] {
  const pageNumbers: number[] = [];
  const parts = pageString.split(',').map(s => s.trim());
  
  for (const part of parts) {
    if (part.includes('-')) {
      // Handle range like "5-7"
      const [startStr, endStr] = part.split('-').map(s => s.trim());
      const start = Math.max(1, parseInt(startStr) || 1);
      const end = Math.min(totalPages, parseInt(endStr) || totalPages);
      
      for (let i = start; i <= end; i++) {
        pageNumbers.push(i - 1); // Convert to 0-based index
      }
    } else {
      // Handle single page like "3"
      const pageNum = parseInt(part);
      if (pageNum >= 1 && pageNum <= totalPages) {
        pageNumbers.push(pageNum - 1); // Convert to 0-based index
      }
    }
  }
  
  // Remove duplicates and sort
  return Array.from(new Set(pageNumbers)).sort((a, b) => a - b);
}

export async function GET() {
  return NextResponse.json(
    { 
      message: 'PDF Rotation API',
      supportedInput: 'PDF',
      outputFormat: 'PDF',
      rotationAngles: [90, 180, 270, -90, -180, -270],
      pageSelection: ['all', 'specific'],
      maxFileSize: '10MB',
      note: 'Rotates PDF pages by specified angle. Supports all pages or specific page ranges.'
    },
    { status: 200 }
  );
}
