#!/bin/bash

# =============================================================================
# TOOLRAPTER - ENVIRONMENT SETUP SCRIPT
# =============================================================================
# Comprehensive environment setup for development, staging, and production
# Usage: ./scripts/setup-environment.sh [development|staging|production]

set -e

# =============================================================================
# CONFIGURATION
# =============================================================================
ENVIRONMENT=${1:-development}
PROJECT_NAME="toolrapter"
DOMAIN="toolrapter.com"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

# =============================================================================
# HELPER FUNCTIONS
# =============================================================================
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }
log_step() { echo -e "${PURPLE}[STEP]${NC} $1"; }

# Check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Generate secure random string
generate_secret() {
    openssl rand -base64 32 | tr -d "=+/" | cut -c1-32
}

# =============================================================================
# ENVIRONMENT VALIDATION
# =============================================================================
validate_environment() {
    log_step "Validating environment: $ENVIRONMENT"
    
    case $ENVIRONMENT in
        development|staging|production)
            log_success "Environment '$ENVIRONMENT' is valid"
            ;;
        *)
            log_error "Invalid environment. Use: development, staging, or production"
            exit 1
            ;;
    esac
}

# =============================================================================
# DEPENDENCY INSTALLATION
# =============================================================================
install_dependencies() {
    log_step "Installing dependencies for $ENVIRONMENT environment"
    
    # Check Node.js
    if ! command_exists node; then
        log_error "Node.js is not installed. Please install Node.js 18+ first."
        exit 1
    fi
    
    NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 18 ]; then
        log_error "Node.js version 18+ is required. Current version: $(node --version)"
        exit 1
    fi
    
    log_success "Node.js $(node --version) detected"
    
    # Install npm dependencies
    log_info "Installing npm dependencies..."
    if [ "$ENVIRONMENT" = "production" ]; then
        npm ci --production --prefer-offline
    else
        npm ci --prefer-offline
    fi
    
    log_success "Dependencies installed successfully"
}

# =============================================================================
# ENVIRONMENT FILE SETUP
# =============================================================================
setup_environment_file() {
    log_step "Setting up environment file for $ENVIRONMENT"
    
    ENV_FILE=".env.local"
    if [ "$ENVIRONMENT" = "production" ]; then
        ENV_FILE=".env.production"
    elif [ "$ENVIRONMENT" = "staging" ]; then
        ENV_FILE=".env.staging"
    fi
    
    # Create environment file from template
    if [ ! -f "$ENV_FILE" ]; then
        log_info "Creating $ENV_FILE from template..."
        cp .env.example "$ENV_FILE"
        
        # Generate secure secrets
        NEXTAUTH_SECRET=$(generate_secret)
        CSRF_SECRET=$(generate_secret)
        
        # Update environment-specific values
        case $ENVIRONMENT in
            development)
                sed -i.bak "s|NODE_ENV=development|NODE_ENV=development|g" "$ENV_FILE"
                sed -i.bak "s|NEXT_PUBLIC_BASE_URL=http://localhost:3000|NEXT_PUBLIC_BASE_URL=http://localhost:3000|g" "$ENV_FILE"
                sed -i.bak "s|NEXTAUTH_URL=http://localhost:3000|NEXTAUTH_URL=http://localhost:3000|g" "$ENV_FILE"
                ;;
            staging)
                sed -i.bak "s|NODE_ENV=development|NODE_ENV=production|g" "$ENV_FILE"
                sed -i.bak "s|NEXT_PUBLIC_BASE_URL=http://localhost:3000|NEXT_PUBLIC_BASE_URL=https://staging.$DOMAIN|g" "$ENV_FILE"
                sed -i.bak "s|NEXTAUTH_URL=http://localhost:3000|NEXTAUTH_URL=https://staging.$DOMAIN|g" "$ENV_FILE"
                ;;
            production)
                sed -i.bak "s|NODE_ENV=development|NODE_ENV=production|g" "$ENV_FILE"
                sed -i.bak "s|NEXT_PUBLIC_BASE_URL=http://localhost:3000|NEXT_PUBLIC_BASE_URL=https://$DOMAIN|g" "$ENV_FILE"
                sed -i.bak "s|NEXTAUTH_URL=http://localhost:3000|NEXTAUTH_URL=https://$DOMAIN|g" "$ENV_FILE"
                ;;
        esac
        
        # Update secrets
        sed -i.bak "s|your-super-secure-secret-key-minimum-32-characters|$NEXTAUTH_SECRET|g" "$ENV_FILE"
        sed -i.bak "s|your-csrf-secret-key-minimum-32-characters|$CSRF_SECRET|g" "$ENV_FILE"
        
        # Clean up backup files
        rm -f "$ENV_FILE.bak"
        
        log_success "$ENV_FILE created with secure defaults"
        log_warning "Please update the following variables in $ENV_FILE:"
        echo "  - MONGODB_URI (MongoDB connection string)"
        echo "  - GOOGLE_CLIENT_ID (Google OAuth client ID)"
        echo "  - GOOGLE_CLIENT_SECRET (Google OAuth client secret)"
        echo "  - SMTP_* (Email configuration)"
    else
        log_info "$ENV_FILE already exists, skipping creation"
    fi
}

# =============================================================================
# DATABASE SETUP
# =============================================================================
setup_database() {
    log_step "Setting up database for $ENVIRONMENT"
    
    # Check if MongoDB URI is configured
    if [ -f ".env.local" ] && grep -q "MONGODB_URI=mongodb" .env.local; then
        log_info "Testing database connection..."
        
        # Test database connection (if MongoDB is available)
        if command_exists mongosh; then
            MONGODB_URI=$(grep "MONGODB_URI=" .env.local | cut -d'=' -f2)
            if mongosh "$MONGODB_URI" --eval "db.runCommand('ping')" > /dev/null 2>&1; then
                log_success "Database connection successful"
            else
                log_warning "Database connection failed. Please check MONGODB_URI"
            fi
        else
            log_info "MongoDB shell not available, skipping connection test"
        fi
    else
        log_warning "MongoDB URI not configured. Please update MONGODB_URI in your environment file"
    fi
}

# =============================================================================
# FONT SETUP
# =============================================================================
setup_fonts() {
    log_step "Setting up fonts"
    
    if [ -f "scripts/download-fonts.js" ]; then
        log_info "Downloading required fonts..."
        npm run download-fonts
        log_success "Fonts downloaded successfully"
    else
        log_warning "Font download script not found, skipping"
    fi
}

# =============================================================================
# BUILD SETUP
# =============================================================================
setup_build() {
    log_step "Setting up build for $ENVIRONMENT"
    
    if [ "$ENVIRONMENT" != "development" ]; then
        log_info "Building application for $ENVIRONMENT..."
        npm run build
        log_success "Build completed successfully"
    else
        log_info "Skipping build for development environment"
    fi
}

# =============================================================================
# SECURITY SETUP
# =============================================================================
setup_security() {
    log_step "Setting up security configurations"
    
    # Check for security best practices
    log_info "Validating security configurations..."
    
    # Check if secrets are properly configured
    if [ -f ".env.local" ]; then
        if grep -q "your-super-secure-secret" .env.local; then
            log_warning "Default secrets detected. Please update NEXTAUTH_SECRET and CSRF_SECRET"
        else
            log_success "Custom secrets configured"
        fi
    fi
    
    # Validate TypeScript configuration
    if [ -f "tsconfig.json" ]; then
        if grep -q '"strict": true' tsconfig.json; then
            log_success "TypeScript strict mode enabled"
        else
            log_warning "Consider enabling TypeScript strict mode for better type safety"
        fi
    fi
    
    log_success "Security validation completed"
}

# =============================================================================
# PERFORMANCE SETUP
# =============================================================================
setup_performance() {
    log_step "Setting up performance optimizations"
    
    # Check Next.js configuration
    if [ -f "next.config.js" ]; then
        if grep -q "optimizePackageImports" next.config.js; then
            log_success "Package import optimization enabled"
        else
            log_info "Consider enabling package import optimization"
        fi
    fi
    
    # Check for performance monitoring
    if [ -f "scripts/performance-monitor.js" ]; then
        log_success "Performance monitoring script available"
    else
        log_info "Performance monitoring script not found"
    fi
    
    log_success "Performance setup completed"
}

# =============================================================================
# VALIDATION
# =============================================================================
validate_setup() {
    log_step "Validating setup for $ENVIRONMENT"
    
    # Run TypeScript check
    log_info "Running TypeScript type check..."
    if npm run type-check; then
        log_success "TypeScript compilation successful"
    else
        log_error "TypeScript compilation failed"
        return 1
    fi
    
    # Run linting
    log_info "Running ESLint..."
    if npm run lint; then
        log_success "Linting passed"
    else
        log_warning "Linting issues found"
    fi
    
    # Test build (for non-development environments)
    if [ "$ENVIRONMENT" != "development" ]; then
        log_info "Testing build process..."
        if npm run build > /dev/null 2>&1; then
            log_success "Build test passed"
        else
            log_error "Build test failed"
            return 1
        fi
    fi
    
    log_success "Setup validation completed"
}

# =============================================================================
# MAIN SETUP PROCESS
# =============================================================================
main() {
    echo "🚀 ToolRapter Environment Setup"
    echo "==============================="
    echo "Environment: $ENVIRONMENT"
    echo "Project: $PROJECT_NAME"
    echo ""
    
    # Validate environment
    validate_environment
    
    # Install dependencies
    install_dependencies
    
    # Setup environment file
    setup_environment_file
    
    # Setup database
    setup_database
    
    # Setup fonts
    setup_fonts
    
    # Setup build
    setup_build
    
    # Setup security
    setup_security
    
    # Setup performance
    setup_performance
    
    # Validate setup
    if validate_setup; then
        echo ""
        log_success "🎉 Environment setup completed successfully!"
        echo ""
        echo "📋 Next Steps:"
        case $ENVIRONMENT in
            development)
                echo "1. Update environment variables in .env.local"
                echo "2. Start development server: npm run dev"
                echo "3. Open http://localhost:3000"
                ;;
            staging)
                echo "1. Update environment variables in .env.staging"
                echo "2. Deploy to staging server"
                echo "3. Test at https://staging.$DOMAIN"
                ;;
            production)
                echo "1. Update environment variables in .env.production"
                echo "2. Configure GitHub secrets for deployment"
                echo "3. Deploy to production: ./scripts/deploy-production.sh"
                echo "4. Monitor at https://$DOMAIN"
                ;;
        esac
    else
        log_error "Setup validation failed. Please fix the issues and try again."
        exit 1
    fi
}

# =============================================================================
# SCRIPT EXECUTION
# =============================================================================
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
