"use client";

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Progress } from './progress';

interface ConversionProgressProps {
  isConverting: boolean;
  progress: number;
  fileName?: string;
  conversionType?: string;
  estimatedTime?: number;
  onCancel?: () => void;
}

export function ConversionProgress({
  isConverting,
  progress,
  fileName,
  conversionType = 'Converting',
  estimatedTime,
  onCancel
}: ConversionProgressProps) {
  const [elapsedTime, setElapsedTime] = useState(0);
  const [currentStage, setCurrentStage] = useState('Preparing');

  useEffect(() => {
    if (!isConverting) {
      setElapsedTime(0);
      setCurrentStage('Preparing');
      return;
    }

    const startTime = Date.now();
    const interval = setInterval(() => {
      setElapsedTime(Math.floor((Date.now() - startTime) / 1000));
    }, 1000);

    return () => clearInterval(interval);
  }, [isConverting]);

  useEffect(() => {
    if (progress < 20) {
      setCurrentStage('Preparing file');
    } else if (progress < 50) {
      setCurrentStage('Processing content');
    } else if (progress < 80) {
      setCurrentStage('Converting format');
    } else if (progress < 95) {
      setCurrentStage('Finalizing');
    } else {
      setCurrentStage('Complete');
    }
  }, [progress]);

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return mins > 0 ? `${mins}m ${secs}s` : `${secs}s`;
  };

  const getProgressColor = () => {
    if (progress < 30) return 'bg-blue-500';
    if (progress < 70) return 'bg-yellow-500';
    if (progress < 90) return 'bg-orange-500';
    return 'bg-green-500';
  };

  if (!isConverting) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }}
        className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6 shadow-lg"
      >
        {/* Header */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
              className="w-6 h-6 text-blue-500"
            >
              <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                />
              </svg>
            </motion.div>
            <div>
              <h3 className="font-semibold text-gray-900 dark:text-gray-100">
                {conversionType}
              </h3>
              {fileName && (
                <p className="text-sm text-gray-500 dark:text-gray-400 truncate max-w-xs">
                  {fileName}
                </p>
              )}
            </div>
          </div>
          
          {onCancel && (
            <button
              onClick={onCancel}
              className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
              aria-label="Cancel conversion"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          )}
        </div>

        {/* Progress Bar */}
        <div className="space-y-2 mb-4">
          <div className="flex justify-between text-sm">
            <span className="text-gray-600 dark:text-gray-400">{currentStage}</span>
            <span className="font-medium text-gray-900 dark:text-gray-100">{Math.round(progress)}%</span>
          </div>
          
          <div className="relative">
            <Progress value={progress} className="h-2" />
            <motion.div
              className={`absolute top-0 left-0 h-2 rounded-full ${getProgressColor()}`}
              initial={{ width: 0 }}
              animate={{ width: `${progress}%` }}
              transition={{ duration: 0.5, ease: "easeOut" }}
            />
          </div>
        </div>

        {/* Status Information */}
        <div className="flex justify-between items-center text-sm text-gray-500 dark:text-gray-400">
          <div className="flex items-center space-x-4">
            <span>Elapsed: {formatTime(elapsedTime)}</span>
            {estimatedTime && elapsedTime < estimatedTime && (
              <span>Est. remaining: {formatTime(estimatedTime - elapsedTime)}</span>
            )}
          </div>
          
          <div className="flex items-center space-x-1">
            <motion.div
              animate={{ scale: [1, 1.2, 1] }}
              transition={{ duration: 1, repeat: Infinity }}
              className="w-2 h-2 bg-green-500 rounded-full"
            />
            <span>Processing</span>
          </div>
        </div>

        {/* Stage Indicators */}
        <div className="mt-4 flex justify-between">
          {['Prepare', 'Process', 'Convert', 'Finalize'].map((stage, index) => {
            const stageProgress = (index + 1) * 25;
            const isActive = progress >= stageProgress - 25;
            const isComplete = progress >= stageProgress;
            
            return (
              <div key={stage} className="flex flex-col items-center space-y-1">
                <div
                  className={`w-3 h-3 rounded-full transition-colors ${
                    isComplete
                      ? 'bg-green-500'
                      : isActive
                      ? 'bg-blue-500'
                      : 'bg-gray-300 dark:bg-gray-600'
                  }`}
                />
                <span
                  className={`text-xs ${
                    isActive
                      ? 'text-gray-900 dark:text-gray-100'
                      : 'text-gray-400 dark:text-gray-500'
                  }`}
                >
                  {stage}
                </span>
              </div>
            );
          })}
        </div>
      </motion.div>
    </AnimatePresence>
  );
}

// Enhanced file upload progress with drag and drop feedback
export function FileUploadProgress({
  isUploading,
  progress,
  fileName,
  fileSize,
  uploadSpeed,
  onCancel
}: {
  isUploading: boolean;
  progress: number;
  fileName: string;
  fileSize: number;
  uploadSpeed?: number;
  onCancel?: () => void;
}) {
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatSpeed = (bytesPerSecond: number) => {
    return formatFileSize(bytesPerSecond) + '/s';
  };

  if (!isUploading) return null;

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.95 }}
      className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4"
    >
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-3">
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 1.5, repeat: Infinity, ease: "linear" }}
            className="w-5 h-5 text-blue-600"
          >
            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"
              />
            </svg>
          </motion.div>
          <div>
            <p className="font-medium text-blue-900 dark:text-blue-100 truncate max-w-xs">
              {fileName}
            </p>
            <p className="text-sm text-blue-600 dark:text-blue-300">
              {formatFileSize(fileSize)}
              {uploadSpeed && ` • ${formatSpeed(uploadSpeed)}`}
            </p>
          </div>
        </div>
        
        {onCancel && (
          <button
            onClick={onCancel}
            className="text-blue-400 hover:text-blue-600 transition-colors"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        )}
      </div>

      <div className="space-y-2">
        <div className="flex justify-between text-sm">
          <span className="text-blue-700 dark:text-blue-300">Uploading...</span>
          <span className="font-medium text-blue-900 dark:text-blue-100">{Math.round(progress)}%</span>
        </div>
        <Progress value={progress} className="h-2 bg-blue-100 dark:bg-blue-900" />
      </div>
    </motion.div>
  );
}
