import { NextResponse } from 'next/server';
import { generateCompleteSitemap } from '@/lib/sitemap';

export async function GET() {
  try {
    // In a real implementation, you would fetch blog posts from your database
    // For now, we'll use an empty array
    const blogPosts: Array<{ slug: string; publishDate: string; modifiedDate?: string }> = [];
    
    // Generate the complete sitemap
    const sitemap = generateCompleteSitemap(blogPosts, {
      baseUrl: 'https://toolrapter.com'
    });
    
    return new NextResponse(sitemap, {
      status: 200,
      headers: {
        'Content-Type': 'application/xml',
        'Cache-Control': 'public, max-age=86400, s-maxage=86400', // Cache for 24 hours
      },
    });
  } catch (error) {
    console.error('Error generating sitemap:', error);
    return new NextResponse('Error generating sitemap', { status: 500 });
  }
}
