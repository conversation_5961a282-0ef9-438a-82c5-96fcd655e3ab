#!/bin/bash

# 🔧 ChunkLoadError Fix Script
# This script fixes the Next.js ChunkLoadError by cleaning cache and reinstalling dependencies

set -e  # Exit on error

echo "🔧 Starting ChunkLoadError Fix..."
echo ""

# Step 1: Clean build cache
echo "📦 Step 1/5: Cleaning build cache..."
if [ -d ".next" ]; then
  rm -rf .next
  echo "✅ Removed .next directory"
else
  echo "ℹ️  .next directory not found"
fi

# Step 2: Clean node_modules
echo ""
echo "📦 Step 2/5: Cleaning node_modules..."
if [ -d "node_modules" ]; then
  rm -rf node_modules
  echo "✅ Removed node_modules directory"
else
  echo "ℹ️  node_modules directory not found"
fi

# Step 3: Clean pnpm store
echo ""
echo "📦 Step 3/5: Cleaning pnpm store..."
if command -v pnpm &> /dev/null; then
  pnpm store prune
  echo "✅ Pruned pnpm store"
else
  echo "⚠️  pnpm not found, skipping store prune"
fi

# Step 4: Reinstall dependencies
echo ""
echo "📦 Step 4/5: Reinstalling dependencies..."
if command -v pnpm &> /dev/null; then
  pnpm install
  echo "✅ Dependencies reinstalled with pnpm"
else
  npm install
  echo "✅ Dependencies reinstalled with npm"
fi

# Step 5: Verify installation
echo ""
echo "📦 Step 5/5: Verifying installation..."
if command -v pnpm &> /dev/null; then
  NEXT_VERSION=$(pnpm list next --depth=0 2>/dev/null | grep next@ | awk '{print $2}')
else
  NEXT_VERSION=$(npm list next --depth=0 2>/dev/null | grep next@ | awk '{print $2}')
fi

echo "✅ Next.js version: $NEXT_VERSION"

# Final message
echo ""
echo "✅ ChunkLoadError fix completed successfully!"
echo ""
echo "🚀 Next steps:"
echo "   1. Run: pnpm dev (or npm run dev)"
echo "   2. Open: http://localhost:3000"
echo "   3. Check browser console for errors"
echo ""
echo "📋 Verification checklist:"
echo "   [ ] No ChunkLoadError in browser console"
echo "   [ ] All pages load without webpack errors"
echo "   [ ] Navigation between pages works smoothly"
echo ""

