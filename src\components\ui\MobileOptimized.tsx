"use client";

import { useState, useEffect, useCallback } from "react";

/**
 * Mobile-optimized wrapper component that adds touch interactions,
 * haptic feedback, and responsive behavior to child components
 */

interface MobileOptimizedProps {
  children: React.ReactNode;
  enableHaptic?: boolean;
  enableSwipe?: boolean;
  onSwipeLeft?: () => void;
  onSwipeRight?: () => void;
  className?: string;
}

interface TouchState {
  startX: number;
  startY: number;
  startTime: number;
  isPressed: boolean;
}

export default function MobileOptimized({
  children,
  enableHaptic = true,
  enableSwipe = false,
  onSwipeLeft,
  onSwipeRight,
  className = ""
}: MobileOptimizedProps) {
  const [touchState, setTouchState] = useState<TouchState>({
    startX: 0,
    startY: 0,
    startTime: 0,
    isPressed: false
  });

  // Haptic feedback function
  const triggerHaptic = useCallback((type: 'light' | 'medium' | 'heavy' = 'light') => {
    if (!enableHaptic) return;
    
    // Check if device supports haptic feedback
    if ('vibrate' in navigator) {
      const patterns = {
        light: [10],
        medium: [20],
        heavy: [30]
      };
      navigator.vibrate(patterns[type]);
    }
    
    // For iOS devices with haptic feedback API
    if ('hapticFeedback' in window) {
      try {
        (window as any).hapticFeedback.impact(type);
      } catch (e) {
        // Silently fail if haptic feedback is not available
      }
    }
  }, [enableHaptic]);

  // Touch event handlers for swipe detection
  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    if (!enableSwipe) return;
    
    const touch = e.touches[0];
    setTouchState({
      startX: touch.clientX,
      startY: touch.clientY,
      startTime: Date.now(),
      isPressed: true
    });
    
    triggerHaptic('light');
  }, [enableSwipe, triggerHaptic]);

  const handleTouchEnd = useCallback((e: React.TouchEvent) => {
    if (!enableSwipe || !touchState.isPressed) return;
    
    const touch = e.changedTouches[0];
    const deltaX = touch.clientX - touchState.startX;
    const deltaY = touch.clientY - touchState.startY;
    const deltaTime = Date.now() - touchState.startTime;
    
    // Swipe detection thresholds
    const minSwipeDistance = 50;
    const maxSwipeTime = 300;
    const maxVerticalDistance = 100;
    
    // Check if it's a valid swipe
    if (
      Math.abs(deltaX) > minSwipeDistance &&
      Math.abs(deltaY) < maxVerticalDistance &&
      deltaTime < maxSwipeTime
    ) {
      if (deltaX > 0 && onSwipeRight) {
        onSwipeRight();
        triggerHaptic('medium');
      } else if (deltaX < 0 && onSwipeLeft) {
        onSwipeLeft();
        triggerHaptic('medium');
      }
    }
    
    setTouchState(prev => ({ ...prev, isPressed: false }));
  }, [enableSwipe, touchState, onSwipeLeft, onSwipeRight, triggerHaptic]);

  // Prevent default touch behaviors that might interfere
  const handleTouchMove = useCallback((e: React.TouchEvent) => {
    if (!enableSwipe) return;
    
    // Prevent scrolling during swipe detection
    if (touchState.isPressed) {
      e.preventDefault();
    }
  }, [enableSwipe, touchState.isPressed]);

  return (
    <div
      className={`mobile-optimized ${className}`}
      onTouchStart={handleTouchStart}
      onTouchEnd={handleTouchEnd}
      onTouchMove={handleTouchMove}
      style={{
        // Improve touch responsiveness
        touchAction: enableSwipe ? 'pan-y' : 'manipulation',
        // Prevent text selection on touch
        userSelect: 'none',
        WebkitUserSelect: 'none',
        // Prevent tap highlight
        WebkitTapHighlightColor: 'transparent',
        // Smooth transitions
        transition: 'transform 0.1s ease-out'
      }}
    >
      {children}
    </div>
  );
}

/**
 * Mobile-optimized button component with proper touch targets
 */
interface MobileButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  disabled?: boolean;
  variant?: 'primary' | 'secondary' | 'success' | 'danger';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  ariaLabel?: string;
}

export function MobileButton({
  children,
  onClick,
  disabled = false,
  variant = 'primary',
  size = 'md',
  className = "",
  ariaLabel
}: MobileButtonProps) {
  const [isPressed, setIsPressed] = useState(false);

  const variantClasses = {
    primary: 'bg-blue-600 hover:bg-blue-700 text-white',
    secondary: 'bg-gray-600 hover:bg-gray-700 text-white',
    success: 'bg-green-600 hover:bg-green-700 text-white',
    danger: 'bg-red-600 hover:bg-red-700 text-white'
  };

  const sizeClasses = {
    sm: 'px-3 py-2 text-sm min-h-[44px]', // Ensure minimum touch target
    md: 'px-4 py-3 text-base min-h-[48px]',
    lg: 'px-6 py-4 text-lg min-h-[52px]'
  };

  const handleTouchStart = () => {
    if (!disabled) {
      setIsPressed(true);
      // Haptic feedback
      if ('vibrate' in navigator) {
        navigator.vibrate([5]);
      }
    }
  };

  const handleTouchEnd = () => {
    setIsPressed(false);
  };

  const handleClick = () => {
    if (!disabled && onClick) {
      onClick();
      // Success haptic feedback
      if ('vibrate' in navigator) {
        navigator.vibrate([10]);
      }
    }
  };

  return (
    <button
      type="button"
      onClick={handleClick}
      onTouchStart={handleTouchStart}
      onTouchEnd={handleTouchEnd}
      disabled={disabled}
      aria-label={ariaLabel}
      className={`
        ${variantClasses[variant]}
        ${sizeClasses[size]}
        ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer active:scale-95'}
        ${isPressed ? 'scale-95' : 'scale-100'}
        ${className}
        font-medium rounded-lg transition-all duration-150 ease-out
        focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500
        touch-manipulation select-none
        flex items-center justify-center
      `}
      style={{
        WebkitTapHighlightColor: 'transparent',
        minWidth: '44px', // Minimum touch target width
        minHeight: '44px' // Minimum touch target height
      }}
    >
      {children}
    </button>
  );
}

/**
 * Mobile-optimized file upload area with drag and drop
 */
interface MobileFileUploadProps {
  onFileSelect: (file: File) => void;
  acceptedTypes?: string;
  maxSizeMB?: number;
  className?: string;
}

export function MobileFileUpload({
  onFileSelect,
  acceptedTypes = "*/*",
  maxSizeMB = 10,
  className = ""
}: MobileFileUploadProps) {
  const [isDragOver, setIsDragOver] = useState(false);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      onFileSelect(file);
      // Success haptic feedback
      if ('vibrate' in navigator) {
        navigator.vibrate([10, 50, 10]);
      }
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = () => {
    setIsDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    
    const file = e.dataTransfer.files[0];
    if (file) {
      onFileSelect(file);
      // Success haptic feedback
      if ('vibrate' in navigator) {
        navigator.vibrate([10, 50, 10]);
      }
    }
  };

  return (
    <div
      className={`
        ${className}
        relative border-2 border-dashed rounded-lg p-6
        ${isDragOver ? 'border-blue-500 bg-blue-50' : 'border-gray-300 bg-gray-50'}
        transition-all duration-200 ease-out
        min-h-[120px] flex flex-col items-center justify-center
        touch-manipulation
      `}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
    >
      <input
        type="file"
        accept={acceptedTypes}
        onChange={handleFileChange}
        className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
        aria-label="Select file to upload"
      />
      
      <div className="text-center">
        <svg
          className="mx-auto h-12 w-12 text-gray-400 mb-4"
          stroke="currentColor"
          fill="none"
          viewBox="0 0 48 48"
          aria-hidden="true"
        >
          <path
            d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
            strokeWidth={2}
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
        
        <p className="text-sm text-gray-600 mb-2">
          <span className="font-medium text-blue-600">Tap to select</span> or drag and drop
        </p>
        
        <p className="text-xs text-gray-500">
          Max file size: {maxSizeMB}MB
        </p>
      </div>
    </div>
  );
}
