"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

interface TimeZoneResult {
  sourceTime: string;
  targetTime: string;
  timeDifference: number;
  isDST: boolean;
  worldTimes: Array<{
    city: string;
    timezone: string;
    time: string;
    offset: string;
  }>;
}

export default function TimeZoneConverter() {
  const [sourceTime, setSourceTime] = useState<string>("");
  const [sourceDate, setSourceDate] = useState<string>("");
  const [sourceTimezone, setSourceTimezone] = useState<string>("America/New_York");
  const [targetTimezone, setTargetTimezone] = useState<string>("Europe/London");
  const [result, setResult] = useState<TimeZoneResult | null>(null);

  const timeZones = [
    { value: "America/New_York", label: "New York (EST/EDT)", offset: -5 },
    { value: "America/Los_Angeles", label: "Los Angeles (PST/PDT)", offset: -8 },
    { value: "America/Chicago", label: "Chicago (CST/CDT)", offset: -6 },
    { value: "America/Denver", label: "Denver (MST/MDT)", offset: -7 },
    { value: "Europe/London", label: "London (GMT/BST)", offset: 0 },
    { value: "Europe/Paris", label: "Paris (CET/CEST)", offset: 1 },
    { value: "Europe/Berlin", label: "Berlin (CET/CEST)", offset: 1 },
    { value: "Europe/Rome", label: "Rome (CET/CEST)", offset: 1 },
    { value: "Asia/Tokyo", label: "Tokyo (JST)", offset: 9 },
    { value: "Asia/Shanghai", label: "Shanghai (CST)", offset: 8 },
    { value: "Asia/Hong_Kong", label: "Hong Kong (HKT)", offset: 8 },
    { value: "Asia/Singapore", label: "Singapore (SGT)", offset: 8 },
    { value: "Asia/Dubai", label: "Dubai (GST)", offset: 4 },
    { value: "Asia/Kolkata", label: "Mumbai (IST)", offset: 5.5 },
    { value: "Australia/Sydney", label: "Sydney (AEST/AEDT)", offset: 10 },
    { value: "Australia/Melbourne", label: "Melbourne (AEST/AEDT)", offset: 10 },
    { value: "Pacific/Auckland", label: "Auckland (NZST/NZDT)", offset: 12 },
    { value: "America/Sao_Paulo", label: "São Paulo (BRT)", offset: -3 },
    { value: "America/Mexico_City", label: "Mexico City (CST/CDT)", offset: -6 },
    { value: "Africa/Cairo", label: "Cairo (EET)", offset: 2 },
    { value: "UTC", label: "UTC (Coordinated Universal Time)", offset: 0 }
  ];

  const convertTime = () => {
    if (!sourceTime || !sourceDate) {
      alert("Please enter both date and time");
      return;
    }

    try {
      // Create date object from input
      const inputDateTime = new Date(`${sourceDate}T${sourceTime}`);
      
      // Get timezone offset for source timezone
      const sourceOffset = getTimezoneOffset(sourceTimezone, inputDateTime);
      const targetOffset = getTimezoneOffset(targetTimezone, inputDateTime);
      
      // Convert to UTC first, then to target timezone
      const utcTime = new Date(inputDateTime.getTime() - (sourceOffset * 60000));
      const targetTime = new Date(utcTime.getTime() + (targetOffset * 60000));
      
      const timeDifference = (targetOffset - sourceOffset) / 60; // in hours
      
      // Check if DST is in effect
      const isDST = isDaylightSavingTime(inputDateTime, sourceTimezone);
      
      // Generate world times
      const worldTimes = generateWorldTimes(utcTime);
      
      setResult({
        sourceTime: inputDateTime.toLocaleString('en-US', {
          timeZone: sourceTimezone,
          weekday: 'long',
          year: 'numeric',
          month: 'long',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit'
        }),
        targetTime: targetTime.toLocaleString('en-US', {
          timeZone: targetTimezone,
          weekday: 'long',
          year: 'numeric',
          month: 'long',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit'
        }),
        timeDifference,
        isDST,
        worldTimes
      });
    } catch (error) {
      alert("Invalid date or time format");
    }
  };

  const getTimezoneOffset = (timezone: string, date: Date): number => {
    // Create a date in the specified timezone
    const utcDate = new Date(date.toLocaleString('en-US', { timeZone: 'UTC' }));
    const tzDate = new Date(date.toLocaleString('en-US', { timeZone: timezone }));
    
    return (utcDate.getTime() - tzDate.getTime()) / 60000; // in minutes
  };

  const isDaylightSavingTime = (date: Date, timezone: string): boolean => {
    const jan = new Date(date.getFullYear(), 0, 1);
    const jul = new Date(date.getFullYear(), 6, 1);
    
    const janOffset = getTimezoneOffset(timezone, jan);
    const julOffset = getTimezoneOffset(timezone, jul);
    const currentOffset = getTimezoneOffset(timezone, date);
    
    return currentOffset !== Math.max(janOffset, julOffset);
  };

  const generateWorldTimes = (utcTime: Date) => {
    const majorCities = [
      { city: "New York", timezone: "America/New_York" },
      { city: "Los Angeles", timezone: "America/Los_Angeles" },
      { city: "London", timezone: "Europe/London" },
      { city: "Paris", timezone: "Europe/Paris" },
      { city: "Tokyo", timezone: "Asia/Tokyo" },
      { city: "Sydney", timezone: "Australia/Sydney" },
      { city: "Dubai", timezone: "Asia/Dubai" },
      { city: "Mumbai", timezone: "Asia/Kolkata" }
    ];

    return majorCities.map(city => {
      const cityTime = new Date(utcTime.toLocaleString('en-US', { timeZone: city.timezone }));
      const offset = getTimezoneOffset(city.timezone, utcTime) / -60; // Convert to hours and flip sign
      
      return {
        city: city.city,
        timezone: city.timezone,
        time: cityTime.toLocaleString('en-US', {
          timeZone: city.timezone,
          hour: '2-digit',
          minute: '2-digit',
          hour12: true
        }),
        offset: `UTC${offset >= 0 ? '+' : ''}${offset}`
      };
    });
  };

  const setCurrentTime = () => {
    const now = new Date();
    setSourceDate(now.toISOString().split('T')[0]);
    setSourceTime(now.toTimeString().slice(0, 5));
  };

  const reset = () => {
    setSourceTime("");
    setSourceDate("");
    setSourceTimezone("America/New_York");
    setTargetTimezone("Europe/London");
    setResult(null);
  };

  const swapTimezones = () => {
    const temp = sourceTimezone;
    setSourceTimezone(targetTimezone);
    setTargetTimezone(temp);
  };

  // Auto-convert when inputs change
  useEffect(() => {
    if (sourceTime && sourceDate) {
      convertTime();
    }
  }, [sourceTime, sourceDate, sourceTimezone, targetTimezone]);

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Time Zone Converter</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Input Section */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div>
                <Label htmlFor="source-date">Date</Label>
                <Input
                  id="source-date"
                  type="date"
                  value={sourceDate}
                  onChange={(e) => setSourceDate(e.target.value)}
                />
              </div>

              <div>
                <Label htmlFor="source-time">Time</Label>
                <Input
                  id="source-time"
                  type="time"
                  value={sourceTime}
                  onChange={(e) => setSourceTime(e.target.value)}
                />
              </div>

              <div>
                <Label htmlFor="source-timezone">From Time Zone</Label>
                <Select value={sourceTimezone} onValueChange={setSourceTimezone}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {timeZones.map((tz) => (
                      <SelectItem key={tz.value} value={tz.value}>
                        {tz.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-4">
              <div>
                <Label htmlFor="target-timezone">To Time Zone</Label>
                <Select value={targetTimezone} onValueChange={setTargetTimezone}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {timeZones.map((tz) => (
                      <SelectItem key={tz.value} value={tz.value}>
                        {tz.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Button onClick={setCurrentTime} variant="outline" className="w-full">
                  Use Current Time
                </Button>
                <Button onClick={swapTimezones} variant="outline" className="w-full">
                  Swap Time Zones
                </Button>
              </div>

              <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <h3 className="font-semibold mb-2">Quick Info</h3>
                <div className="space-y-1 text-sm">
                  <div>Current UTC: {new Date().toUTCString()}</div>
                  <div>Your Local: {new Date().toLocaleString()}</div>
                </div>
              </div>
            </div>
          </div>

          {/* Results */}
          {result && (
            <div className="space-y-6">
              {/* Main Conversion Result */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card className="bg-blue-50 dark:bg-blue-900/20">
                  <CardContent className="pt-6">
                    <div className="text-center space-y-2">
                      <div className="text-sm text-muted-foreground">Source Time</div>
                      <div className="text-lg font-bold text-blue-600 dark:text-blue-400">
                        {result.sourceTime}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {timeZones.find(tz => tz.value === sourceTimezone)?.label}
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-green-50 dark:bg-green-900/20">
                  <CardContent className="pt-6">
                    <div className="text-center space-y-2">
                      <div className="text-sm text-muted-foreground">Target Time</div>
                      <div className="text-lg font-bold text-green-600 dark:text-green-400">
                        {result.targetTime}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {timeZones.find(tz => tz.value === targetTimezone)?.label}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Time Difference */}
              <Card>
                <CardContent className="pt-6">
                  <div className="text-center space-y-2">
                    <div className="text-sm text-muted-foreground">Time Difference</div>
                    <div className="text-2xl font-bold">
                      {result.timeDifference > 0 ? '+' : ''}{result.timeDifference} hours
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {result.isDST ? "Daylight Saving Time is in effect" : "Standard Time"}
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* World Clock */}
              <Card>
                <CardHeader>
                  <CardTitle>World Clock</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    {result.worldTimes.map((worldTime, index) => (
                      <div key={index} className="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg text-center">
                        <div className="font-semibold">{worldTime.city}</div>
                        <div className="text-lg font-bold">{worldTime.time}</div>
                        <div className="text-sm text-muted-foreground">{worldTime.offset}</div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          <div className="flex gap-4">
            <Button onClick={reset} variant="outline" className="flex-1">
              Reset
            </Button>
          </div>

          {/* Tips */}
          <div className="p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
            <h3 className="font-semibold mb-2">Time Zone Tips</h3>
            <ul className="text-sm space-y-1">
              <li>• Daylight Saving Time changes can affect calculations</li>
              <li>• UTC (Coordinated Universal Time) is the global time standard</li>
              <li>• Some countries don't observe Daylight Saving Time</li>
              <li>• Business hours typically overlap most between 9 AM - 11 AM EST</li>
              <li>• Consider cultural differences when scheduling international meetings</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
