"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

interface FreelancerResult {
  hourlyRate: number;
  dailyRate: number;
  weeklyRate: number;
  monthlyRate: number;
  annualIncome: number;
  effectiveHourlyRate: number;
  profitMargin: number;
  breakdownDetails: {
    billableHours: number;
    totalExpenses: number;
    taxes: number;
    netIncome: number;
    overhead: number;
  };
}

export default function FreelancerRateCalculator() {
  // Desired Income
  const [desiredAnnualIncome, setDesiredAnnualIncome] = useState<number>(75000);
  const [billableHoursPerWeek, setBillableHoursPerWeek] = useState<number>(30);
  const [weeksPerYear, setWeeksPerYear] = useState<number>(48);

  // Expenses
  const [businessExpenses, setBusinessExpenses] = useState<number>(12000);
  const [healthInsurance, setHealthInsurance] = useState<number>(6000);
  const [retirement, setRetirement] = useState<number>(7500);
  const [equipment, setEquipment] = useState<number>(3000);
  const [marketing, setMarketing] = useState<number>(2000);
  const [otherExpenses, setOtherExpenses] = useState<number>(1500);

  // Taxes and Benefits
  const [taxRate, setTaxRate] = useState<number>(25);
  const [profitMarginTarget, setProfitMarginTarget] = useState<number>(20);

  // Time Allocation
  const [adminTimePercent, setAdminTimePercent] = useState<number>(20);
  const [marketingTimePercent, setMarketingTimePercent] = useState<number>(15);

  const [result, setResult] = useState<FreelancerResult | null>(null);

  const calculateFreelancerRate = () => {
    // Calculate total expenses
    const totalExpenses = businessExpenses + healthInsurance + retirement + 
                         equipment + marketing + otherExpenses;

    // Calculate taxes on desired income
    const taxes = (desiredAnnualIncome * taxRate) / 100;

    // Calculate total revenue needed (income + expenses + taxes + profit margin)
    const profitAmount = (desiredAnnualIncome * profitMarginTarget) / 100;
    const totalRevenueNeeded = desiredAnnualIncome + totalExpenses + taxes + profitAmount;

    // Calculate billable hours
    const totalBillableHours = billableHoursPerWeek * weeksPerYear;

    // Calculate base hourly rate
    const baseHourlyRate = totalRevenueNeeded / totalBillableHours;

    // Account for non-billable time (admin, marketing, etc.)
    const nonBillablePercent = adminTimePercent + marketingTimePercent;
    const effectiveHourlyRate = baseHourlyRate * (1 + nonBillablePercent / 100);

    // Calculate different rate structures
    const dailyRate = effectiveHourlyRate * 8; // 8-hour day
    const weeklyRate = effectiveHourlyRate * 40; // 40-hour week
    const monthlyRate = (totalRevenueNeeded / 12);

    // Calculate actual annual income based on rates
    const actualAnnualIncome = effectiveHourlyRate * totalBillableHours;
    const netIncome = actualAnnualIncome - totalExpenses - taxes;

    setResult({
      hourlyRate: effectiveHourlyRate,
      dailyRate,
      weeklyRate,
      monthlyRate,
      annualIncome: actualAnnualIncome,
      effectiveHourlyRate,
      profitMargin: ((netIncome - desiredAnnualIncome) / actualAnnualIncome) * 100,
      breakdownDetails: {
        billableHours: totalBillableHours,
        totalExpenses,
        taxes,
        netIncome,
        overhead: totalExpenses + taxes
      }
    });
  };

  const reset = () => {
    setDesiredAnnualIncome(75000);
    setBillableHoursPerWeek(30);
    setWeeksPerYear(48);
    setBusinessExpenses(12000);
    setHealthInsurance(6000);
    setRetirement(7500);
    setEquipment(3000);
    setMarketing(2000);
    setOtherExpenses(1500);
    setTaxRate(25);
    setProfitMarginTarget(20);
    setAdminTimePercent(20);
    setMarketingTimePercent(15);
    setResult(null);
  };

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const formatHourlyRate = (rate: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(rate);
  };

  const getRateRecommendation = (hourlyRate: number): { status: string; color: string; message: string } => {
    if (hourlyRate < 25) {
      return {
        status: "Low",
        color: "red",
        message: "Consider increasing your rates or reducing expenses"
      };
    } else if (hourlyRate < 50) {
      return {
        status: "Entry Level",
        color: "yellow",
        message: "Good starting rate for new freelancers"
      };
    } else if (hourlyRate < 100) {
      return {
        status: "Competitive",
        color: "green",
        message: "Solid rate for experienced professionals"
      };
    } else if (hourlyRate < 200) {
      return {
        status: "Premium",
        color: "blue",
        message: "High-value specialist rate"
      };
    } else {
      return {
        status: "Expert",
        color: "purple",
        message: "Top-tier consultant rate"
      };
    }
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Freelancer Rate Calculator</CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="income" className="space-y-6">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="income">Income Goals</TabsTrigger>
              <TabsTrigger value="expenses">Expenses</TabsTrigger>
              <TabsTrigger value="time">Time Allocation</TabsTrigger>
            </TabsList>

            {/* Income Goals */}
            <TabsContent value="income" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="desired-income">Desired Annual Income</Label>
                    <Input
                      id="desired-income"
                      type="number"
                      value={desiredAnnualIncome}
                      onChange={(e) => setDesiredAnnualIncome(parseFloat(e.target.value) || 0)}
                      min="0"
                      step="1000"
                    />
                  </div>

                  <div>
                    <Label htmlFor="billable-hours">Billable Hours per Week</Label>
                    <Input
                      id="billable-hours"
                      type="number"
                      value={billableHoursPerWeek}
                      onChange={(e) => setBillableHoursPerWeek(parseFloat(e.target.value) || 0)}
                      min="1"
                      max="60"
                    />
                  </div>

                  <div>
                    <Label htmlFor="weeks-per-year">Working Weeks per Year</Label>
                    <Input
                      id="weeks-per-year"
                      type="number"
                      value={weeksPerYear}
                      onChange={(e) => setWeeksPerYear(parseInt(e.target.value) || 0)}
                      min="1"
                      max="52"
                    />
                  </div>
                </div>

                <div className="space-y-4">
                  <div>
                    <Label htmlFor="tax-rate">Tax Rate (%)</Label>
                    <Input
                      id="tax-rate"
                      type="number"
                      value={taxRate}
                      onChange={(e) => setTaxRate(parseFloat(e.target.value) || 0)}
                      min="0"
                      max="50"
                      step="1"
                    />
                  </div>

                  <div>
                    <Label htmlFor="profit-margin">Target Profit Margin (%)</Label>
                    <Input
                      id="profit-margin"
                      type="number"
                      value={profitMarginTarget}
                      onChange={(e) => setProfitMarginTarget(parseFloat(e.target.value) || 0)}
                      min="0"
                      max="50"
                      step="1"
                    />
                  </div>

                  <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                    <h3 className="font-semibold mb-2">Income Summary</h3>
                    <div className="space-y-1 text-sm">
                      <div className="flex justify-between">
                        <span>Total billable hours/year:</span>
                        <span>{billableHoursPerWeek * weeksPerYear}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Vacation weeks:</span>
                        <span>{52 - weeksPerYear}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>

            {/* Expenses */}
            <TabsContent value="expenses" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="business-expenses">Business Expenses (Annual)</Label>
                    <Input
                      id="business-expenses"
                      type="number"
                      value={businessExpenses}
                      onChange={(e) => setBusinessExpenses(parseFloat(e.target.value) || 0)}
                      min="0"
                      step="100"
                    />
                  </div>

                  <div>
                    <Label htmlFor="health-insurance">Health Insurance (Annual)</Label>
                    <Input
                      id="health-insurance"
                      type="number"
                      value={healthInsurance}
                      onChange={(e) => setHealthInsurance(parseFloat(e.target.value) || 0)}
                      min="0"
                      step="100"
                    />
                  </div>

                  <div>
                    <Label htmlFor="retirement">Retirement Savings (Annual)</Label>
                    <Input
                      id="retirement"
                      type="number"
                      value={retirement}
                      onChange={(e) => setRetirement(parseFloat(e.target.value) || 0)}
                      min="0"
                      step="100"
                    />
                  </div>
                </div>

                <div className="space-y-4">
                  <div>
                    <Label htmlFor="equipment">Equipment & Software (Annual)</Label>
                    <Input
                      id="equipment"
                      type="number"
                      value={equipment}
                      onChange={(e) => setEquipment(parseFloat(e.target.value) || 0)}
                      min="0"
                      step="100"
                    />
                  </div>

                  <div>
                    <Label htmlFor="marketing">Marketing & Networking (Annual)</Label>
                    <Input
                      id="marketing"
                      type="number"
                      value={marketing}
                      onChange={(e) => setMarketing(parseFloat(e.target.value) || 0)}
                      min="0"
                      step="100"
                    />
                  </div>

                  <div>
                    <Label htmlFor="other-expenses">Other Expenses (Annual)</Label>
                    <Input
                      id="other-expenses"
                      type="number"
                      value={otherExpenses}
                      onChange={(e) => setOtherExpenses(parseFloat(e.target.value) || 0)}
                      min="0"
                      step="100"
                    />
                  </div>
                </div>
              </div>

              <div className="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                <h3 className="font-semibold mb-2">Total Annual Expenses</h3>
                <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                  {formatCurrency(businessExpenses + healthInsurance + retirement + equipment + marketing + otherExpenses)}
                </div>
              </div>
            </TabsContent>

            {/* Time Allocation */}
            <TabsContent value="time" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="admin-time">Administrative Time (%)</Label>
                    <Input
                      id="admin-time"
                      type="number"
                      value={adminTimePercent}
                      onChange={(e) => setAdminTimePercent(parseFloat(e.target.value) || 0)}
                      min="0"
                      max="50"
                      step="1"
                    />
                    <div className="text-sm text-muted-foreground mt-1">
                      Invoicing, contracts, bookkeeping
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="marketing-time">Marketing Time (%)</Label>
                    <Input
                      id="marketing-time"
                      type="number"
                      value={marketingTimePercent}
                      onChange={(e) => setMarketingTimePercent(parseFloat(e.target.value) || 0)}
                      min="0"
                      max="50"
                      step="1"
                    />
                    <div className="text-sm text-muted-foreground mt-1">
                      Networking, proposals, business development
                    </div>
                  </div>
                </div>

                <div className="p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
                  <h3 className="font-semibold mb-2">Time Breakdown</h3>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Billable work:</span>
                      <span>{100 - adminTimePercent - marketingTimePercent}%</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Administrative:</span>
                      <span>{adminTimePercent}%</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Marketing:</span>
                      <span>{marketingTimePercent}%</span>
                    </div>
                    <div className="border-t pt-2 flex justify-between font-semibold">
                      <span>Non-billable overhead:</span>
                      <span>{adminTimePercent + marketingTimePercent}%</span>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>

          {/* Calculate Button */}
          <div className="flex gap-4 mt-6">
            <Button onClick={calculateFreelancerRate} className="flex-1">
              Calculate Optimal Rate
            </Button>
            <Button onClick={reset} variant="outline">
              Reset
            </Button>
          </div>

          {/* Results */}
          {result && (
            <div className="space-y-6">
              {/* Rate Status */}
              <Card className={`bg-${getRateRecommendation(result.hourlyRate).color}-50 dark:bg-${getRateRecommendation(result.hourlyRate).color}-900/20`}>
                <CardContent className="pt-6">
                  <div className="text-center space-y-2">
                    <div className="text-sm text-muted-foreground">Rate Category</div>
                    <div className={`text-2xl font-bold text-${getRateRecommendation(result.hourlyRate).color}-600 dark:text-${getRateRecommendation(result.hourlyRate).color}-400`}>
                      {getRateRecommendation(result.hourlyRate).status}
                    </div>
                    <div className="text-sm">{getRateRecommendation(result.hourlyRate).message}</div>
                  </div>
                </CardContent>
              </Card>

              {/* Rate Structure */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <Card className="bg-blue-50 dark:bg-blue-900/20">
                  <CardContent className="pt-6">
                    <div className="text-center space-y-2">
                      <div className="text-sm text-muted-foreground">Hourly Rate</div>
                      <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                        {formatHourlyRate(result.hourlyRate)}
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-green-50 dark:bg-green-900/20">
                  <CardContent className="pt-6">
                    <div className="text-center space-y-2">
                      <div className="text-sm text-muted-foreground">Daily Rate</div>
                      <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                        {formatCurrency(result.dailyRate)}
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-purple-50 dark:bg-purple-900/20">
                  <CardContent className="pt-6">
                    <div className="text-center space-y-2">
                      <div className="text-sm text-muted-foreground">Weekly Rate</div>
                      <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                        {formatCurrency(result.weeklyRate)}
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-orange-50 dark:bg-orange-900/20">
                  <CardContent className="pt-6">
                    <div className="text-center space-y-2">
                      <div className="text-sm text-muted-foreground">Monthly Rate</div>
                      <div className="text-2xl font-bold text-orange-600 dark:text-orange-400">
                        {formatCurrency(result.monthlyRate)}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Financial Breakdown */}
              <Card>
                <CardHeader>
                  <CardTitle>Financial Breakdown</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span>Annual Revenue:</span>
                        <span className="font-semibold">{formatCurrency(result.annualIncome)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Total Expenses:</span>
                        <span className="font-semibold text-red-600">-{formatCurrency(result.breakdownDetails.totalExpenses)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Taxes:</span>
                        <span className="font-semibold text-red-600">-{formatCurrency(result.breakdownDetails.taxes)}</span>
                      </div>
                      <div className="flex justify-between border-t pt-2">
                        <span className="font-semibold">Net Income:</span>
                        <span className="font-bold text-green-600">{formatCurrency(result.breakdownDetails.netIncome)}</span>
                      </div>
                    </div>

                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span>Billable Hours/Year:</span>
                        <span className="font-semibold">{result.breakdownDetails.billableHours}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Effective Hourly Rate:</span>
                        <span className="font-semibold">{formatHourlyRate(result.effectiveHourlyRate)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Profit Margin:</span>
                        <span className="font-semibold">{result.profitMargin.toFixed(1)}%</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Overhead Rate:</span>
                        <span className="font-semibold">{((result.breakdownDetails.overhead / result.annualIncome) * 100).toFixed(1)}%</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* Tips */}
          <div className="p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
            <h3 className="font-semibold mb-2">Freelancer Rate Tips</h3>
            <ul className="text-sm space-y-1">
              <li>• Include all business expenses, not just direct costs</li>
              <li>• Account for non-billable time (admin, marketing, learning)</li>
              <li>• Consider seasonal fluctuations in work availability</li>
              <li>• Build in a profit margin for business growth and emergencies</li>
              <li>• Research market rates for your skill level and location</li>
              <li>• Review and adjust rates annually or with skill improvements</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
