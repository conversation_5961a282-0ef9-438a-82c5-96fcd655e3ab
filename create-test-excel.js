/**
 * Create a test Excel file for authentication workflow testing
 */

const XLSX = require('xlsx');
const fs = require('fs');
const path = require('path');

function createTestExcelFile() {
  console.log('Creating test Excel file...');
  
  // Create sample data
  const testData = [
    ['Name', 'Age', 'Department', 'Salary'],
    ['<PERSON>', 30, 'Engineering', 75000],
    ['<PERSON>', 28, 'Marketing', 65000],
    ['<PERSON>', 35, 'Sales', 70000],
    ['<PERSON>', 32, 'HR', 60000],
    ['<PERSON>', 29, 'Engineering', 80000]
  ];
  
  // Create workbook and worksheet
  const workbook = XLSX.utils.book_new();
  const worksheet = XLSX.utils.aoa_to_sheet(testData);
  
  // Add some formatting
  worksheet['!cols'] = [
    { wch: 15 }, // Name column width
    { wch: 8 },  // Age column width
    { wch: 15 }, // Department column width
    { wch: 12 }  // Salary column width
  ];
  
  // Add worksheet to workbook
  XLSX.utils.book_append_sheet(workbook, worksheet, 'Employee Data');
  
  // Create additional sheet for testing
  const summaryData = [
    ['Summary Statistics'],
    ['Total Employees', 5],
    ['Average Age', 30.8],
    ['Average Salary', 70000],
    ['Departments', 4]
  ];
  
  const summarySheet = XLSX.utils.aoa_to_sheet(summaryData);
  XLSX.utils.book_append_sheet(workbook, summarySheet, 'Summary');
  
  // Save the file
  const filePath = path.join(__dirname, 'test-excel-sample.xlsx');
  XLSX.writeFile(workbook, filePath);
  
  console.log(`✅ Test Excel file created: ${filePath}`);
  
  // Get file stats
  const stats = fs.statSync(filePath);
  console.log(`   File size: ${stats.size} bytes`);
  console.log(`   Sheets: Employee Data, Summary`);
  console.log(`   Rows: ${testData.length} (Employee Data)`);
  
  return filePath;
}

function createTestWordFile() {
  console.log('Creating test Word document...');
  
  // Create a simple text file that can be used for Word testing
  const wordContent = `
TEST DOCUMENT FOR AUTHENTICATION WORKFLOW

This is a test document created for verifying the authentication-protected download workflow.

Document Details:
- Created: ${new Date().toISOString()}
- Purpose: Testing Word to PDF conversion
- Content: Sample text with formatting

Sample Content:
1. First item in the list
2. Second item in the list
3. Third item in the list

Table Example:
Name        | Department  | Status
------------|-------------|--------
John Doe    | Engineering | Active
Jane Smith  | Marketing   | Active
Bob Johnson | Sales       | Active

This document should convert to PDF successfully and trigger the authentication workflow when a user attempts to download the converted file.

End of test document.
`;
  
  const filePath = path.join(__dirname, 'test-word-sample.txt');
  fs.writeFileSync(filePath, wordContent);
  
  console.log(`✅ Test Word file created: ${filePath}`);
  
  const stats = fs.statSync(filePath);
  console.log(`   File size: ${stats.size} bytes`);
  
  return filePath;
}

function createTestImage() {
  console.log('Creating test image file...');
  
  // Create a simple SVG image that can be converted to PDF
  const svgContent = `<?xml version="1.0" encoding="UTF-8"?>
<svg width="400" height="300" xmlns="http://www.w3.org/2000/svg">
  <rect width="100%" height="100%" fill="#f0f0f0"/>
  <text x="200" y="50" font-family="Arial" font-size="24" text-anchor="middle" fill="#333">
    Test Image for PDF Conversion
  </text>
  <text x="200" y="100" font-family="Arial" font-size="16" text-anchor="middle" fill="#666">
    Authentication Workflow Testing
  </text>
  <rect x="50" y="150" width="300" height="100" fill="#e0e0e0" stroke="#999" stroke-width="2"/>
  <text x="200" y="190" font-family="Arial" font-size="14" text-anchor="middle" fill="#333">
    This image should convert to PDF
  </text>
  <text x="200" y="210" font-family="Arial" font-size="14" text-anchor="middle" fill="#333">
    and trigger authentication for download
  </text>
  <text x="200" y="280" font-family="Arial" font-size="12" text-anchor="middle" fill="#999">
    Created: ${new Date().toISOString()}
  </text>
</svg>`;
  
  const filePath = path.join(__dirname, 'test-image-sample.svg');
  fs.writeFileSync(filePath, svgContent);
  
  console.log(`✅ Test SVG image created: ${filePath}`);
  
  const stats = fs.statSync(filePath);
  console.log(`   File size: ${stats.size} bytes`);
  
  return filePath;
}

function createAllTestFiles() {
  console.log('🧪 Creating all test files for authentication workflow testing...\n');
  
  const files = {
    excel: createTestExcelFile(),
    word: createTestWordFile(),
    image: createTestImage()
  };
  
  console.log('\n✅ All test files created successfully!');
  console.log('\nTest files available:');
  console.log(`   Excel: ${files.excel}`);
  console.log(`   Word:  ${files.word}`);
  console.log(`   Image: ${files.image}`);
  
  console.log('\n🎯 Next steps:');
  console.log('   1. Start your development server: pnpm run dev');
  console.log('   2. Open http://localhost:3002/tools/excel-to-pdf');
  console.log('   3. Upload the test Excel file');
  console.log('   4. Follow the authentication workflow test steps');
  
  return files;
}

// Run if executed directly
if (require.main === module) {
  createAllTestFiles();
}

module.exports = {
  createTestExcelFile,
  createTestWordFile,
  createTestImage,
  createAllTestFiles
};
