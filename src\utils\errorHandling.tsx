/**
 * Error Handling Utilities
 * Comprehensive error handling for calculators and tools
 */

import React from 'react';

export enum ErrorType {
  VALIDATION = 'validation',
  CALCULATION = 'calculation',
  FILE_PROCESSING = 'file_processing',
  NETWORK = 'network',
  MEMORY = 'memory',
  TIMEOUT = 'timeout',
  UNKNOWN = 'unknown'
}

export enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

export interface AppError {
  type: ErrorType;
  severity: ErrorSeverity;
  message: string;
  userMessage: string;
  code?: string;
  details?: any;
  timestamp: Date;
  component?: string;
  action?: string;
  recoverable: boolean;
  retryable: boolean;
}

export class ToolRapterError extends Error {
  public readonly type: ErrorType;
  public readonly severity: ErrorSeverity;
  public readonly userMessage: string;
  public readonly code?: string;
  public readonly details?: any;
  public readonly timestamp: Date;
  public readonly component?: string;
  public readonly action?: string;
  public readonly recoverable: boolean;
  public readonly retryable: boolean;

  constructor(error: Omit<AppError, 'timestamp'>) {
    super(error.message);
    this.name = 'ToolRapterError';
    this.type = error.type;
    this.severity = error.severity;
    this.userMessage = error.userMessage;
    this.code = error.code;
    this.details = error.details;
    this.timestamp = new Date();
    this.component = error.component;
    this.action = error.action;
    this.recoverable = error.recoverable;
    this.retryable = error.retryable;
  }
}

// Error factory functions
export const createValidationError = (
  message: string,
  userMessage?: string,
  details?: any
): ToolRapterError => {
  return new ToolRapterError({
    type: ErrorType.VALIDATION,
    severity: ErrorSeverity.LOW,
    message,
    userMessage: userMessage || message,
    details,
    recoverable: true,
    retryable: false
  });
};

export const createCalculationError = (
  message: string,
  userMessage?: string,
  details?: any
): ToolRapterError => {
  return new ToolRapterError({
    type: ErrorType.CALCULATION,
    severity: ErrorSeverity.MEDIUM,
    message,
    userMessage: userMessage || 'Calculation failed. Please check your inputs and try again.',
    details,
    recoverable: true,
    retryable: true
  });
};

export const createFileProcessingError = (
  message: string,
  userMessage?: string,
  details?: any
): ToolRapterError => {
  return new ToolRapterError({
    type: ErrorType.FILE_PROCESSING,
    severity: ErrorSeverity.MEDIUM,
    message,
    userMessage: userMessage || 'File processing failed. Please check your file and try again.',
    details,
    recoverable: true,
    retryable: true
  });
};

export const createNetworkError = (
  message: string,
  userMessage?: string,
  details?: any
): ToolRapterError => {
  return new ToolRapterError({
    type: ErrorType.NETWORK,
    severity: ErrorSeverity.HIGH,
    message,
    userMessage: userMessage || 'Network error. Please check your connection and try again.',
    details,
    recoverable: true,
    retryable: true
  });
};

export const createMemoryError = (
  message: string,
  userMessage?: string,
  details?: any
): ToolRapterError => {
  return new ToolRapterError({
    type: ErrorType.MEMORY,
    severity: ErrorSeverity.HIGH,
    message,
    userMessage: userMessage || 'Memory error. Please try with a smaller file or refresh the page.',
    details,
    recoverable: true,
    retryable: false
  });
};

export const createTimeoutError = (
  message: string,
  userMessage?: string,
  details?: any
): ToolRapterError => {
  return new ToolRapterError({
    type: ErrorType.TIMEOUT,
    severity: ErrorSeverity.MEDIUM,
    message,
    userMessage: userMessage || 'Operation timed out. Please try again.',
    details,
    recoverable: true,
    retryable: true
  });
};

// Error handling utilities
export function handleError(error: unknown, component?: string, action?: string): ToolRapterError {
  // If it's already a ToolRapterError, return it
  if (error instanceof ToolRapterError) {
    return error;
  }

  // If it's a standard Error
  if (error instanceof Error) {
    // Check for specific error patterns
    if (error.message.includes('fetch')) {
      return createNetworkError(error.message, undefined, { originalError: error, component, action });
    }
    
    if (error.message.includes('memory') || error.message.includes('heap')) {
      return createMemoryError(error.message, undefined, { originalError: error, component, action });
    }
    
    if (error.message.includes('timeout')) {
      return createTimeoutError(error.message, undefined, { originalError: error, component, action });
    }
    
    if (error.message.includes('validation') || error.message.includes('invalid')) {
      return createValidationError(error.message, undefined, { originalError: error, component, action });
    }

    // Generic error
    return new ToolRapterError({
      type: ErrorType.UNKNOWN,
      severity: ErrorSeverity.MEDIUM,
      message: error.message,
      userMessage: 'An unexpected error occurred. Please try again.',
      details: { originalError: error, component, action },
      recoverable: true,
      retryable: true
    });
  }

  // Handle non-Error objects
  const message = typeof error === 'string' ? error : 'Unknown error occurred';
  return new ToolRapterError({
    type: ErrorType.UNKNOWN,
    severity: ErrorSeverity.MEDIUM,
    message,
    userMessage: 'An unexpected error occurred. Please try again.',
    details: { originalError: error, component, action },
    recoverable: true,
    retryable: true
  });
}

// Error logging
export function logError(error: ToolRapterError): void {
  const logData = {
    timestamp: error.timestamp.toISOString(),
    type: error.type,
    severity: error.severity,
    message: error.message,
    userMessage: error.userMessage,
    code: error.code,
    component: error.component,
    action: error.action,
    details: error.details,
    stack: error.stack
  };

  // Log to console in development
  if (process.env.NODE_ENV === 'development') {
    console.error('[ToolRapter Error]', logData);
  }

  // In production, you would send to error tracking service
  // Example: Sentry, LogRocket, etc.
  if (process.env.NODE_ENV === 'production') {
    // Send to error tracking service
    // errorTrackingService.captureError(logData);
  }
}

// Retry mechanism
export async function withRetry<T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000,
  backoff: number = 2
): Promise<T> {
  let lastError: ToolRapterError;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = handleError(error);
      
      // Don't retry if error is not retryable
      if (!lastError.retryable) {
        throw lastError;
      }

      // Don't retry on last attempt
      if (attempt === maxRetries) {
        throw lastError;
      }

      // Wait before retry with exponential backoff
      await new Promise(resolve => setTimeout(resolve, delay * Math.pow(backoff, attempt - 1)));
    }
  }

  throw lastError!;
}

// Safe execution wrapper
export async function safeExecute<T>(
  operation: () => Promise<T>,
  fallback?: T,
  component?: string,
  action?: string
): Promise<{ success: boolean; data?: T; error?: ToolRapterError }> {
  try {
    const data = await operation();
    return { success: true, data };
  } catch (error) {
    const toolRapterError = handleError(error, component, action);
    logError(toolRapterError);
    
    return {
      success: false,
      error: toolRapterError,
      data: fallback
    };
  }
}

// Error boundary helper
export function createErrorBoundaryFallback(componentName: string) {
  return function ErrorFallback({ error, resetErrorBoundary }: {
    error: Error;
    resetErrorBoundary: () => void;
  }) {
    const toolRapterError = handleError(error, componentName);
    logError(toolRapterError);

    return (
      <div className="p-6 text-center border border-red-200 rounded-lg bg-red-50 dark:bg-red-900/20">
        <div className="mb-4">
          <h3 className="text-lg font-semibold text-red-800 dark:text-red-200">
            Something went wrong
          </h3>
          <p className="text-red-600 dark:text-red-300 mt-2">
            {toolRapterError.userMessage}
          </p>
        </div>
        
        {toolRapterError.recoverable && (
          <div className="space-x-3">
            <button
              onClick={resetErrorBoundary}
              className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
            >
              Try Again
            </button>
            <button
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 transition-colors"
            >
              Refresh Page
            </button>
          </div>
        )}
        
        {process.env.NODE_ENV === 'development' && (
          <details className="mt-4 text-left">
            <summary className="cursor-pointer text-sm text-red-700 dark:text-red-300">
              Error Details (Development)
            </summary>
            <pre className="mt-2 p-2 bg-red-100 dark:bg-red-900/40 rounded text-xs overflow-auto">
              {error.stack}
            </pre>
          </details>
        )}
      </div>
    );
  };
}
