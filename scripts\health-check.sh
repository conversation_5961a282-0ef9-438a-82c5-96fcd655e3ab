#!/bin/bash

# =============================================================================
# TOOLRAPTER - COMPREHENSIVE HEALTH CHECK SCRIPT
# =============================================================================
# Enterprise-grade health monitoring for production deployment
# Usage: ./scripts/health-check.sh [local|production] [--detailed]

set -e

# =============================================================================
# CONFIGURATION
# =============================================================================
ENVIRONMENT=${1:-local}
DETAILED=${2:-false}
DOMAIN="toolrapter.com"
VPS_HOST="************"
VPS_USER="root"

# Performance thresholds
MAX_RESPONSE_TIME=5
MAX_API_RESPONSE_TIME=3
MIN_UPTIME_PERCENTAGE=99.9

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

# =============================================================================
# HELPER FUNCTIONS
# =============================================================================
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[✓]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[⚠]${NC} $1"; }
log_error() { echo -e "${RED}[✗]${NC} $1"; }
log_check() { echo -e "${PURPLE}[CHECK]${NC} $1"; }

# Timer functions
start_timer() {
    TIMER_START=$(date +%s.%N)
}

end_timer() {
    TIMER_END=$(date +%s.%N)
    ELAPSED=$(echo "$TIMER_END - $TIMER_START" | bc -l)
    printf "%.2f" $ELAPSED
}

# HTTP status check
check_http_status() {
    local url=$1
    local expected_status=${2:-200}
    
    start_timer
    local response=$(curl -s -o /dev/null -w "%{http_code}" --max-time 10 "$url" 2>/dev/null || echo "000")
    local response_time=$(end_timer)
    
    if [ "$response" = "$expected_status" ]; then
        log_success "$url (${response_time}s)"
        return 0
    else
        log_error "$url - Status: $response (${response_time}s)"
        return 1
    fi
}

# =============================================================================
# LOCAL HEALTH CHECKS
# =============================================================================
check_local_environment() {
    log_check "Checking local environment..."
    
    # Check Node.js
    if command -v node >/dev/null 2>&1; then
        NODE_VERSION=$(node --version)
        log_success "Node.js $NODE_VERSION"
    else
        log_error "Node.js not installed"
        return 1
    fi
    
    # Check npm dependencies
    if [ -f "package.json" ] && [ -d "node_modules" ]; then
        log_success "Dependencies installed"
    else
        log_error "Dependencies not installed. Run: npm install"
        return 1
    fi
    
    # Check environment file
    if [ -f ".env.local" ]; then
        log_success "Environment file exists"
    else
        log_warning "Environment file missing. Run: cp .env.example .env.local"
    fi
    
    # Check TypeScript compilation
    log_info "Checking TypeScript compilation..."
    if npm run type-check >/dev/null 2>&1; then
        log_success "TypeScript compilation passed"
    else
        log_error "TypeScript compilation failed"
        return 1
    fi
    
    # Check build
    if [ -d ".next" ]; then
        log_success "Build directory exists"
    else
        log_warning "Build not found. Run: npm run build"
    fi
}

check_local_server() {
    log_check "Checking local server..."
    
    # Check if server is running
    if curl -f http://localhost:3000/api/health >/dev/null 2>&1; then
        log_success "Local server responding"
        
        # Check main page
        check_http_status "http://localhost:3000"
        
        # Check API endpoints
        check_http_status "http://localhost:3000/api/health"
        
    else
        log_error "Local server not responding. Start with: npm run dev"
        return 1
    fi
}

# =============================================================================
# PRODUCTION HEALTH CHECKS
# =============================================================================
check_production_infrastructure() {
    log_check "Checking production infrastructure..."
    
    # Check VPS connectivity
    if ping -c 1 $VPS_HOST >/dev/null 2>&1; then
        log_success "VPS connectivity"
    else
        log_error "VPS not reachable"
        return 1
    fi
    
    # Check SSH access
    if ssh -o ConnectTimeout=5 -o StrictHostKeyChecking=no $VPS_USER@$VPS_HOST "echo 'SSH OK'" >/dev/null 2>&1; then
        log_success "SSH access"
    else
        log_error "SSH access failed"
        return 1
    fi
    
    # Check domain resolution
    if nslookup $DOMAIN >/dev/null 2>&1; then
        log_success "Domain resolution"
    else
        log_error "Domain resolution failed"
        return 1
    fi
    
    # Check SSL certificate
    if echo | openssl s_client -servername $DOMAIN -connect $DOMAIN:443 2>/dev/null | openssl x509 -noout -dates >/dev/null 2>&1; then
        log_success "SSL certificate valid"
    else
        log_error "SSL certificate invalid"
        return 1
    fi
}

check_production_services() {
    log_check "Checking production services..."
    
    # Check services via SSH
    ssh $VPS_USER@$VPS_HOST << 'EOF'
        # Check Nginx
        if systemctl is-active --quiet nginx; then
            echo "✓ Nginx service running"
        else
            echo "✗ Nginx service not running"
            exit 1
        fi
        
        # Check PM2
        if pm2 list | grep -q "toolrapter"; then
            echo "✓ PM2 application running"
        else
            echo "✗ PM2 application not running"
            exit 1
        fi
        
        # Check disk space
        DISK_USAGE=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
        if [ $DISK_USAGE -lt 80 ]; then
            echo "✓ Disk usage: ${DISK_USAGE}%"
        else
            echo "⚠ High disk usage: ${DISK_USAGE}%"
        fi
        
        # Check memory usage
        MEMORY_USAGE=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
        if [ $MEMORY_USAGE -lt 80 ]; then
            echo "✓ Memory usage: ${MEMORY_USAGE}%"
        else
            echo "⚠ High memory usage: ${MEMORY_USAGE}%"
        fi
        
        # Check load average
        LOAD_AVG=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')
        echo "✓ Load average: $LOAD_AVG"
EOF
}

check_production_application() {
    log_check "Checking production application..."
    
    # Check main website
    if check_http_status "https://$DOMAIN"; then
        log_success "Main website accessible"
    else
        log_error "Main website not accessible"
        return 1
    fi
    
    # Check API health endpoint
    if check_http_status "https://$DOMAIN/api/health"; then
        log_success "API health endpoint"
    else
        log_error "API health endpoint failed"
        return 1
    fi
    
    # Check critical pages
    local pages=("/" "/tools" "/calculators" "/blog")
    for page in "${pages[@]}"; do
        if check_http_status "https://$DOMAIN$page"; then
            log_success "Page: $page"
        else
            log_warning "Page not accessible: $page"
        fi
    done
    
    # Check API endpoints
    local api_endpoints=("/api/health" "/api/blog" "/api/tools")
    for endpoint in "${api_endpoints[@]}"; do
        if check_http_status "https://$DOMAIN$endpoint"; then
            log_success "API: $endpoint"
        else
            log_warning "API not accessible: $endpoint"
        fi
    done
}

# =============================================================================
# PERFORMANCE CHECKS
# =============================================================================
check_performance() {
    log_check "Checking performance metrics..."
    
    local base_url
    if [ "$ENVIRONMENT" = "local" ]; then
        base_url="http://localhost:3000"
    else
        base_url="https://$DOMAIN"
    fi
    
    # Page load time test
    log_info "Testing page load times..."
    start_timer
    if curl -f --max-time 30 "$base_url" >/dev/null 2>&1; then
        local page_load_time=$(end_timer)
        if (( $(echo "$page_load_time < $MAX_RESPONSE_TIME" | bc -l) )); then
            log_success "Page load time: ${page_load_time}s (target: <${MAX_RESPONSE_TIME}s)"
        else
            log_warning "Page load time: ${page_load_time}s exceeds target (${MAX_RESPONSE_TIME}s)"
        fi
    else
        log_error "Page load test failed"
    fi
    
    # API response time test
    log_info "Testing API response times..."
    start_timer
    if curl -f --max-time 10 "$base_url/api/health" >/dev/null 2>&1; then
        local api_response_time=$(end_timer)
        if (( $(echo "$api_response_time < $MAX_API_RESPONSE_TIME" | bc -l) )); then
            log_success "API response time: ${api_response_time}s (target: <${MAX_API_RESPONSE_TIME}s)"
        else
            log_warning "API response time: ${api_response_time}s exceeds target (${MAX_API_RESPONSE_TIME}s)"
        fi
    else
        log_error "API response test failed"
    fi
}

# =============================================================================
# DETAILED CHECKS
# =============================================================================
detailed_checks() {
    if [ "$DETAILED" = "--detailed" ]; then
        log_check "Running detailed checks..."
        
        # Security headers check
        log_info "Checking security headers..."
        if [ "$ENVIRONMENT" = "production" ]; then
            local headers=$(curl -I "https://$DOMAIN" 2>/dev/null | grep -E "(Strict-Transport-Security|X-Frame-Options|X-Content-Type-Options)")
            if [ -n "$headers" ]; then
                log_success "Security headers present"
            else
                log_warning "Security headers missing"
            fi
        fi
        
        # Database connectivity (if applicable)
        if [ -f ".env.local" ] && grep -q "MONGODB_URI" .env.local; then
            log_info "Testing database connectivity..."
            # This would require additional setup for MongoDB client
            log_info "Database connectivity check requires MongoDB client"
        fi
        
        # Rate limiting test
        log_info "Testing rate limiting..."
        local rate_limit_test=0
        for i in {1..5}; do
            if curl -f --max-time 5 "$base_url/api/health" >/dev/null 2>&1; then
                ((rate_limit_test++))
            fi
        done
        
        if [ $rate_limit_test -eq 5 ]; then
            log_success "Rate limiting allows normal traffic"
        else
            log_warning "Rate limiting may be too restrictive"
        fi
    fi
}

# =============================================================================
# MAIN HEALTH CHECK PROCESS
# =============================================================================
main() {
    echo "🏥 ToolRapter Health Check"
    echo "========================="
    echo "Environment: $ENVIRONMENT"
    echo "Detailed: $DETAILED"
    echo ""
    
    local overall_status=0
    
    case $ENVIRONMENT in
        local)
            check_local_environment || overall_status=1
            check_local_server || overall_status=1
            ;;
        production)
            check_production_infrastructure || overall_status=1
            check_production_services || overall_status=1
            check_production_application || overall_status=1
            ;;
        *)
            log_error "Invalid environment. Use 'local' or 'production'"
            exit 1
            ;;
    esac
    
    # Performance checks for both environments
    check_performance || overall_status=1
    
    # Detailed checks if requested
    detailed_checks
    
    echo ""
    if [ $overall_status -eq 0 ]; then
        log_success "🎉 All health checks passed!"
    else
        log_error "❌ Some health checks failed!"
        exit 1
    fi
}

# =============================================================================
# SCRIPT EXECUTION
# =============================================================================
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    # Check for required tools
    for tool in curl bc; do
        if ! command -v $tool >/dev/null 2>&1; then
            log_error "$tool is required but not installed"
            exit 1
        fi
    done
    
    main "$@"
fi
