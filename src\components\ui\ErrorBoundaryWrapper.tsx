"use client";

import React from 'react';
import { ErrorBoundary } from 'react-error-boundary';
import { Button } from './button';
import { Alert, AlertDescription, AlertTitle } from './alert';
import { handleError, logError, ToolRapterError } from '@/utils/errorHandling';

interface ErrorBoundaryWrapperProps {
  children: React.ReactNode;
  componentName?: string;
  fallback?: React.ComponentType<{ error: Error; resetErrorBoundary: () => void }>;
  onError?: (error: ToolRapterError) => void;
  showDetails?: boolean;
}

function DefaultErrorFallback({ 
  error, 
  resetErrorBoundary,
  componentName = 'Component',
  showDetails = false 
}: {
  error: Error;
  resetErrorBoundary: () => void;
  componentName?: string;
  showDetails?: boolean;
}) {
  const toolRapterError = handleError(error, componentName);
  
  React.useEffect(() => {
    logError(toolRapterError);
  }, [toolRapterError]);

  return (
    <div className="p-6 space-y-4">
      <Alert variant="destructive">
        <AlertTitle className="flex items-center gap-2">
          <svg
            className="h-4 w-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
            />
          </svg>
          Something went wrong
        </AlertTitle>
        <AlertDescription className="mt-2">
          {toolRapterError.userMessage}
        </AlertDescription>
      </Alert>

      {toolRapterError.recoverable && (
        <div className="flex flex-wrap gap-3">
          <Button
            onClick={resetErrorBoundary}
            variant="default"
            size="sm"
          >
            Try Again
          </Button>
          
          <Button
            onClick={() => window.location.reload()}
            variant="outline"
            size="sm"
          >
            Refresh Page
          </Button>
          
          <Button
            onClick={() => window.history.back()}
            variant="ghost"
            size="sm"
          >
            Go Back
          </Button>
        </div>
      )}

      {showDetails && process.env.NODE_ENV === 'development' && (
        <details className="mt-4">
          <summary className="cursor-pointer text-sm font-medium text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100">
            Error Details (Development Only)
          </summary>
          <div className="mt-2 p-3 bg-gray-100 dark:bg-gray-800 rounded-md">
            <div className="space-y-2 text-xs font-mono">
              <div>
                <strong>Type:</strong> {toolRapterError.type}
              </div>
              <div>
                <strong>Severity:</strong> {toolRapterError.severity}
              </div>
              <div>
                <strong>Component:</strong> {toolRapterError.component || 'Unknown'}
              </div>
              <div>
                <strong>Timestamp:</strong> {toolRapterError.timestamp.toISOString()}
              </div>
              {toolRapterError.code && (
                <div>
                  <strong>Code:</strong> {toolRapterError.code}
                </div>
              )}
              <div>
                <strong>Message:</strong> {toolRapterError.message}
              </div>
              {error.stack && (
                <div>
                  <strong>Stack Trace:</strong>
                  <pre className="mt-1 whitespace-pre-wrap text-xs overflow-auto max-h-40">
                    {error.stack}
                  </pre>
                </div>
              )}
            </div>
          </div>
        </details>
      )}
    </div>
  );
}

export function ErrorBoundaryWrapper({
  children,
  componentName = 'Component',
  fallback: CustomFallback,
  onError,
  showDetails = process.env.NODE_ENV === 'development'
}: ErrorBoundaryWrapperProps) {
  const handleErrorBoundary = React.useCallback((error: Error, errorInfo: { componentStack?: string | null }) => {
    const toolRapterError = handleError(error, componentName);

    // Create a new error object with component stack
    const enhancedError = new ToolRapterError({
      type: toolRapterError.type,
      severity: toolRapterError.severity,
      message: toolRapterError.message,
      userMessage: toolRapterError.userMessage,
      code: toolRapterError.code,
      details: {
        ...toolRapterError.details,
        componentStack: errorInfo.componentStack || 'Unknown component stack'
      },
      component: toolRapterError.component,
      action: toolRapterError.action,
      recoverable: toolRapterError.recoverable,
      retryable: toolRapterError.retryable
    });

    logError(enhancedError);
    onError?.(enhancedError);
  }, [componentName, onError]);

  const FallbackComponent = CustomFallback || ((props: any) => (
    <DefaultErrorFallback 
      {...props} 
      componentName={componentName}
      showDetails={showDetails}
    />
  ));

  return (
    <ErrorBoundary
      FallbackComponent={FallbackComponent}
      onError={handleErrorBoundary}
      onReset={() => {
        // Clear any error state if needed
        if (typeof window !== 'undefined') {
          // Clear localStorage errors if any
          const errorKeys = Object.keys(localStorage).filter(key => key.startsWith('error_'));
          errorKeys.forEach(key => localStorage.removeItem(key));
        }
      }}
    >
      {children}
    </ErrorBoundary>
  );
}

// Specialized error boundaries for different components
export function CalculatorErrorBoundary({ children, calculatorId }: {
  children: React.ReactNode;
  calculatorId: string;
}) {
  return (
    <ErrorBoundaryWrapper
      componentName={`Calculator-${calculatorId}`}
      fallback={({ error, resetErrorBoundary }) => (
        <div className="p-6 text-center border border-red-200 rounded-lg bg-red-50 dark:bg-red-900/20">
          <div className="mb-4">
            <h3 className="text-lg font-semibold text-red-800 dark:text-red-200">
              Calculator Error
            </h3>
            <p className="text-red-600 dark:text-red-300 mt-2">
              The calculator encountered an error. This might be due to invalid input values or a calculation overflow.
            </p>
          </div>
          
          <div className="space-y-3">
            <Button
              onClick={resetErrorBoundary}
              className="bg-red-600 hover:bg-red-700 text-white"
            >
              Reset Calculator
            </Button>
            <p className="text-sm text-red-500">
              Try adjusting your input values and calculate again.
            </p>
          </div>
        </div>
      )}
    >
      {children}
    </ErrorBoundaryWrapper>
  );
}

export function ToolErrorBoundary({ children, toolId }: {
  children: React.ReactNode;
  toolId: string;
}) {
  return (
    <ErrorBoundaryWrapper
      componentName={`Tool-${toolId}`}
      fallback={({ error, resetErrorBoundary }) => (
        <div className="p-6 text-center border border-red-200 rounded-lg bg-red-50 dark:bg-red-900/20">
          <div className="mb-4">
            <h3 className="text-lg font-semibold text-red-800 dark:text-red-200">
              Tool Error
            </h3>
            <p className="text-red-600 dark:text-red-300 mt-2">
              The tool encountered an error while processing your file. This might be due to file corruption, unsupported format, or file size limitations.
            </p>
          </div>
          
          <div className="space-y-3">
            <Button
              onClick={resetErrorBoundary}
              className="bg-red-600 hover:bg-red-700 text-white"
            >
              Try Again
            </Button>
            <p className="text-sm text-red-500">
              Try with a different file or check that your file meets the requirements.
            </p>
          </div>
        </div>
      )}
    >
      {children}
    </ErrorBoundaryWrapper>
  );
}

// HOC for automatic error boundary wrapping
export function withErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  componentName?: string
) {
  const WrappedComponent = React.forwardRef<any, P>((props, ref) => (
    <ErrorBoundaryWrapper componentName={componentName || Component.displayName || Component.name}>
      <Component {...(props as any)} ref={ref} />
    </ErrorBoundaryWrapper>
  ));

  WrappedComponent.displayName = `withErrorBoundary(${componentName || Component.displayName || Component.name})`;

  return WrappedComponent;
}
