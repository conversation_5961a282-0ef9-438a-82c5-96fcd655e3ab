import { NextRequest, NextResponse } from 'next/server';
import { PDFDocument } from 'pdf-lib';
import { Document, Paragraph, TextRun, Packer } from 'docx';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }

    if (file.type !== 'application/pdf') {
      return NextResponse.json(
        { error: 'File must be a PDF' },
        { status: 400 }
      );
    }

    // Convert file to buffer
    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);

    // Use require for pdf-parse to avoid ESM issues
    const pdfParse = require('pdf-parse');

    // Extract text from PDF
    const pdfData = await pdfParse(buffer);
    const extractedText = pdfData.text;

    if (!extractedText || extractedText.trim().length === 0) {
      return NextResponse.json(
        { error: 'No text content found in PDF. The PDF might contain only images or be password protected.' },
        { status: 400 }
      );
    }

    // Split text into paragraphs
    const paragraphs = extractedText.split('\n\n').filter((p: string) => p.trim().length > 0);
    
    // Create Word document
    const doc = new Document({
      sections: [
        {
          properties: {},
          children: paragraphs.map((paragraph: string) => {
            // Clean up the paragraph text
            const cleanText = paragraph.replace(/\n/g, ' ').trim();
            
            return new Paragraph({
              children: [
                new TextRun({
                  text: cleanText,
                  size: 24, // 12pt font
                }),
              ],
              spacing: {
                after: 200, // Add space after paragraph
              },
            });
          }),
        },
      ],
    });

    // Generate Word document buffer
    const docxBuffer = await Packer.toBuffer(doc);

    // Generate filename
    const originalName = file.name.replace('.pdf', '');
    const docxFilename = `${originalName}_converted.docx`;

    // Create response with Word document
    const response = new NextResponse(docxBuffer, {
      status: 200,
      headers: {
        'Content-Type': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'Content-Disposition': `attachment; filename="${docxFilename}"`,
        'X-Original-Pages': pdfData.numpages.toString(),
        'X-Extracted-Characters': extractedText.length.toString(),
        'X-Paragraphs-Created': paragraphs.length.toString(),
      },
    });

    return response;

  } catch (error) {
    console.error('PDF to Word conversion error:', error);
    
    // Provide more specific error messages
    if (error instanceof Error) {
      if (error.message.includes('Invalid PDF')) {
        return NextResponse.json(
          { error: 'Invalid PDF file. Please ensure the file is not corrupted.' },
          { status: 400 }
        );
      }
      if (error.message.includes('password')) {
        return NextResponse.json(
          { error: 'Password-protected PDFs are not supported.' },
          { status: 400 }
        );
      }
    }

    return NextResponse.json(
      { error: 'Failed to convert PDF to Word. Please ensure the file is a valid PDF document with text content.' },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json(
    { 
      message: 'PDF to Word Conversion API',
      supportedInput: 'PDF',
      outputFormat: 'DOCX',
      maxFileSize: '10MB',
      note: 'Only text-based PDFs are supported. Image-only or password-protected PDFs cannot be converted.'
    },
    { status: 200 }
  );
}
