"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

interface WaterIntakeResult {
  dailyWaterNeeds: number;
  baseWaterNeeds: number;
  activityAdjustment: number;
  climateAdjustment: number;
  pregnancyAdjustment: number;
  recommendations: {
    glasses: number;
    bottles: number;
    liters: number;
    ounces: number;
  };
}

export default function WaterIntakeCalculator() {
  const [weight, setWeight] = useState<number>(70);
  const [age, setAge] = useState<number>(30);
  const [gender, setGender] = useState<string>("male");
  const [activityLevel, setActivityLevel] = useState<string>("moderate");
  const [climate, setClimate] = useState<string>("temperate");
  const [isPregnant, setIsPregnant] = useState<boolean>(false);
  const [isBreastfeeding, setIsBreastfeeding] = useState<boolean>(false);
  const [unit, setUnit] = useState<string>("metric");
  const [result, setResult] = useState<WaterIntakeResult | null>(null);

  const calculateWaterIntake = () => {
    const weightKg = unit === "metric" ? weight : weight * 0.453592;
    
    // Base water needs calculation
    // Institute of Medicine recommendations: 35ml per kg body weight
    let baseWaterNeeds = weightKg * 35; // ml per day

    // Age adjustment
    if (age > 65) {
      baseWaterNeeds *= 1.1; // Older adults need slightly more
    } else if (age < 18) {
      baseWaterNeeds *= 1.2; // Children and teens need more per kg
    }

    // Gender adjustment (women generally need slightly less)
    if (gender === "female") {
      baseWaterNeeds *= 0.9;
    }

    // Activity level adjustment
    let activityMultiplier = 1;
    switch (activityLevel) {
      case "sedentary":
        activityMultiplier = 0.9;
        break;
      case "light":
        activityMultiplier = 1.0;
        break;
      case "moderate":
        activityMultiplier = 1.2;
        break;
      case "active":
        activityMultiplier = 1.4;
        break;
      case "very_active":
        activityMultiplier = 1.6;
        break;
    }

    const activityAdjustment = baseWaterNeeds * (activityMultiplier - 1);

    // Climate adjustment
    let climateMultiplier = 1;
    switch (climate) {
      case "cold":
        climateMultiplier = 0.95;
        break;
      case "temperate":
        climateMultiplier = 1.0;
        break;
      case "hot":
        climateMultiplier = 1.15;
        break;
      case "very_hot":
        climateMultiplier = 1.3;
        break;
    }

    const climateAdjustment = baseWaterNeeds * (climateMultiplier - 1);

    // Pregnancy and breastfeeding adjustments
    let pregnancyAdjustment = 0;
    if (isPregnant) {
      pregnancyAdjustment += 300; // Additional 300ml for pregnancy
    }
    if (isBreastfeeding) {
      pregnancyAdjustment += 700; // Additional 700ml for breastfeeding
    }

    // Calculate total daily water needs
    const dailyWaterNeeds = baseWaterNeeds + activityAdjustment + climateAdjustment + pregnancyAdjustment;

    // Convert to different units for recommendations
    const liters = dailyWaterNeeds / 1000;
    const ounces = dailyWaterNeeds * 0.033814;
    const glasses = dailyWaterNeeds / 250; // 250ml per glass
    const bottles = dailyWaterNeeds / 500; // 500ml per bottle

    setResult({
      dailyWaterNeeds,
      baseWaterNeeds,
      activityAdjustment,
      climateAdjustment,
      pregnancyAdjustment,
      recommendations: {
        glasses: Math.round(glasses),
        bottles: Math.round(bottles * 10) / 10,
        liters: Math.round(liters * 10) / 10,
        ounces: Math.round(ounces)
      }
    });
  };

  const reset = () => {
    setWeight(70);
    setAge(30);
    setGender("male");
    setActivityLevel("moderate");
    setClimate("temperate");
    setIsPregnant(false);
    setIsBreastfeeding(false);
    setResult(null);
  };

  const getHydrationTips = (): string[] => {
    const tips = [
      "Drink water first thing in the morning",
      "Keep a water bottle with you throughout the day",
      "Eat water-rich foods like fruits and vegetables",
      "Monitor your urine color - pale yellow indicates good hydration"
    ];

    if (activityLevel === "active" || activityLevel === "very_active") {
      tips.push("Drink extra water before, during, and after exercise");
    }

    if (climate === "hot" || climate === "very_hot") {
      tips.push("Increase water intake in hot weather to prevent dehydration");
    }

    if (age > 65) {
      tips.push("Older adults should drink water regularly, even if not thirsty");
    }

    return tips;
  };

  const getHydrationStatus = (intake: number): { status: string; color: string; message: string } => {
    const liters = intake / 1000;
    
    if (liters < 1.5) {
      return { 
        status: "Low", 
        color: "red", 
        message: "You may be at risk of dehydration" 
      };
    } else if (liters < 2.5) {
      return { 
        status: "Adequate", 
        color: "yellow", 
        message: "You're meeting basic hydration needs" 
      };
    } else if (liters < 4) {
      return { 
        status: "Good", 
        color: "green", 
        message: "You're well hydrated for your lifestyle" 
      };
    } else {
      return { 
        status: "High", 
        color: "blue", 
        message: "High water intake - ensure electrolyte balance" 
      };
    }
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Daily Water Intake Calculator</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Input Section */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div>
                <Label htmlFor="unit">Unit System</Label>
                <Select value={unit} onValueChange={setUnit}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="metric">Metric</SelectItem>
                    <SelectItem value="imperial">Imperial</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="weight">Weight ({unit === "metric" ? "kg" : "lbs"})</Label>
                <Input
                  id="weight"
                  type="number"
                  value={weight}
                  onChange={(e) => setWeight(parseFloat(e.target.value) || 0)}
                  min="0"
                  step="0.1"
                />
              </div>

              <div>
                <Label htmlFor="age">Age</Label>
                <Input
                  id="age"
                  type="number"
                  value={age}
                  onChange={(e) => setAge(parseInt(e.target.value) || 0)}
                  min="1"
                  max="120"
                />
              </div>

              <div>
                <Label htmlFor="gender">Gender</Label>
                <Select value={gender} onValueChange={setGender}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="male">Male</SelectItem>
                    <SelectItem value="female">Female</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-4">
              <div>
                <Label htmlFor="activity-level">Activity Level</Label>
                <Select value={activityLevel} onValueChange={setActivityLevel}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="sedentary">Sedentary (little/no exercise)</SelectItem>
                    <SelectItem value="light">Light (light exercise 1-3 days/week)</SelectItem>
                    <SelectItem value="moderate">Moderate (moderate exercise 3-5 days/week)</SelectItem>
                    <SelectItem value="active">Active (hard exercise 6-7 days/week)</SelectItem>
                    <SelectItem value="very_active">Very Active (very hard exercise, physical job)</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="climate">Climate</Label>
                <Select value={climate} onValueChange={setClimate}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="cold">Cold (below 10°C/50°F)</SelectItem>
                    <SelectItem value="temperate">Temperate (10-25°C/50-77°F)</SelectItem>
                    <SelectItem value="hot">Hot (25-35°C/77-95°F)</SelectItem>
                    <SelectItem value="very_hot">Very Hot (above 35°C/95°F)</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {gender === "female" && (
                <>
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="pregnant"
                      checked={isPregnant}
                      onChange={(e) => setIsPregnant(e.target.checked)}
                    />
                    <Label htmlFor="pregnant">Pregnant</Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="breastfeeding"
                      checked={isBreastfeeding}
                      onChange={(e) => setIsBreastfeeding(e.target.checked)}
                    />
                    <Label htmlFor="breastfeeding">Breastfeeding</Label>
                  </div>
                </>
              )}

              <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <h3 className="font-semibold mb-2">Quick Facts</h3>
                <div className="space-y-1 text-sm">
                  <div>• Body is ~60% water</div>
                  <div>• Brain is ~75% water</div>
                  <div>• Muscles are ~75% water</div>
                  <div>• Blood is ~83% water</div>
                </div>
              </div>
            </div>
          </div>

          {/* Calculate Button */}
          <div className="flex gap-4">
            <Button onClick={calculateWaterIntake} className="flex-1">
              Calculate Water Needs
            </Button>
            <Button onClick={reset} variant="outline">
              Reset
            </Button>
          </div>

          {/* Results */}
          {result && (
            <div className="space-y-6">
              {/* Status Card */}
              <Card className={`bg-${getHydrationStatus(result.dailyWaterNeeds).color}-50 dark:bg-${getHydrationStatus(result.dailyWaterNeeds).color}-900/20`}>
                <CardContent className="pt-6">
                  <div className="text-center space-y-2">
                    <div className="text-sm text-muted-foreground">Hydration Status</div>
                    <div className={`text-2xl font-bold text-${getHydrationStatus(result.dailyWaterNeeds).color}-600 dark:text-${getHydrationStatus(result.dailyWaterNeeds).color}-400`}>
                      {getHydrationStatus(result.dailyWaterNeeds).status}
                    </div>
                    <div className="text-sm">{getHydrationStatus(result.dailyWaterNeeds).message}</div>
                  </div>
                </CardContent>
              </Card>

              {/* Main Results */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <Card className="bg-blue-50 dark:bg-blue-900/20">
                  <CardContent className="pt-6">
                    <div className="text-center space-y-2">
                      <div className="text-sm text-muted-foreground">Daily Water Needs</div>
                      <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                        {result.recommendations.liters}L
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {result.recommendations.ounces} fl oz
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-green-50 dark:bg-green-900/20">
                  <CardContent className="pt-6">
                    <div className="text-center space-y-2">
                      <div className="text-sm text-muted-foreground">Glasses per Day</div>
                      <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                        {result.recommendations.glasses}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        250ml each
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-purple-50 dark:bg-purple-900/20">
                  <CardContent className="pt-6">
                    <div className="text-center space-y-2">
                      <div className="text-sm text-muted-foreground">Water Bottles</div>
                      <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                        {result.recommendations.bottles}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        500ml each
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-orange-50 dark:bg-orange-900/20">
                  <CardContent className="pt-6">
                    <div className="text-center space-y-2">
                      <div className="text-sm text-muted-foreground">Total Volume</div>
                      <div className="text-2xl font-bold text-orange-600 dark:text-orange-400">
                        {Math.round(result.dailyWaterNeeds)}ml
                      </div>
                      <div className="text-sm text-muted-foreground">
                        per day
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Breakdown */}
              <Card>
                <CardHeader>
                  <CardTitle>Water Needs Breakdown</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span>Base water needs:</span>
                      <span className="font-semibold">{Math.round(result.baseWaterNeeds)}ml</span>
                    </div>
                    {result.activityAdjustment !== 0 && (
                      <div className="flex justify-between">
                        <span>Activity adjustment:</span>
                        <span className="font-semibold">
                          {result.activityAdjustment > 0 ? '+' : ''}{Math.round(result.activityAdjustment)}ml
                        </span>
                      </div>
                    )}
                    {result.climateAdjustment !== 0 && (
                      <div className="flex justify-between">
                        <span>Climate adjustment:</span>
                        <span className="font-semibold">
                          {result.climateAdjustment > 0 ? '+' : ''}{Math.round(result.climateAdjustment)}ml
                        </span>
                      </div>
                    )}
                    {result.pregnancyAdjustment > 0 && (
                      <div className="flex justify-between">
                        <span>Pregnancy/Breastfeeding:</span>
                        <span className="font-semibold">+{result.pregnancyAdjustment}ml</span>
                      </div>
                    )}
                    <div className="flex justify-between border-t pt-2">
                      <span className="font-semibold">Total daily needs:</span>
                      <span className="font-bold">{Math.round(result.dailyWaterNeeds)}ml</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Hydration Tips */}
              <Card>
                <CardHeader>
                  <CardTitle>Personalized Hydration Tips</CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {getHydrationTips().map((tip, index) => (
                      <li key={index} className="flex items-start space-x-2">
                        <span className="text-blue-500 mt-1">•</span>
                        <span className="text-sm">{tip}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            </div>
          )}

          {/* General Tips */}
          <div className="p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
            <h3 className="font-semibold mb-2">Hydration Guidelines</h3>
            <ul className="text-sm space-y-1">
              <li>• Drink water throughout the day, not just when thirsty</li>
              <li>• Increase intake during illness, hot weather, or exercise</li>
              <li>• Foods contribute ~20% of daily fluid intake</li>
              <li>• Caffeine and alcohol can have mild diuretic effects</li>
              <li>• Clear or pale yellow urine indicates good hydration</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
