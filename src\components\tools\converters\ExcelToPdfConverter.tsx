"use client";

import { useState, useEffect } from "react";
import FileUploader from "../FileUploader";
import { useProtectedDownload } from "@/hooks/useProtectedDownload";
import ErrorDisplay, { getErrorType } from "@/components/ui/ErrorDisplay";
import SuccessNotification, { successMessages, createSuccessData } from "@/components/ui/SuccessNotification";
import ProgressIndicator, { getStageFromProgress, estimateRemainingTime } from "@/components/ui/ProgressIndicator";
import MobileOptimized, { MobileButton } from "@/components/ui/MobileOptimized";
import { useMobileDetection } from "@/hooks/useMobileDetection";
import SEOHead, { generateToolSEO, generateFAQStructuredData } from "@/components/seo/SEOHead";

const config = {
  sourceExtension: /\.(xls|xlsx)$/,
  fileExtension: ".pdf",
  acceptedFileTypes: ".xls,.xlsx,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
  maxFileSizeMB: 10,
  convertButtonText: "Convert to PDF",
  downloadButtonText: "Download PDF Document",
  aboutTitle: "About Excel to PDF Conversion",
  aboutDescription: "Our Excel to PDF converter transforms Microsoft Excel spreadsheets (.xls or .xlsx) into PDF files while preserving the original formatting, tables, charts, and data. This is useful when you need to share financial data or reports in a format that can't be easily edited.",
  noteDescription: "The conversion maintains all cell formatting, formulas (as values), charts, and tables. For multi-sheet workbooks, each sheet will be converted to a separate page in the PDF document. Very large spreadsheets may be scaled to fit the PDF page size, which could affect readability of small text."
};

export default function ExcelToPdfConverter() {
  const [file, setFile] = useState<File | null>(null);
  const [isConverting, setIsConverting] = useState(false);
  const [convertedFileUrl, setConvertedFileUrl] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [conversionProgress, setConversionProgress] = useState(0);
  const [conversionStartTime, setConversionStartTime] = useState<number>(0);
  const [convertedBlob, setConvertedBlob] = useState<Blob | null>(null);
  const [showSuccess, setShowSuccess] = useState(false);

  // Mobile detection
  const { isMobile, isTablet, isTouchDevice } = useMobileDetection();

  // SEO data
  const seoData = generateToolSEO({
    name: "Excel to PDF Converter",
    description: "Convert Excel spreadsheets (.xlsx, .xls) to PDF documents online for free. Fast, secure, and no registration required.",
    slug: "excel-to-pdf",
    keywords: [
      "Excel to PDF",
      "XLSX to PDF",
      "XLS to PDF",
      "spreadsheet to PDF",
      "convert Excel",
      "Excel converter",
      "online Excel to PDF"
    ],
    category: "Office to PDF"
  });

  // FAQ data for structured data
  const faqData = generateFAQStructuredData([
    {
      question: "How do I convert Excel to PDF?",
      answer: "Upload your Excel file (.xlsx or .xls), click 'Convert to PDF', and download your converted PDF document. The process is free and requires no registration."
    },
    {
      question: "Is it safe to convert Excel files online?",
      answer: "Yes, our Excel to PDF converter uses secure processing. Files are automatically deleted after conversion and we don't store any of your documents."
    },
    {
      question: "What Excel formats are supported?",
      answer: "We support both .xlsx (Excel 2007+) and .xls (Excel 97-2003) file formats for conversion to PDF."
    },
    {
      question: "Is there a file size limit?",
      answer: "Yes, the maximum file size for Excel to PDF conversion is 10MB to ensure fast processing and optimal performance."
    },
    {
      question: "Do I need to install any software?",
      answer: "No, our Excel to PDF converter works entirely in your web browser. No software installation or registration is required."
    }
  ]);

  // Protected download hook
  const {
    isAuthenticated,
    isLoading: authLoading,
    isDownloading,
    initiateDownload,
    processPendingDownload,
    canDownload,
    needsAuth
  } = useProtectedDownload({
    requireAuth: true,
    onAuthRequired: () => {
      setError("Please log in to download converted files. You can still upload and convert files without logging in.");
    },
    onDownloadStart: (data) => {
      console.log("Starting download:", data);
    },
    onDownloadComplete: (data) => {
      console.log("Download completed:", data);
    },
    onError: (errorMsg) => {
      setError(`Download failed: ${errorMsg}`);
    }
  });

  // Process pending downloads after login
  useEffect(() => {
    if (isAuthenticated) {
      processPendingDownload();
    }
  }, [isAuthenticated, processPendingDownload]);

  const handleFileSelect = (selectedFile: File) => {
    setFile(selectedFile);
    setConvertedFileUrl(null);
    setError(null);
    setConversionProgress(0);
    setConvertedBlob(null);
    setShowSuccess(false);
    setConversionStartTime(0);
  };

  const handleConvert = async () => {
    if (!file) return;

    setIsConverting(true);
    setError(null);
    setConversionProgress(0);
    setShowSuccess(false);
    setConversionStartTime(Date.now());

    try {
      // Create FormData for file upload
      const formData = new FormData();
      formData.append('file', file);

      // Start progress simulation
      const progressInterval = setInterval(() => {
        setConversionProgress(prev => {
          if (prev >= 90) return prev;
          return prev + Math.random() * 10;
        });
      }, 400);

      // Send file to conversion API
      const response = await fetch('/api/tools/excel-to-pdf', {
        method: 'POST',
        body: formData,
      });

      clearInterval(progressInterval);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Conversion failed');
      }

      // Get the converted PDF blob
      const pdfBlob = await response.blob();
      setConvertedFileUrl(URL.createObjectURL(pdfBlob));
      setConvertedBlob(pdfBlob);
      setConversionProgress(100);
      setShowSuccess(true);

    } catch (err) {
      setError(err instanceof Error ? err.message : "An error occurred during conversion. Please try again.");
      console.error(err);
    } finally {
      setIsConverting(false);
    }
  };

  const handleDownload = async () => {
    if (!convertedFileUrl || !file) return;

    const fileName = file.name.replace(config.sourceExtension, config.fileExtension);

    // Calculate file sizes
    const originalFileSize = file.size;
    let convertedFileSize = 0;

    try {
      const response = await fetch(convertedFileUrl);
      const blob = await response.blob();
      convertedFileSize = blob.size;
    } catch (error) {
      console.warn("Could not determine converted file size:", error);
    }

    // Use protected download
    await initiateDownload(convertedFileUrl, {
      fileName,
      conversionType: 'excel-to-pdf',
      originalFileSize,
      convertedFileSize,
      toolName: 'Excel to PDF Converter'
    });
  };

  return (
    <>
      <SEOHead
        title={seoData.title}
        description={seoData.description}
        keywords={seoData.keywords}
        canonicalUrl={seoData.canonicalUrl}
        structuredData={[seoData.structuredData, faqData]}
      />

      <MobileOptimized
        enableHaptic={isTouchDevice}
        className="space-y-4 sm:space-y-6"
      >
      <div className="bg-blue-50 p-3 sm:p-4 rounded-lg">
        <h2 className="text-base sm:text-lg font-semibold mb-2">
          How to Convert Excel to PDF
        </h2>
        <ol className="list-decimal list-inside space-y-1 sm:space-y-2 text-sm sm:text-base text-gray-700">
          <li>Upload your Excel spreadsheet using the uploader below.</li>
          <li>Click the &quot;Convert to PDF&quot; button to start the conversion.</li>
          <li>Download your converted PDF document when ready.</li>
        </ol>
      </div>

      <div className="space-y-3 sm:space-y-4">
        <FileUploader
          acceptedFileTypes={config.acceptedFileTypes}
          maxFileSizeMB={config.maxFileSizeMB}
          onFileSelect={handleFileSelect}
        />

        {file && (
          <div className="flex items-center space-x-2 p-3 bg-gray-50 rounded-lg">
            <svg
              className="w-6 h-6 text-gray-500"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
              ></path>
            </svg>
            <span className="flex-1 truncate">{file.name}</span>
            <span className="text-sm text-gray-500">
              {(file.size / (1024 * 1024)).toFixed(2)} MB
            </span>
            <button
              onClick={() => setFile(null)}
              className="text-red-500 hover:text-red-700"
              aria-label="Remove file"
            >
              <svg
                className="w-5 h-5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M6 18L18 6M6 6l12 12"
                ></path>
              </svg>
            </button>
          </div>
        )}

        {file && (
          <MobileButton
            onClick={handleConvert}
            disabled={isConverting}
            variant="primary"
            size={isMobile ? "lg" : "md"}
            className="w-full"
            ariaLabel={isConverting ? "Converting file, please wait" : "Convert Excel file to PDF"}
          >
            {isConverting ? "Converting..." : config.convertButtonText}
          </MobileButton>
        )}

        {isConverting && (
          <ProgressIndicator
            progress={conversionProgress}
            stage={getStageFromProgress(conversionProgress)}
            estimatedTime={conversionStartTime > 0 ? estimateRemainingTime(conversionProgress, conversionStartTime) : undefined}
            fileName={file?.name}
            showPercentage={true}
          />
        )}

        {error && (
          <ErrorDisplay
            error={error}
            type={getErrorType(error)}
            onRetry={() => {
              setError(null);
              if (file) handleConvert();
            }}
            onDismiss={() => setError(null)}
          />
        )}

        {showSuccess && convertedFileUrl && file && convertedBlob && (
          <SuccessNotification
            message={successMessages['excel-to-pdf']}
            details={createSuccessData(file, convertedBlob, conversionStartTime, 'Excel to PDF Converter').details}
            onDownload={handleDownload}
            downloadLabel={
              isDownloading
                ? "Downloading..."
                : needsAuth
                  ? "Login to Download"
                  : config.downloadButtonText
            }
            onDismiss={() => setShowSuccess(false)}
            showStats={true}
          />
        )}
      </div>

      <div className="mt-8 border-t pt-6">
        <h3 className="text-lg font-semibold mb-4">
          {config.aboutTitle}
        </h3>
        <p className="text-gray-700 mb-4">
          {config.aboutDescription}
        </p>
        <div className="bg-yellow-50 p-4 rounded-lg">
          <h4 className="font-medium text-yellow-800 mb-2">Please Note:</h4>
          <p className="text-yellow-700 text-sm">
            {config.noteDescription}
          </p>
        </div>
      </div>
    </MobileOptimized>
    </>
  );
}
