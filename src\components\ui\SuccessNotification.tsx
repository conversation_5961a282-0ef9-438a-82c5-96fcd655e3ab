"use client";

import { CheckCircle, Download, Clock, FileText, X } from "lucide-react";
import { formatFileSize } from "@/lib/utils";

interface ConversionDetails {
  originalSize?: number;
  convertedSize?: number;
  conversionTime?: number;
  fileName?: string;
}

interface SuccessNotificationProps {
  message: string;
  details?: ConversionDetails;
  onDismiss?: () => void;
  onDownload?: () => void;
  downloadLabel?: string;
  className?: string;
  showStats?: boolean;
}

export default function SuccessNotification({
  message,
  details,
  onDismiss,
  onDownload,
  downloadLabel = "Download File",
  className = "",
  showStats = true
}: SuccessNotificationProps) {
  const getSizeReduction = () => {
    if (!details?.originalSize || !details?.convertedSize) return null;
    
    const reduction = ((details.originalSize - details.convertedSize) / details.originalSize) * 100;
    return reduction > 0 ? reduction.toFixed(1) : null;
  };

  const formatTime = (seconds: number) => {
    if (seconds < 60) return `${seconds}s`;
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  };

  const sizeReduction = getSizeReduction();

  return (
    <div
      className={`bg-green-50 border border-green-200 rounded-lg p-4 ${className}`}
      role="status"
      aria-live="polite"
      aria-labelledby="success-title"
      aria-describedby="success-description"
    >
      <div className="flex items-start">
        <div className="flex-shrink-0" aria-hidden="true">
          <CheckCircle className="h-5 w-5 text-green-500" />
        </div>

        <div className="ml-3 flex-1">
          <h3
            id="success-title"
            className="text-sm font-medium text-green-800"
          >
            Conversion Completed Successfully!
          </h3>

          <div className="mt-1 text-sm text-green-700">
            <p id="success-description">{message}</p>
          </div>

          {/* Conversion Statistics */}
          {showStats && details && (
            <div className="mt-3 grid grid-cols-1 sm:grid-cols-3 gap-3">
              {details.originalSize && details.convertedSize && (
                <div className="flex items-center text-xs text-green-600">
                  <FileText className="h-3 w-3 mr-1" aria-hidden="true" />
                  <span>
                    <span className="sr-only">File size: </span>
                    {formatFileSize(details.originalSize)} → {formatFileSize(details.convertedSize)}
                    {sizeReduction && (
                      <span className="ml-1 font-medium">
                        ({sizeReduction}% smaller)
                      </span>
                    )}
                  </span>
                </div>
              )}

              {details.conversionTime && (
                <div className="flex items-center text-xs text-green-600">
                  <Clock className="h-3 w-3 mr-1" aria-hidden="true" />
                  <span>
                    <span className="sr-only">Conversion time: </span>
                    Completed in {formatTime(details.conversionTime)}
                  </span>
                </div>
              )}

              {details.fileName && (
                <div className="flex items-center text-xs text-green-600">
                  <FileText className="h-3 w-3 mr-1" aria-hidden="true" />
                  <span className="truncate">
                    <span className="sr-only">File name: </span>
                    {details.fileName}
                  </span>
                </div>
              )}
            </div>
          )}

          {/* Action Buttons */}
          <div className="mt-4 flex flex-col sm:flex-row gap-2">
            {onDownload && (
              <button
                type="button"
                onClick={onDownload}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                aria-describedby="success-description"
              >
                <Download className="h-4 w-4 mr-2" aria-hidden="true" />
                {downloadLabel}
              </button>
            )}

            {onDismiss && (
              <button
                type="button"
                onClick={onDismiss}
                className="inline-flex items-center px-4 py-2 border border-green-300 text-sm font-medium rounded-md text-green-700 bg-white hover:bg-green-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                aria-label="Continue to next step or dismiss notification"
              >
                Continue
              </button>
            )}
          </div>
        </div>
        
        {/* Dismiss Button */}
        {onDismiss && (
          <div className="ml-auto pl-3">
            <div className="-mx-1.5 -my-1.5">
              <button
                type="button"
                onClick={onDismiss}
                className="inline-flex rounded-md p-1.5 text-green-500 hover:bg-green-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-green-50 focus:ring-green-600"
                aria-label="Close success notification"
              >
                <span className="sr-only">Dismiss</span>
                <X className="h-4 w-4" aria-hidden="true" />
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

// Utility function to create success notification data
export function createSuccessData(
  originalFile: File,
  convertedBlob: Blob,
  conversionStartTime: number,
  toolName: string
): {
  message: string;
  details: ConversionDetails;
} {
  const conversionTime = Math.round((Date.now() - conversionStartTime) / 1000);
  
  return {
    message: `Your file has been successfully converted using ${toolName}.`,
    details: {
      originalSize: originalFile.size,
      convertedSize: convertedBlob.size,
      conversionTime,
      fileName: originalFile.name
    }
  };
}

// Pre-configured success messages for different tools
export const successMessages = {
  'excel-to-pdf': 'Your Excel spreadsheet has been successfully converted to PDF format.',
  'word-to-pdf': 'Your Word document has been successfully converted to PDF format.',
  'pdf-to-word': 'Your PDF has been successfully converted to an editable Word document.',
  'pdf-to-excel': 'Your PDF has been successfully converted to an Excel spreadsheet.',
  'merge-pdf': 'Your PDF files have been successfully merged into a single document.',
  'split-pdf': 'Your PDF has been successfully split into separate pages.',
  'compress-pdf': 'Your PDF has been successfully compressed to reduce file size.',
  'rotate-pdf': 'Your PDF pages have been successfully rotated.',
  'jpg-to-pdf': 'Your image has been successfully converted to PDF format.',
  'pdf-to-jpg': 'Your PDF has been successfully converted to image files.',
  'html-to-pdf': 'Your HTML content has been successfully converted to PDF format.',
  'powerpoint-to-pdf': 'Your PowerPoint presentation has been successfully converted to PDF format.',
  'pdf-to-powerpoint': 'Your PDF has been successfully converted to an editable PowerPoint presentation.',
  'add-watermark': 'Your PDF has been successfully watermarked.',
  'protect-pdf': 'Your PDF has been successfully password protected.',
  'pdf-to-pdfa': 'Your PDF has been successfully converted to PDF/A format.'
};
