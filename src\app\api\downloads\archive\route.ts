import { NextRequest, NextResponse } from 'next/server';
import { DownloadArchiver } from '@/lib/archival/downloadArchiver';

/**
 * POST /api/downloads/archive
 * Manually trigger the download archival process
 * This endpoint can be called by cron jobs or admin users
 */
export async function POST(request: NextRequest) {
  try {
    // Check for authorization (API key or admin role)
    const authHeader = request.headers.get('authorization');
    const apiKey = process.env.ARCHIVAL_API_KEY;
    
    // Allow access with API key or admin session
    if (apiKey && authHeader === `Bearer ${apiKey}`) {
      // API key authentication for cron jobs
    } else {
      // For manual triggers, check admin role
      const { getServerSession } = await import('next-auth/next');
      const { authOptions } = await import('@/lib/auth');
      
      const session = await getServerSession(authOptions);
      if (!session || session.user.role !== 'admin') {
        return NextResponse.json(
          { error: 'Unauthorized. Admin access required.' },
          { status: 401 }
        );
      }
    }

    // Parse request parameters
    const body = await request.json().catch(() => ({}));
    const hoursOld = body.hoursOld || 24;
    const archiveDirectory = body.archiveDirectory || './data/archives';

    // Initialize archiver
    const archiver = new DownloadArchiver(archiveDirectory, hoursOld);

    // Run archival process
    const result = await archiver.archiveDownloads();

    if (result.success) {
      return NextResponse.json({
        success: true,
        message: 'Download archival completed successfully',
        data: {
          recordsArchived: result.recordsArchived,
          filePath: result.filePath,
          executionTime: result.executionTime
        }
      });
    } else {
      return NextResponse.json({
        success: false,
        message: 'Download archival failed',
        error: result.error,
        executionTime: result.executionTime
      }, { status: 500 });
    }

  } catch (error) {
    console.error('Archive API error:', error);
    
    return NextResponse.json({
      success: false,
      message: 'Archive API error',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

/**
 * GET /api/downloads/archive
 * Get archival statistics and status
 */
export async function GET(request: NextRequest) {
  try {
    // Check for authorization
    const { getServerSession } = await import('next-auth/next');
    const { authOptions } = await import('@/lib/auth');
    
    const session = await getServerSession(authOptions);
    if (!session || session.user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized. Admin access required.' },
        { status: 401 }
      );
    }

    // Initialize archiver
    const archiver = new DownloadArchiver();

    // Get archival statistics
    const stats = await archiver.getArchivalStats();

    // Get database statistics
    const { connectToDatabase } = await import('@/lib/mongo');
    const { db } = await connectToDatabase();

    // Count current records in database
    const totalRecords = await db.collection('downloads').countDocuments();
    
    // Count records older than 24 hours
    const cutoffDate = new Date();
    cutoffDate.setHours(cutoffDate.getHours() - 24);
    const recordsToArchive = await db.collection('downloads').countDocuments({
      createdAt: { $lt: cutoffDate }
    });

    // Get recent records for preview
    const recentRecords = await db.collection('downloads')
      .find({})
      .sort({ createdAt: -1 })
      .limit(5)
      .project({
        fileName: 1,
        conversionType: 1,
        userEmail: 1,
        createdAt: 1
      })
      .toArray();

    return NextResponse.json({
      success: true,
      archivalStats: stats,
      databaseStats: {
        totalRecords,
        recordsToArchive,
        recentRecords: recentRecords.map(record => ({
          fileName: record.fileName,
          conversionType: record.conversionType,
          userEmail: record.userEmail,
          createdAt: record.createdAt
        }))
      },
      nextArchivalDue: recordsToArchive > 0 ? 'Now' : 'No records to archive',
      archivalConfiguration: {
        hoursOld: 24,
        archiveDirectory: './data/archives',
        automaticArchival: process.env.ENABLE_AUTO_ARCHIVAL === 'true'
      }
    });

  } catch (error) {
    console.error('Archive stats API error:', error);
    
    return NextResponse.json({
      success: false,
      message: 'Failed to get archival statistics',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
