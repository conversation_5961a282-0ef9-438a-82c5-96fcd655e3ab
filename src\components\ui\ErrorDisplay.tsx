"use client";

import { AlertCircle, RefreshCw, X } from "lucide-react";

interface ErrorDisplayProps {
  error: string | null;
  type?: 'conversion' | 'upload' | 'download' | 'auth' | 'general';
  onRetry?: () => void;
  onDismiss?: () => void;
  className?: string;
}

const errorTypeConfig = {
  conversion: {
    icon: AlertCircle,
    bgColor: 'bg-red-50',
    borderColor: 'border-red-200',
    textColor: 'text-red-800',
    iconColor: 'text-red-500',
    title: 'Conversion Failed'
  },
  upload: {
    icon: AlertCircle,
    bgColor: 'bg-orange-50',
    borderColor: 'border-orange-200',
    textColor: 'text-orange-800',
    iconColor: 'text-orange-500',
    title: 'Upload Error'
  },
  download: {
    icon: AlertCircle,
    bgColor: 'bg-purple-50',
    borderColor: 'border-purple-200',
    textColor: 'text-purple-800',
    iconColor: 'text-purple-500',
    title: 'Download Error'
  },
  auth: {
    icon: AlertCircle,
    bgColor: 'bg-blue-50',
    borderColor: 'border-blue-200',
    textColor: 'text-blue-800',
    iconColor: 'text-blue-500',
    title: 'Authentication Required'
  },
  general: {
    icon: AlertCircle,
    bgColor: 'bg-red-50',
    borderColor: 'border-red-200',
    textColor: 'text-red-800',
    iconColor: 'text-red-500',
    title: 'Error'
  }
};

export default function ErrorDisplay({ 
  error, 
  type = 'general', 
  onRetry, 
  onDismiss, 
  className = '' 
}: ErrorDisplayProps) {
  if (!error) return null;

  const config = errorTypeConfig[type];
  const IconComponent = config.icon;

  return (
    <div
      className={`${config.bgColor} ${config.borderColor} border rounded-lg p-4 ${className}`}
      role="alert"
      aria-live="assertive"
      aria-labelledby="error-title"
      aria-describedby="error-description"
    >
      <div className="flex items-start">
        <div className="flex-shrink-0" aria-hidden="true">
          <IconComponent className={`h-5 w-5 ${config.iconColor}`} />
        </div>

        <div className="ml-3 flex-1">
          <h3
            id="error-title"
            className={`text-sm font-medium ${config.textColor}`}
          >
            {config.title}
          </h3>
          <div className={`mt-1 text-sm ${config.textColor}`}>
            <p id="error-description">{error}</p>
          </div>
          
          {(onRetry || onDismiss) && (
            <div className="mt-3 flex space-x-2">
              {onRetry && (
                <button
                  type="button"
                  onClick={onRetry}
                  className={`inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md ${config.textColor} bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500`}
                  aria-label="Retry the failed operation"
                >
                  <RefreshCw className="h-3 w-3 mr-1" aria-hidden="true" />
                  Try Again
                </button>
              )}

              {onDismiss && (
                <button
                  type="button"
                  onClick={onDismiss}
                  className={`inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md ${config.textColor} bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500`}
                  aria-label="Dismiss this error message"
                >
                  <X className="h-3 w-3 mr-1" aria-hidden="true" />
                  Dismiss
                </button>
              )}
            </div>
          )}
        </div>
        
        {onDismiss && (
          <div className="ml-auto pl-3">
            <div className="-mx-1.5 -my-1.5">
              <button
                type="button"
                onClick={onDismiss}
                className={`inline-flex rounded-md p-1.5 ${config.iconColor} hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-red-50 focus:ring-red-600`}
                aria-label="Close error message"
              >
                <span className="sr-only">Dismiss</span>
                <X className="h-4 w-4" aria-hidden="true" />
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

// Helper function to get appropriate error type based on error message
export function getErrorType(error: string): ErrorDisplayProps['type'] {
  const errorLower = error.toLowerCase();
  
  if (errorLower.includes('login') || errorLower.includes('auth') || errorLower.includes('sign in')) {
    return 'auth';
  }
  if (errorLower.includes('upload') || errorLower.includes('file')) {
    return 'upload';
  }
  if (errorLower.includes('download')) {
    return 'download';
  }
  if (errorLower.includes('convert') || errorLower.includes('process')) {
    return 'conversion';
  }
  
  return 'general';
}

// Pre-configured error messages for common scenarios
export const commonErrors = {
  fileTooBig: "File size exceeds the maximum limit of 10MB. Please choose a smaller file.",
  invalidFileType: "Invalid file type. Please select a supported file format.",
  conversionFailed: "Conversion failed due to an unexpected error. Please try again or contact support if the problem persists.",
  uploadFailed: "Failed to upload file. Please check your internet connection and try again.",
  downloadFailed: "Failed to prepare download. Please try again.",
  authRequired: "Please log in to download converted files. You can still upload and convert files without logging in.",
  networkError: "Network error occurred. Please check your internet connection and try again.",
  serverError: "Server error occurred. Please try again later or contact support if the problem persists."
};
