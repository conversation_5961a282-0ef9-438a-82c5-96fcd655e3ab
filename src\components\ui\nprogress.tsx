"use client";

import { useEffect, useRef, useState } from "react";
import NProgress from "nprogress";
import { usePathname, useSearchParams, useRouter } from "next/navigation";

// NProgress configuration - YouTube-like appearance
NProgress.configure({
  minimum: 0.15,
  easing: 'ease',
  speed: 400,
  showSpinner: false,
  trickleSpeed: 150,
  barSelector: '[role="bar"]',
  template: '<div class="bar" role="bar"><div class="peg"></div></div>'
});

export function NavigationProgress() {
  const [isClient, setIsClient] = useState(false);
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const router = useRouter();
  const isNavigating = useRef(false);
  const timer = useRef<NodeJS.Timeout | null>(null);

  // Update meta tag for loading state
  const updateLoadingMeta = (loading: boolean) => {
    if (typeof document === 'undefined') return;
    // Find or create the loading meta tag
    let meta = document.querySelector('meta[name="loading"]');
    if (!meta) {
      meta = document.createElement('meta');
      meta.setAttribute('name', 'loading');
      document.head.appendChild(meta);
    }
    meta.setAttribute('content', loading ? 'true' : 'false');
  };

  // Only render on client side to avoid SSR issues
  useEffect(() => {
    setIsClient(true);
  }, []);

  useEffect(() => {
    if (!isClient) return;
    // Only start progress if we're not already navigating
    if (!isNavigating.current) {
      isNavigating.current = true;
      NProgress.start();
      updateLoadingMeta(true);
    }

    // Clear any existing timer
    if (timer.current) {
      clearTimeout(timer.current);
    }

    // Set a new timer to complete the progress
    timer.current = setTimeout(() => {
      NProgress.done();
      isNavigating.current = false;
      updateLoadingMeta(false);
      timer.current = null;
    }, 300); // Shorter delay for better UX

    // Cleanup function
    return () => {
      if (timer.current) {
        clearTimeout(timer.current);
      }
    };
  }, [pathname, searchParams, isClient]);

  // Add event listeners for router events - optimized for Next.js App Router
  useEffect(() => {
    if (!isClient) return;
    // Create a meta tag for loading state if it doesn't exist
    if (!document.querySelector('meta[name="loading"]')) {
      const meta = document.createElement('meta');
      meta.setAttribute('name', 'loading');
      meta.setAttribute('content', 'false');
      document.head.appendChild(meta);
    }

    // Function to handle navigation start
    const handleStart = () => {
      isNavigating.current = true;
      NProgress.start();
      updateLoadingMeta(true);
    };

    // Function to handle navigation complete
    const handleComplete = () => {
      if (timer.current) {
        clearTimeout(timer.current);
      }

      // Small delay to ensure smooth transition
      timer.current = setTimeout(() => {
        NProgress.done();
        isNavigating.current = false;
        updateLoadingMeta(false);
        timer.current = null;
      }, 100);
    };

    // Create custom events for Next.js App Router
    if (typeof window !== 'undefined') {
      // Create custom events if they don't exist
      if (!window.hasOwnProperty('navigationStart')) {
        Object.defineProperty(window, 'navigationStart', {
          get: function() {
            handleStart();
            return true;
          }
        });
      }

      if (!window.hasOwnProperty('navigationComplete')) {
        Object.defineProperty(window, 'navigationComplete', {
          get: function() {
            handleComplete();
            return true;
          }
        });
      }
    }

    // Add event listeners for navigation events
    window.addEventListener('beforeunload', handleStart);

    // For Next.js App Router
    const handleRouteChangeStart = () => {
      const event = new Event('routeChangeStart');
      document.dispatchEvent(event);
      handleStart();
    };

    const handleRouteChangeComplete = () => {
      const event = new Event('routeChangeComplete');
      document.dispatchEvent(event);
      handleComplete();
    };

    // Add event listeners for both legacy and custom events
    document.addEventListener('routeChangeStart', handleStart);
    document.addEventListener('routeChangeComplete', handleComplete);
    document.addEventListener('routeChangeError', handleComplete);

    // Observe pathname changes for App Router
    return () => {
      // Clean up event listeners
      window.removeEventListener('beforeunload', handleStart);
      document.removeEventListener('routeChangeStart', handleStart);
      document.removeEventListener('routeChangeComplete', handleComplete);
      document.removeEventListener('routeChangeError', handleComplete);

      if (timer.current) {
        clearTimeout(timer.current);
      }
      NProgress.done();
    };
  }, [pathname, searchParams, isClient]);

  if (!isClient) {
    return null;
  }

  return null;
}

// Function to manually control progress
export const Progress = {
  start: () => {
    NProgress.start();
    // Update meta tag
    const meta = document.querySelector('meta[name="loading"]');
    if (meta) meta.setAttribute('content', 'true');
  },
  done: () => {
    NProgress.done();
    // Update meta tag
    const meta = document.querySelector('meta[name="loading"]');
    if (meta) meta.setAttribute('content', 'false');
  },
  set: (n: number) => {
    NProgress.set(n);
  },
  inc: (n?: number) => {
    NProgress.inc(n);
  },
};
