"use client";

import Script from "next/script";

/**
 * Client-side scripts for font loading and loading state management
 * Separated from RootLayout to prevent RSC hydration issues
 */
export function ClientScripts() {
  return (
    <>
      {/* Font loading verification script */}
      <Script id="font-verification" strategy="afterInteractive">
        {`
          // Verify local fonts loaded correctly
          if (typeof window !== 'undefined') {
            // Check if fonts are loaded
            document.fonts.ready.then(() => {
              console.log('All fonts loaded successfully');
            }).catch(err => {
              console.warn('Font loading issue detected, using system fallback');
              document.documentElement.classList.add('font-fallback');
            });
          }
        `}
      </Script>

      {/* Clean loading state management - NO RETRY LOOPS */}
      <Script id="loading-state-manager" strategy="afterInteractive">
        {`
          // Clean loading state management without retry loops
          if (typeof window !== 'undefined') {
            function resetLoadingStates() {
              // Reset loading meta tag
              let loadingMeta = document.querySelector('meta[name="loading"]');
              if (loadingMeta) {
                loadingMeta.setAttribute('content', 'false');
              }

              // Hide NProgress bar
              if (typeof NProgress !== 'undefined') {
                NProgress.done();
              }

              // Reset loading classes
              document.querySelectorAll('.loading, .skeleton').forEach(el => {
                el.classList.remove('loading', 'skeleton');
              });

              // Clear loading attributes
              document.querySelectorAll('[data-loading="true"]').forEach(el => {
                el.removeAttribute('data-loading');
              });

              // Dispatch reset event for React components
              const resetEvent = new CustomEvent('reset-loading-states');
              document.dispatchEvent(resetEvent);
            }

            // Reset on page load
            window.addEventListener('load', resetLoadingStates);

            // Reset after route changes
            document.addEventListener('routeChangeComplete', resetLoadingStates);

            // Single fallback timeout
            setTimeout(resetLoadingStates, 5000);
          }
        `}
      </Script>
    </>
  );
}

