/**
 * Input Validation Utilities
 * Comprehensive validation for calculators and tools
 */

// File validation
export interface FileValidationOptions {
  maxSizeMB?: number;
  allowedTypes?: string[];
  allowedExtensions?: string[];
  minSizeMB?: number;
}

export interface FileValidationResult {
  isValid: boolean;
  error?: string;
  warnings?: string[];
}

export function validateFile(file: File, options: FileValidationOptions = {}): FileValidationResult {
  const {
    maxSizeMB = 50,
    allowedTypes = [],
    allowedExtensions = [],
    minSizeMB = 0
  } = options;

  const warnings: string[] = [];

  // Check file size
  const fileSizeMB = file.size / (1024 * 1024);
  
  if (fileSizeMB > maxSizeMB) {
    return {
      isValid: false,
      error: `File size (${fileSizeMB.toFixed(2)}MB) exceeds maximum allowed size (${maxSizeMB}MB)`
    };
  }

  if (fileSizeMB < minSizeMB) {
    return {
      isValid: false,
      error: `File size (${fileSizeMB.toFixed(2)}MB) is below minimum required size (${minSizeMB}MB)`
    };
  }

  // Check file type
  if (allowedTypes.length > 0 && !allowedTypes.includes(file.type)) {
    return {
      isValid: false,
      error: `File type "${file.type}" is not allowed. Allowed types: ${allowedTypes.join(', ')}`
    };
  }

  // Check file extension
  if (allowedExtensions.length > 0) {
    const fileExtension = file.name.split('.').pop()?.toLowerCase();
    if (!fileExtension || !allowedExtensions.includes(fileExtension)) {
      return {
        isValid: false,
        error: `File extension ".${fileExtension}" is not allowed. Allowed extensions: ${allowedExtensions.join(', ')}`
      };
    }
  }

  // Performance warnings
  if (fileSizeMB > 25) {
    warnings.push('Large file detected. Processing may take longer than usual.');
  }

  if (file.name.length > 100) {
    warnings.push('Very long filename detected. Consider using a shorter name.');
  }

  return {
    isValid: true,
    warnings: warnings.length > 0 ? warnings : undefined
  };
}

// Number validation
export interface NumberValidationOptions {
  min?: number;
  max?: number;
  integer?: boolean;
  positive?: boolean;
  allowZero?: boolean;
  maxDecimalPlaces?: number;
}

export interface NumberValidationResult {
  isValid: boolean;
  error?: string;
  sanitizedValue?: number;
}

export function validateNumber(value: any, options: NumberValidationOptions = {}): NumberValidationResult {
  const {
    min,
    max,
    integer = false,
    positive = false,
    allowZero = true,
    maxDecimalPlaces
  } = options;

  // Convert to number
  const num = typeof value === 'string' ? parseFloat(value) : Number(value);

  // Check if it's a valid number
  if (isNaN(num) || !isFinite(num)) {
    return {
      isValid: false,
      error: 'Please enter a valid number'
    };
  }

  // Check positive constraint
  if (positive && num < 0) {
    return {
      isValid: false,
      error: 'Number must be positive'
    };
  }

  // Check zero constraint
  if (!allowZero && num === 0) {
    return {
      isValid: false,
      error: 'Number cannot be zero'
    };
  }

  // Check integer constraint
  if (integer && !Number.isInteger(num)) {
    return {
      isValid: false,
      error: 'Number must be a whole number'
    };
  }

  // Check decimal places
  if (maxDecimalPlaces !== undefined) {
    const decimalPlaces = (num.toString().split('.')[1] || '').length;
    if (decimalPlaces > maxDecimalPlaces) {
      return {
        isValid: false,
        error: `Number cannot have more than ${maxDecimalPlaces} decimal places`
      };
    }
  }

  // Check min/max constraints
  if (min !== undefined && num < min) {
    return {
      isValid: false,
      error: `Number must be at least ${min}`
    };
  }

  if (max !== undefined && num > max) {
    return {
      isValid: false,
      error: `Number cannot exceed ${max}`
    };
  }

  return {
    isValid: true,
    sanitizedValue: num
  };
}

// String validation
export interface StringValidationOptions {
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  allowEmpty?: boolean;
  trim?: boolean;
  sanitize?: boolean;
}

export interface StringValidationResult {
  isValid: boolean;
  error?: string;
  sanitizedValue?: string;
}

export function validateString(value: any, options: StringValidationOptions = {}): StringValidationResult {
  const {
    minLength = 0,
    maxLength = Infinity,
    pattern,
    allowEmpty = true,
    trim = true,
    sanitize = true
  } = options;

  let str = String(value);

  // Trim if requested
  if (trim) {
    str = str.trim();
  }

  // Sanitize if requested
  if (sanitize) {
    str = str.replace(/[<>]/g, ''); // Remove potential HTML tags
  }

  // Check empty constraint
  if (!allowEmpty && str.length === 0) {
    return {
      isValid: false,
      error: 'This field is required'
    };
  }

  // Check length constraints
  if (str.length < minLength) {
    return {
      isValid: false,
      error: `Must be at least ${minLength} characters long`
    };
  }

  if (str.length > maxLength) {
    return {
      isValid: false,
      error: `Cannot exceed ${maxLength} characters`
    };
  }

  // Check pattern
  if (pattern && !pattern.test(str)) {
    return {
      isValid: false,
      error: 'Invalid format'
    };
  }

  return {
    isValid: true,
    sanitizedValue: str
  };
}

// Email validation
export function validateEmail(email: string): StringValidationResult {
  const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  
  const result = validateString(email, {
    minLength: 5,
    maxLength: 254,
    pattern: emailPattern,
    allowEmpty: false,
    trim: true
  });

  if (!result.isValid && result.error === 'Invalid format') {
    result.error = 'Please enter a valid email address';
  }

  return result;
}

// Date validation
export interface DateValidationOptions {
  minDate?: Date;
  maxDate?: Date;
  allowFuture?: boolean;
  allowPast?: boolean;
}

export interface DateValidationResult {
  isValid: boolean;
  error?: string;
  sanitizedValue?: Date;
}

export function validateDate(value: any, options: DateValidationOptions = {}): DateValidationResult {
  const {
    minDate,
    maxDate,
    allowFuture = true,
    allowPast = true
  } = options;

  let date: Date;

  // Convert to Date
  if (value instanceof Date) {
    date = value;
  } else if (typeof value === 'string' || typeof value === 'number') {
    date = new Date(value);
  } else {
    return {
      isValid: false,
      error: 'Please enter a valid date'
    };
  }

  // Check if it's a valid date
  if (isNaN(date.getTime())) {
    return {
      isValid: false,
      error: 'Please enter a valid date'
    };
  }

  const now = new Date();

  // Check future/past constraints
  if (!allowFuture && date > now) {
    return {
      isValid: false,
      error: 'Date cannot be in the future'
    };
  }

  if (!allowPast && date < now) {
    return {
      isValid: false,
      error: 'Date cannot be in the past'
    };
  }

  // Check min/max constraints
  if (minDate && date < minDate) {
    return {
      isValid: false,
      error: `Date cannot be before ${minDate.toLocaleDateString()}`
    };
  }

  if (maxDate && date > maxDate) {
    return {
      isValid: false,
      error: `Date cannot be after ${maxDate.toLocaleDateString()}`
    };
  }

  return {
    isValid: true,
    sanitizedValue: date
  };
}

// Batch validation
export function validateBatch(validations: Array<() => { isValid: boolean; error?: string }>): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];

  for (const validation of validations) {
    const result = validation();
    if (!result.isValid && result.error) {
      errors.push(result.error);
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}
