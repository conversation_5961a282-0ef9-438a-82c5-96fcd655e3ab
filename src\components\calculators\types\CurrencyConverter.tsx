"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

interface CurrencyResult {
  fromAmount: number;
  toAmount: number;
  exchangeRate: number;
  fromCurrency: string;
  toCurrency: string;
  lastUpdated: string;
  popularConversions: Array<{
    currency: string;
    amount: number;
    symbol: string;
  }>;
}

export default function CurrencyConverter() {
  const [amount, setAmount] = useState<number>(100);
  const [fromCurrency, setFromCurrency] = useState<string>("USD");
  const [toCurrency, setToCurrency] = useState<string>("EUR");
  const [result, setResult] = useState<CurrencyResult | null>(null);

  // Static exchange rates (in a real app, these would come from an API)
  const exchangeRates: { [key: string]: { [key: string]: number } } = {
    USD: {
      EUR: 0.85,
      GBP: 0.73,
      JPY: 110.0,
      CAD: 1.25,
      AUD: 1.35,
      CHF: 0.92,
      CNY: 6.45,
      INR: 74.5,
      BRL: 5.2,
      KRW: 1180.0,
      MXN: 20.1,
      SGD: 1.35,
      HKD: 7.8,
      NOK: 8.6,
      SEK: 8.9,
      DKK: 6.3,
      PLN: 3.9,
      CZK: 21.5,
      HUF: 295.0,
      RUB: 73.5
    }
  };

  const currencies = [
    { code: "USD", name: "US Dollar", symbol: "$", flag: "🇺🇸" },
    { code: "EUR", name: "Euro", symbol: "€", flag: "🇪🇺" },
    { code: "GBP", name: "British Pound", symbol: "£", flag: "🇬🇧" },
    { code: "JPY", name: "Japanese Yen", symbol: "¥", flag: "🇯🇵" },
    { code: "CAD", name: "Canadian Dollar", symbol: "C$", flag: "🇨🇦" },
    { code: "AUD", name: "Australian Dollar", symbol: "A$", flag: "🇦🇺" },
    { code: "CHF", name: "Swiss Franc", symbol: "CHF", flag: "🇨🇭" },
    { code: "CNY", name: "Chinese Yuan", symbol: "¥", flag: "🇨🇳" },
    { code: "INR", name: "Indian Rupee", symbol: "₹", flag: "🇮🇳" },
    { code: "BRL", name: "Brazilian Real", symbol: "R$", flag: "🇧🇷" },
    { code: "KRW", name: "South Korean Won", symbol: "₩", flag: "🇰🇷" },
    { code: "MXN", name: "Mexican Peso", symbol: "$", flag: "🇲🇽" },
    { code: "SGD", name: "Singapore Dollar", symbol: "S$", flag: "🇸🇬" },
    { code: "HKD", name: "Hong Kong Dollar", symbol: "HK$", flag: "🇭🇰" },
    { code: "NOK", name: "Norwegian Krone", symbol: "kr", flag: "🇳🇴" },
    { code: "SEK", name: "Swedish Krona", symbol: "kr", flag: "🇸🇪" },
    { code: "DKK", name: "Danish Krone", symbol: "kr", flag: "🇩🇰" },
    { code: "PLN", name: "Polish Zloty", symbol: "zł", flag: "🇵🇱" },
    { code: "CZK", name: "Czech Koruna", symbol: "Kč", flag: "🇨🇿" },
    { code: "HUF", name: "Hungarian Forint", symbol: "Ft", flag: "🇭🇺" },
    { code: "RUB", name: "Russian Ruble", symbol: "₽", flag: "🇷🇺" }
  ];

  const getExchangeRate = (from: string, to: string): number => {
    if (from === to) return 1;
    
    // If we have direct rate from USD
    if (from === "USD" && exchangeRates.USD[to]) {
      return exchangeRates.USD[to];
    }
    
    // If converting to USD
    if (to === "USD" && exchangeRates.USD[from]) {
      return 1 / exchangeRates.USD[from];
    }
    
    // Cross-currency conversion via USD
    if (exchangeRates.USD[from] && exchangeRates.USD[to]) {
      return exchangeRates.USD[to] / exchangeRates.USD[from];
    }
    
    return 1; // Fallback
  };

  const convertCurrency = () => {
    const rate = getExchangeRate(fromCurrency, toCurrency);
    const convertedAmount = amount * rate;
    
    // Generate popular conversions
    const popularCurrencies = ["USD", "EUR", "GBP", "JPY", "CAD"];
    const popularConversions = popularCurrencies
      .filter(curr => curr !== fromCurrency)
      .map(curr => {
        const conversionRate = getExchangeRate(fromCurrency, curr);
        const currencyInfo = currencies.find(c => c.code === curr);
        return {
          currency: curr,
          amount: amount * conversionRate,
          symbol: currencyInfo?.symbol || curr
        };
      });

    setResult({
      fromAmount: amount,
      toAmount: convertedAmount,
      exchangeRate: rate,
      fromCurrency,
      toCurrency,
      lastUpdated: new Date().toLocaleString(),
      popularConversions
    });
  };

  const swapCurrencies = () => {
    const temp = fromCurrency;
    setFromCurrency(toCurrency);
    setToCurrency(temp);
  };

  const reset = () => {
    setAmount(100);
    setFromCurrency("USD");
    setToCurrency("EUR");
    setResult(null);
  };

  const formatCurrency = (amount: number, currencyCode: string): string => {
    const currency = currencies.find(c => c.code === currencyCode);
    const symbol = currency?.symbol || currencyCode;
    
    // Format based on currency
    if (currencyCode === "JPY" || currencyCode === "KRW") {
      return `${symbol}${Math.round(amount).toLocaleString()}`;
    }
    
    return `${symbol}${amount.toFixed(2)}`;
  };

  const getCurrencyFlag = (code: string): string => {
    return currencies.find(c => c.code === code)?.flag || "💱";
  };

  const getMarketStatus = (): { status: string; color: string; message: string } => {
    const now = new Date();
    const hour = now.getUTCHours();
    
    // Forex market is open 24/5, closed on weekends
    const isWeekend = now.getUTCDay() === 0 || now.getUTCDay() === 6;
    
    if (isWeekend) {
      return {
        status: "Closed",
        color: "red",
        message: "Forex markets are closed on weekends"
      };
    }
    
    // Major trading sessions
    if ((hour >= 22 && hour <= 23) || (hour >= 0 && hour <= 9)) {
      return {
        status: "Active",
        color: "green",
        message: "Asian/European trading session"
      };
    } else if (hour >= 13 && hour <= 22) {
      return {
        status: "Active",
        color: "green",
        message: "US trading session"
      };
    } else {
      return {
        status: "Low Activity",
        color: "yellow",
        message: "Between major trading sessions"
      };
    }
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Currency Converter</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Input Section */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="amount">Amount</Label>
              <Input
                id="amount"
                type="number"
                value={amount}
                onChange={(e) => setAmount(parseFloat(e.target.value) || 0)}
                min="0"
                step="0.01"
              />
            </div>

            <div>
              <Label htmlFor="from-currency">From</Label>
              <Select value={fromCurrency} onValueChange={setFromCurrency}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {currencies.map((currency) => (
                    <SelectItem key={currency.code} value={currency.code}>
                      {currency.flag} {currency.code} - {currency.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="to-currency">To</Label>
              <Select value={toCurrency} onValueChange={setToCurrency}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {currencies.map((currency) => (
                    <SelectItem key={currency.code} value={currency.code}>
                      {currency.flag} {currency.code} - {currency.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-4">
            <Button onClick={convertCurrency} className="flex-1">
              Convert Currency
            </Button>
            <Button onClick={swapCurrencies} variant="outline">
              Swap
            </Button>
            <Button onClick={reset} variant="outline">
              Reset
            </Button>
          </div>

          {/* Market Status */}
          <Card className={`bg-${getMarketStatus().color}-50 dark:bg-${getMarketStatus().color}-900/20`}>
            <CardContent className="pt-6">
              <div className="text-center space-y-2">
                <div className="text-sm text-muted-foreground">Forex Market Status</div>
                <div className={`text-lg font-bold text-${getMarketStatus().color}-600 dark:text-${getMarketStatus().color}-400`}>
                  {getMarketStatus().status}
                </div>
                <div className="text-sm">{getMarketStatus().message}</div>
              </div>
            </CardContent>
          </Card>

          {/* Results */}
          {result && (
            <div className="space-y-6">
              {/* Main Conversion Result */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card className="bg-blue-50 dark:bg-blue-900/20">
                  <CardContent className="pt-6">
                    <div className="text-center space-y-2">
                      <div className="text-sm text-muted-foreground">From</div>
                      <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                        {formatCurrency(result.fromAmount, result.fromCurrency)}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {getCurrencyFlag(result.fromCurrency)} {result.fromCurrency}
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-green-50 dark:bg-green-900/20">
                  <CardContent className="pt-6">
                    <div className="text-center space-y-2">
                      <div className="text-sm text-muted-foreground">To</div>
                      <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                        {formatCurrency(result.toAmount, result.toCurrency)}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {getCurrencyFlag(result.toCurrency)} {result.toCurrency}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Exchange Rate Info */}
              <Card>
                <CardContent className="pt-6">
                  <div className="text-center space-y-2">
                    <div className="text-sm text-muted-foreground">Exchange Rate</div>
                    <div className="text-xl font-bold">
                      1 {result.fromCurrency} = {result.exchangeRate.toFixed(4)} {result.toCurrency}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      Last updated: {result.lastUpdated}
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Popular Conversions */}
              <Card>
                <CardHeader>
                  <CardTitle>Popular Conversions</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    {result.popularConversions.map((conversion, index) => (
                      <div key={index} className="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg text-center">
                        <div className="text-sm text-muted-foreground">{conversion.currency}</div>
                        <div className="font-bold">
                          {formatCurrency(conversion.amount, conversion.currency)}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Historical Context */}
              <Card>
                <CardHeader>
                  <CardTitle>Exchange Rate Context</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div className="space-y-2">
                      <div><strong>Rate Type:</strong> Mid-market rate</div>
                      <div><strong>Spread:</strong> Banks typically add 2-4% markup</div>
                      <div><strong>Best for:</strong> Reference and planning</div>
                    </div>
                    <div className="space-y-2">
                      <div><strong>Volatility:</strong> Rates change constantly</div>
                      <div><strong>Factors:</strong> Economic data, politics, trade</div>
                      <div><strong>Update:</strong> Real rates update every few seconds</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* Tips */}
          <div className="p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
            <h3 className="font-semibold mb-2">Currency Exchange Tips</h3>
            <ul className="text-sm space-y-1">
              <li>• Exchange rates fluctuate constantly during market hours</li>
              <li>• Banks and money changers add fees and spreads to rates</li>
              <li>• Major currencies (USD, EUR, GBP, JPY) have tighter spreads</li>
              <li>• Avoid airport currency exchanges - they have poor rates</li>
              <li>• Consider using credit cards for better exchange rates abroad</li>
              <li>• These are reference rates - actual rates may vary</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
