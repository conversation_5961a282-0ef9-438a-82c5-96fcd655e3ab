"use client";

import { ReactNode } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import { UserRole } from "@/lib/auth/roles";

interface ProtectedRouteProps {
  children: ReactNode;
  requiredRole: UserRole;
  fallbackUrl?: string;
  loadingComponent?: ReactNode;
  unauthorizedComponent?: ReactNode;
}

/**
 * Component to protect routes based on user authentication and role
 * Uses NextAuth for authentication state
 */
export function ProtectedRoute({ 
  children, 
  requiredRole, 
  fallbackUrl = "/login",
  loadingComponent,
  unauthorizedComponent
}: ProtectedRouteProps) {
  const { data: session, status } = useSession();
  const router = useRouter();

  useEffect(() => {
    if (status === "loading") return; // Still loading

    if (!session) {
      // Not authenticated, redirect to login
      const currentPath = window.location.pathname;
      router.push(`${fallbackUrl}?callbackUrl=${encodeURIComponent(currentPath)}`);
      return;
    }

    // Check role requirements
    if (requiredRole === UserRole.ADMIN && session.user.role !== "admin") {
      // User doesn't have required admin role
      router.push("/");
      return;
    }
  }, [session, status, requiredRole, router, fallbackUrl]);

  // Show loading while checking auth
  if (status === "loading") {
    return loadingComponent || (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  // Don't render children if not authenticated
  if (!session) {
    return null;
  }

  // Don't render children if user doesn't have required role
  if (requiredRole === UserRole.ADMIN && session.user.role !== "admin") {
    return unauthorizedComponent || (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600 mb-4">Unauthorized</h1>
          <p className="text-gray-600">You don&rsquo;t have permission to access this page.</p>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}
