import cron from 'node-cron';
import { DownloadArchiver } from '@/lib/archival/downloadArchiver';

/**
 * Archival Scheduler
 * Manages automated download archival using cron jobs
 */
export class ArchivalScheduler {
  private static instance: ArchivalScheduler;
  private cronJob: cron.ScheduledTask | null = null;
  private isRunning = false;
  private lastRun: Date | null = null;
  private nextRun: Date | null = null;

  private constructor() {}

  /**
   * Get singleton instance
   */
  static getInstance(): ArchivalScheduler {
    if (!ArchivalScheduler.instance) {
      ArchivalScheduler.instance = new ArchivalScheduler();
    }
    return ArchivalScheduler.instance;
  }

  /**
   * Start the archival cron job
   * Runs every 24 hours at 2:00 AM
   */
  start(): void {
    if (this.cronJob) {
      console.log('[ArchivalScheduler] Cron job already running');
      return;
    }

    // Check if auto-archival is enabled
    if (process.env.ENABLE_AUTO_ARCHIVAL !== 'true') {
      console.log('[ArchivalScheduler] Auto-archival is disabled. Set ENABLE_AUTO_ARCHIVAL=true to enable.');
      return;
    }

    // Schedule: Every day at 2:00 AM
    // Format: second minute hour day month dayOfWeek
    const cronExpression = '0 0 2 * * *';
    
    this.cronJob = cron.schedule(cronExpression, async () => {
      await this.runArchival();
    }, {
      scheduled: false,
      timezone: process.env.TIMEZONE || 'UTC'
    });

    this.cronJob.start();
    this.isRunning = true;
    this.updateNextRunTime();

    console.log('[ArchivalScheduler] Archival cron job started - runs daily at 2:00 AM');
    console.log(`[ArchivalScheduler] Next run scheduled for: ${this.nextRun?.toISOString()}`);
  }

  /**
   * Stop the archival cron job
   */
  stop(): void {
    if (this.cronJob) {
      this.cronJob.stop();
      this.cronJob.destroy();
      this.cronJob = null;
      this.isRunning = false;
      this.nextRun = null;
      console.log('[ArchivalScheduler] Archival cron job stopped');
    }
  }

  /**
   * Restart the archival cron job
   */
  restart(): void {
    this.stop();
    this.start();
  }

  /**
   * Run archival process manually
   */
  async runArchival(): Promise<void> {
    if (this.isRunning && this.cronJob) {
      console.log('[ArchivalScheduler] Starting scheduled archival process...');
      
      try {
        const archiver = new DownloadArchiver();
        const result = await archiver.archiveDownloads();
        
        this.lastRun = new Date();
        this.updateNextRunTime();

        if (result.success) {
          console.log(`[ArchivalScheduler] Scheduled archival completed successfully`);
          console.log(`[ArchivalScheduler] Records archived: ${result.recordsArchived}`);
          console.log(`[ArchivalScheduler] Execution time: ${result.executionTime}ms`);
          
          if (result.filePath) {
            console.log(`[ArchivalScheduler] Archive file: ${result.filePath}`);
          }
        } else {
          console.error(`[ArchivalScheduler] Scheduled archival failed: ${result.error}`);
        }

        // Send notification if configured
        await this.sendNotification(result);

      } catch (error) {
        console.error('[ArchivalScheduler] Archival process error:', error);
        this.lastRun = new Date();
        this.updateNextRunTime();
      }
    }
  }

  /**
   * Get scheduler status
   */
  getStatus(): {
    isRunning: boolean;
    lastRun: Date | null;
    nextRun: Date | null;
    cronExpression: string;
    timezone: string;
    autoArchivalEnabled: boolean;
  } {
    return {
      isRunning: this.isRunning,
      lastRun: this.lastRun,
      nextRun: this.nextRun,
      cronExpression: '0 0 2 * * *', // Daily at 2:00 AM
      timezone: process.env.TIMEZONE || 'UTC',
      autoArchivalEnabled: process.env.ENABLE_AUTO_ARCHIVAL === 'true'
    };
  }

  /**
   * Update next run time calculation
   */
  private updateNextRunTime(): void {
    if (!this.isRunning) {
      this.nextRun = null;
      return;
    }

    // Calculate next 2:00 AM
    const now = new Date();
    const next = new Date(now);
    next.setHours(2, 0, 0, 0);

    // If it's already past 2:00 AM today, schedule for tomorrow
    if (now.getHours() >= 2) {
      next.setDate(next.getDate() + 1);
    }

    this.nextRun = next;
  }

  /**
   * Send notification about archival results (if configured)
   */
  private async sendNotification(result: any): Promise<void> {
    try {
      // Check if notifications are enabled
      const notificationUrl = process.env.ARCHIVAL_NOTIFICATION_WEBHOOK;
      if (!notificationUrl) {
        return;
      }

      const notification = {
        timestamp: new Date().toISOString(),
        success: result.success,
        recordsArchived: result.recordsArchived,
        filePath: result.filePath,
        executionTime: result.executionTime,
        error: result.error || null
      };

      const response = await fetch(notificationUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(notification),
      });

      if (!response.ok) {
        console.warn('[ArchivalScheduler] Failed to send notification:', response.statusText);
      } else {
        console.log('[ArchivalScheduler] Notification sent successfully');
      }

    } catch (error) {
      console.warn('[ArchivalScheduler] Notification error:', error);
    }
  }
}

/**
 * Initialize and start the archival scheduler
 * Call this function when the application starts
 */
export function initializeArchivalScheduler(): void {
  const scheduler = ArchivalScheduler.getInstance();
  scheduler.start();
}

/**
 * Get the archival scheduler instance
 */
export function getArchivalScheduler(): ArchivalScheduler {
  return ArchivalScheduler.getInstance();
}
