"use client";

import React from "react";
import { motion } from "framer-motion";
import { Card, CardContent, CardHeader } from "@/components/ui/card";

export function ToolSkeleton() {
  return (
    <div className="container mx-auto px-4 py-8">
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5 }}
        className="max-w-4xl mx-auto"
      >
        {/* Header Skeleton */}
        <div className="text-center mb-8">
          <div className="h-8 bg-muted rounded-md w-80 mx-auto mb-4 animate-pulse" />
          <div className="h-4 bg-muted rounded-md w-96 mx-auto animate-pulse" />
        </div>

        {/* Tool Card Skeleton */}
        <Card className="mb-8">
          <CardHeader>
            <div className="h-6 bg-muted rounded-md w-56 mb-2 animate-pulse" />
            <div className="h-4 bg-muted rounded-md w-80 animate-pulse" />
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Steps Skeleton */}
            <div className="space-y-3">
              <div className="h-5 bg-muted rounded-md w-32 animate-pulse" />
              <div className="space-y-2">
                {[...Array(3)].map((_, i) => (
                  <div key={i} className="flex items-center space-x-3">
                    <div className="h-6 w-6 bg-muted rounded-full animate-pulse" />
                    <div className="h-4 bg-muted rounded-md flex-1 animate-pulse" />
                  </div>
                ))}
              </div>
            </div>

            {/* File Upload Area Skeleton */}
            <div className="border-2 border-dashed border-muted rounded-lg p-8">
              <div className="text-center space-y-4">
                <div className="h-12 w-12 bg-muted rounded-full mx-auto animate-pulse" />
                <div className="h-4 bg-muted rounded-md w-48 mx-auto animate-pulse" />
                <div className="h-4 bg-muted rounded-md w-32 mx-auto animate-pulse" />
                <div className="h-10 bg-muted rounded-md w-32 mx-auto animate-pulse" />
              </div>
            </div>

            {/* Progress Bar Skeleton */}
            <div className="space-y-2">
              <div className="h-2 bg-muted rounded-full w-full animate-pulse" />
              <div className="h-4 bg-muted rounded-md w-24 mx-auto animate-pulse" />
            </div>
          </CardContent>
        </Card>

        {/* About Section Skeleton */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <div className="h-5 bg-muted rounded-md w-40 animate-pulse" />
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {[...Array(5)].map((_, i) => (
                  <div key={i} className="h-4 bg-muted rounded-md w-full animate-pulse" />
                ))}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <div className="h-5 bg-muted rounded-md w-32 animate-pulse" />
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {[...Array(4)].map((_, i) => (
                  <div key={i} className="h-4 bg-muted rounded-md w-full animate-pulse" />
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Loading Text */}
        <div className="text-center mt-8">
          <motion.div
            animate={{ opacity: [0.5, 1, 0.5] }}
            transition={{ duration: 1.5, repeat: Infinity }}
            className="text-muted-foreground"
          >
            Loading tool...
          </motion.div>
        </div>
      </motion.div>
    </div>
  );
}

export default ToolSkeleton;
