"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

interface CarLoanResult {
  monthlyPayment: number;
  totalPayment: number;
  totalInterest: number;
  payoffDate: string;
  amortizationSchedule: Array<{
    month: number;
    payment: number;
    principal: number;
    interest: number;
    balance: number;
  }>;
}

export default function CarLoanCalculator() {
  const [carPrice, setCarPrice] = useState<number>(25000);
  const [downPayment, setDownPayment] = useState<number>(5000);
  const [tradeInValue, setTradeInValue] = useState<number>(0);
  const [loanTerm, setLoanTerm] = useState<number>(60);
  const [interestRate, setInterestRate] = useState<number>(4.5);
  const [salesTax, setSalesTax] = useState<number>(8.5);
  const [fees, setFees] = useState<number>(1500);
  const [result, setResult] = useState<CarLoanResult | null>(null);

  const calculateCarLoan = () => {
    // Calculate total amount financed
    const taxAmount = (carPrice * salesTax) / 100;
    const totalCost = carPrice + taxAmount + fees;
    const loanAmount = totalCost - downPayment - tradeInValue;

    if (loanAmount <= 0) {
      alert("Loan amount must be positive. Increase car price or reduce down payment/trade-in.");
      return;
    }

    // Calculate monthly payment using loan formula
    const monthlyRate = interestRate / 100 / 12;
    const numPayments = loanTerm;
    
    let monthlyPayment: number;
    if (monthlyRate === 0) {
      monthlyPayment = loanAmount / numPayments;
    } else {
      monthlyPayment = loanAmount * (monthlyRate * Math.pow(1 + monthlyRate, numPayments)) / 
                     (Math.pow(1 + monthlyRate, numPayments) - 1);
    }

    const totalPayment = monthlyPayment * numPayments;
    const totalInterest = totalPayment - loanAmount;

    // Calculate payoff date
    const today = new Date();
    const payoffDate = new Date(today.getFullYear(), today.getMonth() + loanTerm, today.getDate());

    // Generate amortization schedule (first 12 months)
    const amortizationSchedule: Array<{
      month: number;
      payment: number;
      principal: number;
      interest: number;
      balance: number;
    }> = [];

    let remainingBalance = loanAmount;

    for (let month = 1; month <= Math.min(12, numPayments); month++) {
      const interestPayment = remainingBalance * monthlyRate;
      const principalPayment = monthlyPayment - interestPayment;
      remainingBalance -= principalPayment;

      amortizationSchedule.push({
        month,
        payment: monthlyPayment,
        principal: principalPayment,
        interest: interestPayment,
        balance: Math.max(0, remainingBalance)
      });
    }

    setResult({
      monthlyPayment,
      totalPayment,
      totalInterest,
      payoffDate: payoffDate.toLocaleDateString(),
      amortizationSchedule
    });
  };

  const reset = () => {
    setCarPrice(25000);
    setDownPayment(5000);
    setTradeInValue(0);
    setLoanTerm(60);
    setInterestRate(4.5);
    setSalesTax(8.5);
    setFees(1500);
    setResult(null);
  };

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount);
  };

  const getLoanAmount = (): number => {
    const taxAmount = (carPrice * salesTax) / 100;
    const totalCost = carPrice + taxAmount + fees;
    return totalCost - downPayment - tradeInValue;
  };

  const getTotalCost = (): number => {
    const taxAmount = (carPrice * salesTax) / 100;
    return carPrice + taxAmount + fees;
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Car Loan Calculator</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Input Section */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div>
                <Label htmlFor="car-price">Car Price</Label>
                <Input
                  id="car-price"
                  type="number"
                  value={carPrice}
                  onChange={(e) => setCarPrice(parseFloat(e.target.value) || 0)}
                  min="0"
                  step="500"
                />
              </div>

              <div>
                <Label htmlFor="down-payment">Down Payment</Label>
                <Input
                  id="down-payment"
                  type="number"
                  value={downPayment}
                  onChange={(e) => setDownPayment(parseFloat(e.target.value) || 0)}
                  min="0"
                  step="500"
                />
              </div>

              <div>
                <Label htmlFor="trade-in">Trade-in Value</Label>
                <Input
                  id="trade-in"
                  type="number"
                  value={tradeInValue}
                  onChange={(e) => setTradeInValue(parseFloat(e.target.value) || 0)}
                  min="0"
                  step="500"
                />
              </div>

              <div>
                <Label htmlFor="sales-tax">Sales Tax (%)</Label>
                <Input
                  id="sales-tax"
                  type="number"
                  value={salesTax}
                  onChange={(e) => setSalesTax(parseFloat(e.target.value) || 0)}
                  min="0"
                  max="20"
                  step="0.1"
                />
              </div>
            </div>

            <div className="space-y-4">
              <div>
                <Label htmlFor="loan-term">Loan Term (Months)</Label>
                <Select value={loanTerm.toString()} onValueChange={(value) => setLoanTerm(parseInt(value))}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="36">36 months (3 years)</SelectItem>
                    <SelectItem value="48">48 months (4 years)</SelectItem>
                    <SelectItem value="60">60 months (5 years)</SelectItem>
                    <SelectItem value="72">72 months (6 years)</SelectItem>
                    <SelectItem value="84">84 months (7 years)</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="interest-rate">Interest Rate (% APR)</Label>
                <Input
                  id="interest-rate"
                  type="number"
                  value={interestRate}
                  onChange={(e) => setInterestRate(parseFloat(e.target.value) || 0)}
                  min="0"
                  max="30"
                  step="0.1"
                />
              </div>

              <div>
                <Label htmlFor="fees">Fees & Documentation</Label>
                <Input
                  id="fees"
                  type="number"
                  value={fees}
                  onChange={(e) => setFees(parseFloat(e.target.value) || 0)}
                  min="0"
                  step="100"
                />
              </div>

              <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <h3 className="font-semibold mb-2">Loan Summary</h3>
                <div className="space-y-1 text-sm">
                  <div className="flex justify-between">
                    <span>Car Price:</span>
                    <span>{formatCurrency(carPrice)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Sales Tax:</span>
                    <span>{formatCurrency((carPrice * salesTax) / 100)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Fees:</span>
                    <span>{formatCurrency(fees)}</span>
                  </div>
                  <div className="flex justify-between border-t pt-1">
                    <span>Total Cost:</span>
                    <span className="font-semibold">{formatCurrency(getTotalCost())}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Down Payment:</span>
                    <span>-{formatCurrency(downPayment)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Trade-in:</span>
                    <span>-{formatCurrency(tradeInValue)}</span>
                  </div>
                  <div className="flex justify-between border-t pt-1">
                    <span>Loan Amount:</span>
                    <span className="font-semibold">{formatCurrency(getLoanAmount())}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Calculate Button */}
          <div className="flex gap-4">
            <Button onClick={calculateCarLoan} className="flex-1">
              Calculate Car Loan
            </Button>
            <Button onClick={reset} variant="outline">
              Reset
            </Button>
          </div>

          {/* Results */}
          {result && (
            <div className="space-y-6">
              {/* Summary Cards */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card className="bg-green-50 dark:bg-green-900/20">
                  <CardContent className="pt-6">
                    <div className="text-center space-y-2">
                      <div className="text-sm text-muted-foreground">Monthly Payment</div>
                      <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                        {formatCurrency(result.monthlyPayment)}
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-blue-50 dark:bg-blue-900/20">
                  <CardContent className="pt-6">
                    <div className="text-center space-y-2">
                      <div className="text-sm text-muted-foreground">Total Payment</div>
                      <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                        {formatCurrency(result.totalPayment)}
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-red-50 dark:bg-red-900/20">
                  <CardContent className="pt-6">
                    <div className="text-center space-y-2">
                      <div className="text-sm text-muted-foreground">Total Interest</div>
                      <div className="text-2xl font-bold text-red-600 dark:text-red-400">
                        {formatCurrency(result.totalInterest)}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Additional Info */}
              <Card>
                <CardContent className="pt-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-center">
                    <div>
                      <div className="text-sm text-muted-foreground">Loan Payoff Date</div>
                      <div className="text-xl font-semibold">{result.payoffDate}</div>
                    </div>
                    <div>
                      <div className="text-sm text-muted-foreground">Interest vs Principal</div>
                      <div className="text-xl font-semibold">
                        {((result.totalInterest / getLoanAmount()) * 100).toFixed(1)}%
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Amortization Schedule */}
              <Card>
                <CardHeader>
                  <CardTitle>First Year Payment Breakdown</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="overflow-x-auto">
                    <table className="w-full text-sm">
                      <thead>
                        <tr className="border-b">
                          <th className="text-left p-2">Month</th>
                          <th className="text-right p-2">Payment</th>
                          <th className="text-right p-2">Principal</th>
                          <th className="text-right p-2">Interest</th>
                          <th className="text-right p-2">Balance</th>
                        </tr>
                      </thead>
                      <tbody>
                        {result.amortizationSchedule.map((payment, index) => (
                          <tr key={index} className="border-b">
                            <td className="p-2">{payment.month}</td>
                            <td className="text-right p-2">{formatCurrency(payment.payment)}</td>
                            <td className="text-right p-2">{formatCurrency(payment.principal)}</td>
                            <td className="text-right p-2">{formatCurrency(payment.interest)}</td>
                            <td className="text-right p-2 font-semibold">{formatCurrency(payment.balance)}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* Tips */}
          <div className="p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
            <h3 className="font-semibold mb-2">Car Buying Tips</h3>
            <ul className="text-sm space-y-1">
              <li>• Larger down payments reduce monthly payments and total interest</li>
              <li>• Shorter loan terms mean higher monthly payments but less total interest</li>
              <li>• Shop around for the best interest rates from different lenders</li>
              <li>• Consider certified pre-owned vehicles for better value</li>
              <li>• Factor in insurance, maintenance, and fuel costs</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
