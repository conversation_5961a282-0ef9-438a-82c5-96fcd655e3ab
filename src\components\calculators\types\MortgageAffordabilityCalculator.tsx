"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

interface AffordabilityResult {
  maxHomePrice: number;
  maxLoanAmount: number;
  monthlyPayment: number;
  totalMonthlyHousing: number;
  debtToIncomeRatio: number;
  frontEndRatio: number;
  backEndRatio: number;
  recommendedDownPayment: number;
}

export default function MortgageAffordabilityCalculator() {
  const [annualIncome, setAnnualIncome] = useState<number>(80000);
  const [monthlyDebts, setMonthlyDebts] = useState<number>(500);
  const [downPaymentPercent, setDownPaymentPercent] = useState<number>(20);
  const [interestRate, setInterestRate] = useState<number>(6.5);
  const [loanTerm, setLoanTerm] = useState<number>(30);
  const [propertyTax, setPropertyTax] = useState<number>(1.2);
  const [homeInsurance, setHomeInsurance] = useState<number>(0.5);
  const [pmi, setPmi] = useState<number>(0.5);
  const [hoaFees, setHoaFees] = useState<number>(0);
  const [result, setResult] = useState<AffordabilityResult | null>(null);

  const calculateAffordability = () => {
    const monthlyIncome = annualIncome / 12;
    
    // Standard ratios: 28% front-end, 36% back-end
    const maxFrontEndPayment = monthlyIncome * 0.28;
    const maxBackEndPayment = monthlyIncome * 0.36;
    const maxHousingPayment = Math.min(maxFrontEndPayment, maxBackEndPayment - monthlyDebts);

    // Calculate monthly rates
    const monthlyRate = interestRate / 100 / 12;
    const numPayments = loanTerm * 12;

    // Estimate monthly property costs as percentage of home value
    const monthlyPropertyTaxRate = propertyTax / 100 / 12;
    const monthlyInsuranceRate = homeInsurance / 100 / 12;
    const monthlyPmiRate = downPaymentPercent < 20 ? pmi / 100 / 12 : 0;

    // Calculate maximum home price using iterative approach
    let homePrice = 100000;
    let increment = 50000;
    let bestPrice = 0;

    for (let i = 0; i < 20; i++) {
      const loanAmount = homePrice * (1 - downPaymentPercent / 100);
      
      // Calculate monthly payment components
      let monthlyPrincipalInterest: number;
      if (monthlyRate === 0) {
        monthlyPrincipalInterest = loanAmount / numPayments;
      } else {
        monthlyPrincipalInterest = loanAmount * 
          (monthlyRate * Math.pow(1 + monthlyRate, numPayments)) / 
          (Math.pow(1 + monthlyRate, numPayments) - 1);
      }

      const monthlyPropertyTax = homePrice * monthlyPropertyTaxRate;
      const monthlyInsurance = homePrice * monthlyInsuranceRate;
      const monthlyPmiPayment = loanAmount * monthlyPmiRate;
      
      const totalMonthlyPayment = monthlyPrincipalInterest + monthlyPropertyTax + 
                                 monthlyInsurance + monthlyPmiPayment + hoaFees;

      if (totalMonthlyPayment <= maxHousingPayment) {
        bestPrice = homePrice;
        homePrice += increment;
      } else {
        homePrice -= increment;
        increment /= 2;
        if (increment < 100) break;
      }
    }

    // Calculate final results based on best price
    const finalLoanAmount = bestPrice * (1 - downPaymentPercent / 100);
    const finalDownPayment = bestPrice * (downPaymentPercent / 100);
    
    let finalMonthlyPI: number;
    if (monthlyRate === 0) {
      finalMonthlyPI = finalLoanAmount / numPayments;
    } else {
      finalMonthlyPI = finalLoanAmount * 
        (monthlyRate * Math.pow(1 + monthlyRate, numPayments)) / 
        (Math.pow(1 + monthlyRate, numPayments) - 1);
    }

    const finalPropertyTax = bestPrice * monthlyPropertyTaxRate;
    const finalInsurance = bestPrice * monthlyInsuranceRate;
    const finalPmi = finalLoanAmount * monthlyPmiRate;
    const finalTotalMonthly = finalMonthlyPI + finalPropertyTax + finalInsurance + finalPmi + hoaFees;

    const frontEndRatio = (finalTotalMonthly / monthlyIncome) * 100;
    const backEndRatio = ((finalTotalMonthly + monthlyDebts) / monthlyIncome) * 100;

    setResult({
      maxHomePrice: bestPrice,
      maxLoanAmount: finalLoanAmount,
      monthlyPayment: finalMonthlyPI,
      totalMonthlyHousing: finalTotalMonthly,
      debtToIncomeRatio: (monthlyDebts / monthlyIncome) * 100,
      frontEndRatio,
      backEndRatio,
      recommendedDownPayment: finalDownPayment
    });
  };

  const reset = () => {
    setAnnualIncome(80000);
    setMonthlyDebts(500);
    setDownPaymentPercent(20);
    setInterestRate(6.5);
    setLoanTerm(30);
    setPropertyTax(1.2);
    setHomeInsurance(0.5);
    setPmi(0.5);
    setHoaFees(0);
    setResult(null);
  };

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const getAffordabilityStatus = (): { status: string; color: string; message: string } => {
    if (!result) return { status: "Unknown", color: "gray", message: "" };
    
    if (result.backEndRatio <= 28) {
      return { 
        status: "Excellent", 
        color: "green", 
        message: "You have excellent affordability with low debt ratios" 
      };
    } else if (result.backEndRatio <= 36) {
      return { 
        status: "Good", 
        color: "blue", 
        message: "You meet standard lending requirements" 
      };
    } else if (result.backEndRatio <= 43) {
      return { 
        status: "Marginal", 
        color: "yellow", 
        message: "You may qualify but consider reducing debt first" 
      };
    } else {
      return { 
        status: "Challenging", 
        color: "red", 
        message: "Consider increasing income or reducing debt before buying" 
      };
    }
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Mortgage Affordability Calculator</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Input Section */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div>
                <Label htmlFor="annual-income">Annual Gross Income</Label>
                <Input
                  id="annual-income"
                  type="number"
                  value={annualIncome}
                  onChange={(e) => setAnnualIncome(parseFloat(e.target.value) || 0)}
                  min="0"
                  step="1000"
                />
              </div>

              <div>
                <Label htmlFor="monthly-debts">Monthly Debt Payments</Label>
                <Input
                  id="monthly-debts"
                  type="number"
                  value={monthlyDebts}
                  onChange={(e) => setMonthlyDebts(parseFloat(e.target.value) || 0)}
                  min="0"
                  step="50"
                />
              </div>

              <div>
                <Label htmlFor="down-payment">Down Payment (%)</Label>
                <Input
                  id="down-payment"
                  type="number"
                  value={downPaymentPercent}
                  onChange={(e) => setDownPaymentPercent(parseFloat(e.target.value) || 0)}
                  min="0"
                  max="50"
                  step="1"
                />
              </div>

              <div>
                <Label htmlFor="interest-rate">Interest Rate (%)</Label>
                <Input
                  id="interest-rate"
                  type="number"
                  value={interestRate}
                  onChange={(e) => setInterestRate(parseFloat(e.target.value) || 0)}
                  min="0"
                  max="15"
                  step="0.1"
                />
              </div>

              <div>
                <Label htmlFor="loan-term">Loan Term (Years)</Label>
                <Input
                  id="loan-term"
                  type="number"
                  value={loanTerm}
                  onChange={(e) => setLoanTerm(parseInt(e.target.value) || 0)}
                  min="10"
                  max="40"
                />
              </div>
            </div>

            <div className="space-y-4">
              <div>
                <Label htmlFor="property-tax">Property Tax Rate (% annually)</Label>
                <Input
                  id="property-tax"
                  type="number"
                  value={propertyTax}
                  onChange={(e) => setPropertyTax(parseFloat(e.target.value) || 0)}
                  min="0"
                  max="5"
                  step="0.1"
                />
              </div>

              <div>
                <Label htmlFor="home-insurance">Home Insurance (% annually)</Label>
                <Input
                  id="home-insurance"
                  type="number"
                  value={homeInsurance}
                  onChange={(e) => setHomeInsurance(parseFloat(e.target.value) || 0)}
                  min="0"
                  max="2"
                  step="0.1"
                />
              </div>

              <div>
                <Label htmlFor="pmi">PMI Rate (% annually)</Label>
                <Input
                  id="pmi"
                  type="number"
                  value={pmi}
                  onChange={(e) => setPmi(parseFloat(e.target.value) || 0)}
                  min="0"
                  max="2"
                  step="0.1"
                />
              </div>

              <div>
                <Label htmlFor="hoa-fees">Monthly HOA Fees</Label>
                <Input
                  id="hoa-fees"
                  type="number"
                  value={hoaFees}
                  onChange={(e) => setHoaFees(parseFloat(e.target.value) || 0)}
                  min="0"
                  step="25"
                />
              </div>

              <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <h3 className="font-semibold mb-2">Income Summary</h3>
                <div className="space-y-1 text-sm">
                  <div className="flex justify-between">
                    <span>Monthly Income:</span>
                    <span>{formatCurrency(annualIncome / 12)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Monthly Debts:</span>
                    <span>{formatCurrency(monthlyDebts)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Available for Housing:</span>
                    <span>{formatCurrency((annualIncome / 12) * 0.28)}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Calculate Button */}
          <div className="flex gap-4">
            <Button onClick={calculateAffordability} className="flex-1">
              Calculate Home Affordability
            </Button>
            <Button onClick={reset} variant="outline">
              Reset
            </Button>
          </div>

          {/* Results */}
          {result && (
            <div className="space-y-6">
              {/* Status Card */}
              <Card className={`bg-${getAffordabilityStatus().color}-50 dark:bg-${getAffordabilityStatus().color}-900/20`}>
                <CardContent className="pt-6">
                  <div className="text-center space-y-2">
                    <div className="text-sm text-muted-foreground">Affordability Status</div>
                    <div className={`text-2xl font-bold text-${getAffordabilityStatus().color}-600 dark:text-${getAffordabilityStatus().color}-400`}>
                      {getAffordabilityStatus().status}
                    </div>
                    <div className="text-sm">{getAffordabilityStatus().message}</div>
                  </div>
                </CardContent>
              </Card>

              {/* Main Results */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card className="bg-green-50 dark:bg-green-900/20">
                  <CardContent className="pt-6">
                    <div className="text-center space-y-2">
                      <div className="text-sm text-muted-foreground">Maximum Home Price</div>
                      <div className="text-3xl font-bold text-green-600 dark:text-green-400">
                        {formatCurrency(result.maxHomePrice)}
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-blue-50 dark:bg-blue-900/20">
                  <CardContent className="pt-6">
                    <div className="text-center space-y-2">
                      <div className="text-sm text-muted-foreground">Required Down Payment</div>
                      <div className="text-3xl font-bold text-blue-600 dark:text-blue-400">
                        {formatCurrency(result.recommendedDownPayment)}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Payment Breakdown */}
              <Card>
                <CardHeader>
                  <CardTitle>Monthly Payment Breakdown</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span>Principal & Interest:</span>
                        <span className="font-semibold">{formatCurrency(result.monthlyPayment)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Property Tax:</span>
                        <span className="font-semibold">{formatCurrency((result.maxHomePrice * propertyTax / 100) / 12)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Home Insurance:</span>
                        <span className="font-semibold">{formatCurrency((result.maxHomePrice * homeInsurance / 100) / 12)}</span>
                      </div>
                      {downPaymentPercent < 20 && (
                        <div className="flex justify-between">
                          <span>PMI:</span>
                          <span className="font-semibold">{formatCurrency((result.maxLoanAmount * pmi / 100) / 12)}</span>
                        </div>
                      )}
                      {hoaFees > 0 && (
                        <div className="flex justify-between">
                          <span>HOA Fees:</span>
                          <span className="font-semibold">{formatCurrency(hoaFees)}</span>
                        </div>
                      )}
                      <div className="flex justify-between border-t pt-2">
                        <span className="font-semibold">Total Monthly Housing:</span>
                        <span className="font-bold">{formatCurrency(result.totalMonthlyHousing)}</span>
                      </div>
                    </div>

                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span>Loan Amount:</span>
                        <span className="font-semibold">{formatCurrency(result.maxLoanAmount)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Front-End Ratio:</span>
                        <span className="font-semibold">{result.frontEndRatio.toFixed(1)}%</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Back-End Ratio:</span>
                        <span className="font-semibold">{result.backEndRatio.toFixed(1)}%</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Current Debt Ratio:</span>
                        <span className="font-semibold">{result.debtToIncomeRatio.toFixed(1)}%</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* Tips */}
          <div className="p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
            <h3 className="font-semibold mb-2">Home Buying Tips</h3>
            <ul className="text-sm space-y-1">
              <li>• Front-end ratio should be ≤28% (housing costs / income)</li>
              <li>• Back-end ratio should be ≤36% (total debt / income)</li>
              <li>• Consider additional costs: maintenance, utilities, moving</li>
              <li>• Get pre-approved before house hunting</li>
              <li>• Save for closing costs (2-5% of home price)</li>
              <li>• Emergency fund should cover 3-6 months of expenses</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
