import { NextRequest, NextResponse } from 'next/server';
import { PDFDocument } from 'pdf-lib';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const files = formData.getAll('files') as File[];

    if (!files || files.length === 0) {
      return NextResponse.json(
        { error: 'No files provided' },
        { status: 400 }
      );
    }

    if (files.length < 2) {
      return NextResponse.json(
        { error: 'At least 2 PDF files are required for merging' },
        { status: 400 }
      );
    }

    // Validate all files are PDFs
    for (const file of files) {
      if (file.type !== 'application/pdf') {
        return NextResponse.json(
          { error: `File "${file.name}" is not a PDF` },
          { status: 400 }
        );
      }
    }

    // Create a new PDF document for merging
    const mergedPdf = await PDFDocument.create();
    
    let totalPages = 0;
    const fileInfo = [];

    // Process each PDF file
    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      const arrayBuffer = await file.arrayBuffer();
      
      try {
        // Load the PDF document
        const pdf = await PDFDocument.load(arrayBuffer);
        const pageCount = pdf.getPageCount();
        
        // Copy all pages from this PDF to the merged PDF
        const copiedPages = await mergedPdf.copyPages(pdf, pdf.getPageIndices());
        
        // Add each copied page to the merged document
        copiedPages.forEach((page) => {
          mergedPdf.addPage(page);
        });

        totalPages += pageCount;
        fileInfo.push({
          name: file.name,
          pages: pageCount,
          size: arrayBuffer.byteLength
        });

      } catch (error) {
        console.error(`Error processing file ${file.name}:`, error);
        return NextResponse.json(
          { error: `Failed to process file "${file.name}". Please ensure it's a valid PDF.` },
          { status: 400 }
        );
      }
    }

    // Save the merged PDF
    const mergedPdfBytes = await mergedPdf.save({
      useObjectStreams: true,
      addDefaultPage: false,
    });

    // Generate filename for merged PDF
    const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
    const mergedFilename = `merged_${files.length}_files_${timestamp}.pdf`;

    // Create response with merged PDF
    const response = new NextResponse(mergedPdfBytes, {
      status: 200,
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `attachment; filename="${mergedFilename}"`,
        'X-Total-Pages': totalPages.toString(),
        'X-Files-Merged': files.length.toString(),
        'X-File-Info': JSON.stringify(fileInfo),
      },
    });

    return response;

  } catch (error) {
    console.error('PDF merge error:', error);
    return NextResponse.json(
      { error: 'Failed to merge PDFs. Please ensure all files are valid PDF documents.' },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json(
    { 
      message: 'PDF Merge API',
      maxFiles: 10,
      maxFileSize: '10MB per file',
      supportedFormat: 'PDF'
    },
    { status: 200 }
  );
}
