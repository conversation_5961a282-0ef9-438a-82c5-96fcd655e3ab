// Accessibility Testing Script for ToolRapter PDF Tools
// Run this in the browser console to test accessibility features

console.log('🔍 Starting Accessibility Testing for ToolRapter...');

/**
 * Test 1: ARIA Attributes and Roles
 */
function testARIAAttributes() {
  console.log('\n📋 Testing ARIA Attributes and Roles...');
  
  const results = {
    alerts: 0,
    status: 0,
    progressbars: 0,
    buttons: 0,
    missingLabels: []
  };
  
  // Check for alert regions
  const alerts = document.querySelectorAll('[role="alert"]');
  results.alerts = alerts.length;
  console.log(`✅ Found ${alerts.length} alert regions`);
  
  // Check for status regions
  const status = document.querySelectorAll('[role="status"]');
  results.status = status.length;
  console.log(`✅ Found ${status.length} status regions`);
  
  // Check for progress bars
  const progressbars = document.querySelectorAll('[role="progressbar"]');
  results.progressbars = progressbars.length;
  console.log(`✅ Found ${progressbars.length} progress bars`);
  
  // Check buttons for accessibility
  const buttons = document.querySelectorAll('button');
  results.buttons = buttons.length;
  
  buttons.forEach((button, index) => {
    const hasAriaLabel = button.hasAttribute('aria-label');
    const hasAriaLabelledBy = button.hasAttribute('aria-labelledby');
    const hasTextContent = button.textContent.trim().length > 0;
    
    if (!hasAriaLabel && !hasAriaLabelledBy && !hasTextContent) {
      results.missingLabels.push(`Button ${index + 1}: Missing accessible label`);
    }
  });
  
  console.log(`✅ Found ${buttons.length} buttons`);
  if (results.missingLabels.length > 0) {
    console.warn('⚠️ Buttons with missing labels:', results.missingLabels);
  } else {
    console.log('✅ All buttons have accessible labels');
  }
  
  return results;
}

/**
 * Test 2: Keyboard Navigation
 */
function testKeyboardNavigation() {
  console.log('\n⌨️ Testing Keyboard Navigation...');
  
  const focusableElements = document.querySelectorAll(
    'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
  );
  
  console.log(`✅ Found ${focusableElements.length} focusable elements`);
  
  // Test tab order
  const tabOrder = [];
  focusableElements.forEach((element, index) => {
    const tabIndex = element.tabIndex;
    const tagName = element.tagName.toLowerCase();
    const role = element.getAttribute('role') || 'none';
    
    tabOrder.push({
      index: index + 1,
      element: tagName,
      role: role,
      tabIndex: tabIndex,
      text: element.textContent?.trim().substring(0, 30) || element.getAttribute('aria-label') || 'No text'
    });
  });
  
  console.table(tabOrder);
  
  // Check for focus indicators
  const elementsWithFocus = document.querySelectorAll(':focus-visible, .focus-visible');
  console.log(`✅ Elements with focus indicators: ${elementsWithFocus.length}`);
  
  return {
    focusableCount: focusableElements.length,
    tabOrder: tabOrder
  };
}

/**
 * Test 3: Color Contrast
 */
function testColorContrast() {
  console.log('\n🎨 Testing Color Contrast...');
  
  const colorTests = [
    { name: 'Primary Blue', bg: '#2563eb', fg: '#ffffff' },
    { name: 'Success Green', bg: '#16a34a', fg: '#ffffff' },
    { name: 'Error Red', bg: '#dc2626', fg: '#ffffff' },
    { name: 'Warning Orange', bg: '#ea580c', fg: '#ffffff' },
    { name: 'Text on White', bg: '#ffffff', fg: '#374151' }
  ];
  
  function calculateContrast(bg, fg) {
    // Simplified contrast calculation
    function getLuminance(hex) {
      const rgb = parseInt(hex.slice(1), 16);
      const r = (rgb >> 16) & 0xff;
      const g = (rgb >> 8) & 0xff;
      const b = (rgb >> 0) & 0xff;
      
      const [rs, gs, bs] = [r, g, b].map(c => {
        c = c / 255;
        return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
      });
      
      return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs;
    }
    
    const l1 = getLuminance(bg);
    const l2 = getLuminance(fg);
    const lighter = Math.max(l1, l2);
    const darker = Math.min(l1, l2);
    
    return (lighter + 0.05) / (darker + 0.05);
  }
  
  colorTests.forEach(test => {
    const ratio = calculateContrast(test.bg, test.fg);
    const passAA = ratio >= 4.5;
    const passAAA = ratio >= 7;
    
    console.log(`${passAA ? '✅' : '❌'} ${test.name}: ${ratio.toFixed(2)}:1 ${passAA ? '(AA)' : '(FAIL)'} ${passAAA ? '(AAA)' : ''}`);
  });
}

/**
 * Test 4: Screen Reader Compatibility
 */
function testScreenReaderCompatibility() {
  console.log('\n🔊 Testing Screen Reader Compatibility...');
  
  // Check for proper heading structure
  const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
  console.log(`✅ Found ${headings.length} headings`);
  
  const headingStructure = [];
  headings.forEach(heading => {
    headingStructure.push({
      level: heading.tagName,
      text: heading.textContent.trim().substring(0, 50),
      hasId: heading.hasAttribute('id')
    });
  });
  
  if (headingStructure.length > 0) {
    console.table(headingStructure);
  }
  
  // Check for alt text on images
  const images = document.querySelectorAll('img');
  const imagesWithoutAlt = [];
  
  images.forEach((img, index) => {
    if (!img.hasAttribute('alt')) {
      imagesWithoutAlt.push(`Image ${index + 1}: ${img.src}`);
    }
  });
  
  console.log(`✅ Found ${images.length} images`);
  if (imagesWithoutAlt.length > 0) {
    console.warn('⚠️ Images without alt text:', imagesWithoutAlt);
  } else {
    console.log('✅ All images have alt text');
  }
  
  // Check for live regions
  const liveRegions = document.querySelectorAll('[aria-live]');
  console.log(`✅ Found ${liveRegions.length} live regions`);
  
  return {
    headings: headings.length,
    images: images.length,
    imagesWithoutAlt: imagesWithoutAlt.length,
    liveRegions: liveRegions.length
  };
}

/**
 * Test 5: Form Accessibility
 */
function testFormAccessibility() {
  console.log('\n📝 Testing Form Accessibility...');
  
  const inputs = document.querySelectorAll('input, textarea, select');
  const inputResults = [];
  
  inputs.forEach((input, index) => {
    const hasLabel = document.querySelector(`label[for="${input.id}"]`) !== null;
    const hasAriaLabel = input.hasAttribute('aria-label');
    const hasAriaLabelledBy = input.hasAttribute('aria-labelledby');
    const hasPlaceholder = input.hasAttribute('placeholder');
    
    const isAccessible = hasLabel || hasAriaLabel || hasAriaLabelledBy;
    
    inputResults.push({
      index: index + 1,
      type: input.type || input.tagName.toLowerCase(),
      hasLabel,
      hasAriaLabel,
      hasAriaLabelledBy,
      hasPlaceholder,
      isAccessible
    });
  });
  
  console.log(`✅ Found ${inputs.length} form inputs`);
  if (inputResults.length > 0) {
    console.table(inputResults);
  }
  
  const inaccessibleInputs = inputResults.filter(input => !input.isAccessible);
  if (inaccessibleInputs.length > 0) {
    console.warn(`⚠️ ${inaccessibleInputs.length} inputs lack proper labels`);
  } else {
    console.log('✅ All inputs have proper labels');
  }
  
  return inputResults;
}

/**
 * Run All Tests
 */
function runAllAccessibilityTests() {
  console.log('🚀 Running Complete Accessibility Test Suite...');
  console.log('='.repeat(60));
  
  const results = {
    aria: testARIAAttributes(),
    keyboard: testKeyboardNavigation(),
    forms: testFormAccessibility(),
    screenReader: testScreenReaderCompatibility()
  };
  
  testColorContrast();
  
  console.log('\n📊 Test Summary:');
  console.log('='.repeat(60));
  console.log(`ARIA Elements: ${results.aria.alerts + results.aria.status + results.aria.progressbars} total`);
  console.log(`Focusable Elements: ${results.keyboard.focusableCount}`);
  console.log(`Form Inputs: ${results.forms.length}`);
  console.log(`Headings: ${results.screenReader.headings}`);
  console.log(`Images: ${results.screenReader.images}`);
  console.log(`Live Regions: ${results.screenReader.liveRegions}`);
  
  // Overall score calculation
  let score = 100;
  if (results.aria.missingLabels.length > 0) score -= 10;
  if (results.screenReader.imagesWithoutAlt > 0) score -= 15;
  if (results.forms.filter(f => !f.isAccessible).length > 0) score -= 20;
  
  console.log(`\n🎯 Accessibility Score: ${score}/100`);
  
  if (score >= 90) {
    console.log('🎉 Excellent accessibility implementation!');
  } else if (score >= 75) {
    console.log('👍 Good accessibility, minor improvements needed');
  } else if (score >= 60) {
    console.log('⚠️ Moderate accessibility, several improvements needed');
  } else {
    console.log('❌ Poor accessibility, significant improvements required');
  }
  
  return results;
}

// Auto-run tests when script loads
runAllAccessibilityTests();

// Export for manual testing
window.accessibilityTests = {
  runAll: runAllAccessibilityTests,
  testARIA: testARIAAttributes,
  testKeyboard: testKeyboardNavigation,
  testContrast: testColorContrast,
  testScreenReader: testScreenReaderCompatibility,
  testForms: testFormAccessibility
};
