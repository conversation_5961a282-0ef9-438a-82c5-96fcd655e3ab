'use client';

import { useState, useEffect, useCallback } from 'react';
import { motion } from 'framer-motion';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { BlogHero } from '@/components/blog/BlogHero';
import { OptimizedPinterestLayout } from '@/components/blog/OptimizedPinterestLayout';
import { GridLayout } from '@/components/blog/GridLayout';
import { LayoutToggle, LayoutType } from '@/components/blog/LayoutToggle';
import { BlogSkeleton } from '@/components/blog/BlogSkeleton';
import { FeaturedTools } from '@/components/blog/FeaturedTools';
import { TrendingTopics } from '@/components/blog/TrendingTopics';
import { AboutPlatform } from '@/components/blog/AboutPlatform';
import { fetchBlogPosts } from '@/services/blogService';
import { ChevronLeft, ChevronRight, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { usePullToRefresh, hapticFeedback } from '@/hooks/useTouch';


interface BlogPost {
  id: string;
  _id: string;
  title: string;
  excerpt: string;
  content: string;
  slug: string;
  featuredImage?: string;
  imageCredit?: string;
  categories: string[];
  tags: string[];
  publishedAt: string;
  author: {
    name: string;
    email: string;
  };
}

// Type adapter to handle API response
interface ApiBlogPost {
  _id: string;
  title: string;
  content: string;
  slug: string;
  description?: string;
  featuredImage?: string;
  imageCredit?: string;
  category?: string; // Single category string from API
  categories?: string[]; // Array format for compatibility
  tags: string[];
  publishedAt: string;
  createdAt: string;
  author: {
    name: string;
    email: string;
  };
  excerpt: string;
}

// Convert API response to BlogPost format
const adaptApiBlogPost = (apiPost: ApiBlogPost): BlogPost => ({
  id: apiPost._id,
  _id: apiPost._id,
  title: apiPost.title,
  excerpt: apiPost.excerpt || apiPost.description || '',
  content: apiPost.content,
  slug: apiPost.slug,
  featuredImage: apiPost.featuredImage,
  imageCredit: apiPost.imageCredit,
  categories: apiPost.categories || (apiPost.category ? [apiPost.category] : []),
  tags: apiPost.tags || [],
  publishedAt: apiPost.publishedAt || apiPost.createdAt,
  author: apiPost.author
});

export default function BlogPage() {
  const [posts, setPosts] = useState<BlogPost[]>([]);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [layoutType, setLayoutType] = useState<LayoutType>('pinterest');
  const [refreshing, setRefreshing] = useState(false);

  const postsPerPage = 9; // 3x3 grid

  const loadPosts = useCallback(async () => {
    try {
      setLoading(true);
      const response = await fetchBlogPosts({
        page: currentPage,
        limit: postsPerPage,
        status: 'published'
      });

      if (response.success && response.data) {
        const adaptedPosts = (response.data as any[]).map((post: any) => adaptApiBlogPost(post));
        setPosts(adaptedPosts);
        if (response.pagination) {
          setTotalPages(response.pagination.totalPages);
        }
      } else {
        // If API fails, set empty array instead of mock data
        setPosts([]);
        setTotalPages(0);
        console.error('Failed to load posts:', response.error);
      }
    } catch (error) {
      console.error('Error loading posts:', error);
      // Set empty array instead of mock data
      setPosts([]);
      setTotalPages(0);
    } finally {
      setLoading(false);
    }
  }, [currentPage, postsPerPage]);

  useEffect(() => {
    loadPosts();
  }, [loadPosts]);

  // Pull to refresh functionality
  const handleRefresh = async () => {
    setRefreshing(true);
    hapticFeedback.medium();
    await loadPosts();
    setRefreshing(false);
    hapticFeedback.success();
  };

  const pullToRefreshHandlers = usePullToRefresh(handleRefresh, 80);

  // Destructure to separate DOM-safe handlers from state properties
  const { isPulling, pullDistance, pullProgress, ...domSafeHandlers } = pullToRefreshHandlers;

  return (
    <div className="min-h-screen bg-background">
      <Header />

      {/* Hero Section */}
      <BlogHero />

      {/* Enhanced Blog Cards Grid Section */}
      <section
        className="py-16 px-4 bg-background pull-to-refresh relative"
        {...domSafeHandlers}
      >
        {/* Pull to refresh indicator */}
        {isPulling && (
          <motion.div
            className="pull-indicator"
            style={{
              top: Math.min(pullDistance - 60, 20),
            }}
            animate={{
              rotate: pullProgress * 360,
              scale: Math.min(pullProgress * 1.2, 1),
            }}
          >
            <RefreshCw className="w-6 h-6 text-blue-500" />
          </motion.div>
        )}

        <div className="max-w-7xl mx-auto overflow-visible">{/* Explicitly set overflow-visible to prevent scroll issues */}

          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-12"
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="inline-flex items-center gap-2 px-6 py-3 rounded-full bg-primary/10 text-primary text-sm font-medium mb-4"
            >
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ duration: 3, repeat: Infinity, ease: "linear" }}
              >
                ✨
              </motion.div>
              Latest Articles
            </motion.div>

            <motion.h2
              className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 bg-gradient-to-r from-primary via-purple-500 to-orange-500 bg-clip-text text-transparent"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
            >
              Discover Amazing Stories
            </motion.h2>

          </motion.div>

          {/* Layout Toggle */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
            className="flex justify-center mb-8"
          >
            <LayoutToggle
              layoutType={layoutType}
              onLayoutChange={setLayoutType}
              className="mb-4"
            />
          </motion.div>

          {loading ? (
            <BlogSkeleton count={9} layout={layoutType === 'pinterest' ? 'masonry' : 'grid'} />
          ) : (
            <>
              {/* Dynamic Layout Rendering */}
              <div className="mb-12">
                {layoutType === 'pinterest' ? (
                  <OptimizedPinterestLayout
                    posts={posts}
                    loading={false}
                    showAnimation={true}
                  />
                ) : (
                  <GridLayout
                    posts={posts}
                    loading={false}
                    showAnimation={true}
                  />
                )}
              </div>

              {/* Enhanced Pagination */}
              {totalPages > 1 && (
                <motion.div
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: 0.4 }}
                  className="flex justify-center items-center gap-6"
                >
                  <motion.div
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <Button
                      variant="outline"
                      onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                      disabled={currentPage === 1}
                      className="flex items-center gap-3 px-6 py-3 rounded-full font-semibold transition-all duration-300 hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <ChevronLeft className="h-5 w-5" />
                      Previous
                    </Button>
                  </motion.div>

                  <div className="flex gap-3">
                    {Array.from({ length: Math.min(totalPages, 5) }, (_, i) => {
                      let page;
                      if (totalPages <= 5) {
                        page = i + 1;
                      } else if (currentPage <= 3) {
                        page = i + 1;
                      } else if (currentPage >= totalPages - 2) {
                        page = totalPages - 4 + i;
                      } else {
                        page = currentPage - 2 + i;
                      }

                      return (
                        <motion.div
                          key={page}
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.9 }}
                        >
                          <Button
                            variant={currentPage === page ? "default" : "outline"}
                            onClick={() => setCurrentPage(page)}
                            className={`
                              w-12 h-12 rounded-full font-bold transition-all duration-300
                              ${currentPage === page
                                ? 'bg-gradient-to-r from-primary to-purple-500 text-white shadow-lg shadow-primary/25'
                                : 'hover:shadow-md'
                              }
                            `}
                          >
                            {page}
                          </Button>
                        </motion.div>
                      );
                    })}
                  </div>

                  <motion.div
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <Button
                      variant="outline"
                      onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                      disabled={currentPage === totalPages}
                      className="flex items-center gap-3 px-6 py-3 rounded-full font-semibold transition-all duration-300 hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Next
                      <ChevronRight className="h-5 w-5" />
                    </Button>
                  </motion.div>
                </motion.div>
              )}
            </>
          )}
        </div>
      </section>

      {/* Featured Tools Section */}
      <FeaturedTools />

      {/* Trending Topics Slider */}
      <TrendingTopics />

      {/* About Platform Section */}
      <AboutPlatform />

      <Footer />
    </div>
  );
}
