"use client";

import { useEffect, useState } from "react";
import Link from "next/link";
import Image from "next/image";
import { motion } from "framer-motion";
import { ArrowLeft, Calendar, User, Tag, Camera, Clock } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { getRelatedTools, getRelatedBlogPosts, getCategoryStyle, RelatedTool, RelatedBlogPost } from "@/lib/blog-utils-client";
import { useCategoryName } from "@/lib/categoryUtils";
import RelatedTools from "./RelatedTools";
import RelatedBlogPosts from "./RelatedBlogPosts";

interface BlogPost {
  _id?: string;
  title: string;
  date: string;
  author: string;
  category: string;
  image: string;
  content: string;
  description?: string;
  imageCredit?: string;
  tags?: string[];
}

interface BlogPostContentProps {
  post: BlogPost;
  slug: string;
}

export function BlogPostContent({ post, slug }: BlogPostContentProps) {
  const [relatedTools, setRelatedTools] = useState<RelatedTool[]>([]);
  const [relatedPosts, setRelatedPosts] = useState<RelatedBlogPost[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Get proper category name and styling
  const categoryName = useCategoryName(post.category);
  const categoryStyle = getCategoryStyle(categoryName);

  useEffect(() => {
    // Smooth scroll to top when page loads
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });

    // Load related content
    const loadRelatedContent = async () => {
      try {
        setIsLoading(true);

        // Get related tools
        const tools = getRelatedTools(post.category, post.content, post.tags || []);
        setRelatedTools(tools);

        // Get related blog posts
        const posts = await getRelatedBlogPosts(slug, post.category, post.tags || []);
        setRelatedPosts(posts);
      } catch (error) {
        console.error('Error loading related content:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadRelatedContent();
    // Only depend on slug to avoid infinite re-renders
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [slug]);

  return (
    <main className="flex-1 bg-background">
      {/* Back Button */}
      <motion.div
        initial={{ opacity: 0, x: -20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.5 }}
        className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-6"
      >
        <Link href="/blog">
          <motion.div
            whileHover={{ x: -5 }}
            transition={{ type: "spring", stiffness: 400, damping: 10 }}
            className="inline-flex items-center gap-2 text-muted-foreground hover:text-foreground transition-colors duration-300 group"
          >
            <ArrowLeft className="h-4 w-4 transition-transform group-hover:-translate-x-1" />
            <span className="font-medium">Back to Blogs</span>
            <div className="h-px bg-gradient-to-r from-muted-foreground/50 to-transparent w-0 group-hover:w-8 transition-all duration-300" />
          </motion.div>
        </Link>
      </motion.div>

      {/* Hero Cover Image Section */}
      <motion.section
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.7, delay: 0.2 }}
        className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"
      >
        <div className="relative h-[50vh] md:h-[60vh] rounded-3xl overflow-hidden shadow-2xl group">
          {/* Hero Image with Hover Effect */}
          <motion.div
            whileHover={{ scale: 1.05 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
            className="absolute inset-0"
          >
            <Image
              src={post.image}
              alt={post.title}
              fill
              className="object-cover transition-all duration-700 group-hover:brightness-110 group-hover:contrast-110"
            />
          </motion.div>

          {/* Gradient Overlays */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent" />
          <div className="absolute inset-0 bg-gradient-to-r from-primary/10 via-transparent to-secondary/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />



          {/* Hero Content */}
          <div className="absolute inset-0 flex items-end">
            <div className="w-full p-8 md:p-12">
              <motion.div
                initial={{ y: 30, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.5, duration: 0.7 }}
                className="max-w-4xl"
              >
                {/* Category Badge */}
                <motion.div
                  initial={{ scale: 0.8, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  transition={{ delay: 0.3, duration: 0.5 }}
                  className="mb-4"
                >
                  <Badge
                    className={`${categoryStyle.bgColor} ${categoryStyle.color} border-0 text-sm font-semibold px-4 py-2 shadow-lg backdrop-blur-sm`}
                  >
                    <span className="mr-2">{categoryStyle.icon}</span>
                    {categoryName}
                  </Badge>
                </motion.div>

                {/* Title */}
                <motion.h1
                  initial={{ y: 20, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ delay: 0.6, duration: 0.7 }}
                  className="text-3xl md:text-5xl lg:text-6xl font-bold text-white mb-6 leading-tight"
                >
                  {post.title}
                </motion.h1>

                {/* Image Credit (above meta) */}
                {post.imageCredit && (
                  <motion.div
                    initial={{ y: 20, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ delay: 0.7, duration: 0.5 }}
                    className="flex items-center gap-2 text-white/80 mb-4"
                  >
                    <Camera className="h-4 w-4" />
                    <span className="text-sm">Photo credit: </span>
                    {post.imageCredit.includes("http") || post.imageCredit.includes("www") ? (
                      <a
                        href={post.imageCredit}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-sm underline hover:text-white transition-colors"
                      >
                        {post.imageCredit}
                      </a>
                    ) : (
                      <span className="text-sm">{post.imageCredit}</span>
                    )}
                  </motion.div>
                )}

                {/* Meta Information */}
                <motion.div
                  initial={{ y: 20, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ delay: 0.8, duration: 0.5 }}
                  className="flex flex-wrap items-center gap-6 text-white/90"
                >
                  <div className="flex items-center gap-2">
                    <User className="h-4 w-4" />
                    <span className="font-medium">{post.author}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4" />
                    <span>{post.date}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4" />
                    <span>{Math.ceil(post.content.split(' ').length / 200)} min read</span>
                  </div>
                </motion.div>
              </motion.div>
            </div>
          </div>
        </div>
      </motion.section>

      {/* Main Content Section */}
      <motion.section
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, delay: 0.4 }}
        className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12"
      >
        {/* Tags Section */}
        {post.tags && post.tags.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6, duration: 0.5 }}
            className="mb-8"
          >
            <div className="flex flex-wrap gap-3">
              {post.tags.map((tag, index) => (
                <motion.div
                  key={tag}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.7 + index * 0.1, duration: 0.3 }}
                  whileHover={{ scale: 1.05 }}
                  className="inline-flex items-center gap-1 px-3 py-1 rounded-full bg-muted/50 text-muted-foreground hover:bg-primary/10 hover:text-primary transition-all duration-200 text-sm"
                >
                  <Tag className="h-3 w-3" />
                  {tag}
                </motion.div>
              ))}
            </div>
          </motion.div>
        )}

        {/* Blog Content */}
        <motion.article
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8, duration: 0.6 }}
          className="prose prose-lg max-w-none text-foreground prose-headings:text-foreground prose-a:text-primary prose-blockquote:border-primary/30 prose-code:text-primary prose-pre:bg-muted/50 prose-img:rounded-xl prose-img:shadow-lg"
        >
          <div dangerouslySetInnerHTML={{ __html: post.content }} />
        </motion.article>


      </motion.section>

      {/* Related Tools Section */}
      {!isLoading && relatedTools.length > 0 && (
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <RelatedTools tools={relatedTools} />
        </div>
      )}

      {/* Related Blog Posts Section */}
      {!isLoading && relatedPosts.length > 0 && (
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <RelatedBlogPosts posts={relatedPosts} />
        </div>
      )}
    </main>
  );
}
