import { NextRequest, NextResponse } from 'next/server';
import { PDFDocument } from 'pdf-lib';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const files = formData.getAll('files') as File[];

    if (!files || files.length === 0) {
      return NextResponse.json(
        { error: 'No files provided' },
        { status: 400 }
      );
    }

    // Validate all files are images
    const supportedTypes = ['image/jpeg', 'image/jpg', 'image/png'];
    for (const file of files) {
      if (!supportedTypes.includes(file.type)) {
        return NextResponse.json(
          { error: `File "${file.name}" is not a supported image format. Supported formats: JPG, JPEG, PNG` },
          { status: 400 }
        );
      }
    }

    // Create a new PDF document
    const pdfDoc = await PDFDocument.create();
    
    let totalImages = 0;
    const imageInfo = [];

    // Process each image file
    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      const arrayBuffer = await file.arrayBuffer();
      
      try {
        let image;
        
        // Embed image based on type
        if (file.type === 'image/jpeg' || file.type === 'image/jpg') {
          image = await pdfDoc.embedJpg(arrayBuffer);
        } else if (file.type === 'image/png') {
          image = await pdfDoc.embedPng(arrayBuffer);
        } else {
          throw new Error(`Unsupported image type: ${file.type}`);
        }

        // Get image dimensions
        const { width, height } = image.scale(1);
        
        // Calculate page size to fit image (max A4 size)
        const maxWidth = 595.28; // A4 width in points
        const maxHeight = 841.89; // A4 height in points
        
        let pageWidth = width;
        let pageHeight = height;
        
        // Scale down if image is larger than A4
        if (width > maxWidth || height > maxHeight) {
          const widthRatio = maxWidth / width;
          const heightRatio = maxHeight / height;
          const scale = Math.min(widthRatio, heightRatio);
          
          pageWidth = width * scale;
          pageHeight = height * scale;
        }

        // Add a new page for this image
        const page = pdfDoc.addPage([pageWidth, pageHeight]);
        
        // Draw the image on the page
        page.drawImage(image, {
          x: 0,
          y: 0,
          width: pageWidth,
          height: pageHeight,
        });

        totalImages++;
        imageInfo.push({
          name: file.name,
          originalWidth: width,
          originalHeight: height,
          pageWidth: pageWidth,
          pageHeight: pageHeight,
          size: arrayBuffer.byteLength
        });

      } catch (error) {
        console.error(`Error processing image ${file.name}:`, error);
        return NextResponse.json(
          { error: `Failed to process image "${file.name}". Please ensure it's a valid image file.` },
          { status: 400 }
        );
      }
    }

    // Save the PDF
    const pdfBytes = await pdfDoc.save({
      useObjectStreams: true,
      addDefaultPage: false,
    });

    // Generate filename for PDF
    const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
    const pdfFilename = files.length === 1 
      ? `${files[0].name.replace(/\.(jpg|jpeg|png)$/i, '')}_converted.pdf`
      : `images_to_pdf_${files.length}_images_${timestamp}.pdf`;

    // Create response with PDF
    const response = new NextResponse(pdfBytes, {
      status: 200,
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `attachment; filename="${pdfFilename}"`,
        'X-Total-Images': totalImages.toString(),
        'X-Total-Pages': totalImages.toString(),
        'X-Image-Info': JSON.stringify(imageInfo),
      },
    });

    return response;

  } catch (error) {
    console.error('Image to PDF conversion error:', error);
    return NextResponse.json(
      { error: 'Failed to convert images to PDF. Please ensure all files are valid image files.' },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json(
    { 
      message: 'Image to PDF Conversion API',
      supportedFormats: ['JPG', 'JPEG', 'PNG'],
      maxFiles: 20,
      maxFileSize: '10MB per file',
      note: 'Each image will be placed on a separate page in the PDF'
    },
    { status: 200 }
  );
}
