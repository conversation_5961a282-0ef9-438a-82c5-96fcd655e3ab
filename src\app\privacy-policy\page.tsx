"use client";

import { motion } from "framer-motion";
import Link from "next/link";
import { ArrowLeft, Shield, Eye, Lock, Database, Mail, Globe, Calendar } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import GlobalSEO from "@/components/seo/GlobalSEO";

export default function PrivacyPolicyPage() {
  const router = useRouter();

  const sections = [
    {
      id: "information-collection",
      title: "Information We Collect",
      icon: <Database className="h-5 w-5" />,
      content: [
        {
          subtitle: "Personal Information",
          text: "When you create an account, we collect your name, email address, and encrypted password. We may also collect additional profile information you choose to provide."
        },
        {
          subtitle: "Usage Data", 
          text: "We automatically collect information about how you use our platform, including tools accessed, calculation history, and interaction patterns to improve our services."
        },
        {
          subtitle: "Technical Information",
          text: "We collect device information, IP addresses, browser type, and operating system to ensure platform security and optimize performance."
        }
      ]
    },
    {
      id: "information-use",
      title: "How We Use Your Information",
      icon: <Eye className="h-5 w-5" />,
      content: [
        {
          subtitle: "Service Provision",
          text: "We use your information to provide, maintain, and improve our tools and calculators, process your requests, and deliver personalized experiences."
        },
        {
          subtitle: "Communication",
          text: "We may send you service-related notifications, security alerts, and optional newsletters. You can opt out of marketing communications at any time."
        },
        {
          subtitle: "Analytics and Improvement",
          text: "We analyze usage patterns to enhance our platform, develop new features, and ensure optimal performance across all tools and calculators."
        }
      ]
    },
    {
      id: "information-sharing",
      title: "Information Sharing and Disclosure",
      icon: <Globe className="h-5 w-5" />,
      content: [
        {
          subtitle: "Third-Party Services",
          text: "We may share data with trusted service providers who assist in platform operations, including analytics providers, hosting services, and authentication providers."
        },
        {
          subtitle: "Legal Requirements",
          text: "We may disclose information when required by law, to protect our rights, prevent fraud, or ensure user safety and platform security."
        },
        {
          subtitle: "Business Transfers",
          text: "In the event of a merger, acquisition, or sale of assets, user information may be transferred as part of the business transaction."
        }
      ]
    },
    {
      id: "data-security",
      title: "Data Security",
      icon: <Lock className="h-5 w-5" />,
      content: [
        {
          subtitle: "Encryption and Protection",
          text: "We use industry-standard encryption (bcrypt for passwords, HTTPS for data transmission) and secure authentication methods including JWT tokens."
        },
        {
          subtitle: "Access Controls",
          text: "We implement role-based access controls, regular security audits, and monitor for unauthorized access attempts to protect your data."
        },
        {
          subtitle: "Data Retention",
          text: "We retain personal information only as long as necessary to provide services, comply with legal obligations, and resolve disputes."
        }
      ]
    },
    {
      id: "cookies-tracking",
      title: "Cookies and Tracking",
      icon: <Shield className="h-5 w-5" />,
      content: [
        {
          subtitle: "Essential Cookies",
          text: "We use necessary cookies for authentication, session management, and core platform functionality. These cannot be disabled."
        },
        {
          subtitle: "Analytics Cookies",
          text: "We may use Google Analytics and similar services to understand user behavior and improve our platform. You can opt out through browser settings."
        },
        {
          subtitle: "Preference Cookies",
          text: "We store your theme preferences, language settings, and other customization choices to enhance your user experience."
        }
      ]
    },
    {
      id: "user-rights",
      title: "Your Rights and Choices",
      icon: <Calendar className="h-5 w-5" />,
      content: [
        {
          subtitle: "Access and Portability",
          text: "You have the right to access, download, and transfer your personal data. Contact us to request a copy of your information."
        },
        {
          subtitle: "Correction and Deletion",
          text: "You can update your profile information at any time and request deletion of your account and associated data."
        },
        {
          subtitle: "Communication Preferences",
          text: "You can manage email preferences, opt out of marketing communications, and control notification settings in your account."
        }
      ]
    }
  ];

  return (
    <>
      <GlobalSEO
        pageTitle="Privacy Policy"
        pageDescription="Learn how ToolCrush protects your privacy and handles your personal information. We are committed to transparency and data security."
        pageUrl="/privacy-policy"
        pageType="article"
      />
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700"
      >
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <Button
              variant="ghost"
              onClick={() => router.back()}
              className="flex items-center text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
            <div className="text-sm text-gray-500 dark:text-gray-400">
              Last updated: {new Date().toLocaleDateString()}
            </div>
          </div>
        </div>
      </motion.div>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-12">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
          className="max-w-4xl mx-auto"
        >
          {/* Title Section */}
          <div className="text-center mb-12">
            <motion.div
              initial={{ scale: 0.9 }}
              animate={{ scale: 1 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="inline-flex items-center justify-center w-16 h-16 bg-blue-100 dark:bg-blue-900/30 rounded-full mb-6"
            >
              <Shield className="h-8 w-8 text-blue-600 dark:text-blue-400" />
            </motion.div>
            <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
              Privacy Policy
            </h1>
            <p className="text-xl text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
              We are committed to protecting your privacy and ensuring the security of your personal information.
            </p>
          </div>

          {/* Table of Contents */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
            className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6 mb-8"
          >
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              Table of Contents
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
              {sections.map((section, index) => (
                <motion.a
                  key={section.id}
                  href={`#${section.id}`}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.3, delay: 0.4 + index * 0.1 }}
                  className="flex items-center p-2 rounded-md text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                >
                  {section.icon}
                  <span className="ml-2">{section.title}</span>
                </motion.a>
              ))}
            </div>
          </motion.div>

          {/* Sections */}
          <div className="space-y-8">
            {sections.map((section, sectionIndex) => (
              <motion.section
                key={section.id}
                id={section.id}
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.5 + sectionIndex * 0.1 }}
                className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-8"
              >
                <div className="flex items-center mb-6">
                  <div className="flex items-center justify-center w-10 h-10 bg-blue-100 dark:bg-blue-900/30 rounded-lg mr-4">
                    {section.icon}
                  </div>
                  <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                    {section.title}
                  </h2>
                </div>
                
                <div className="space-y-6">
                  {section.content.map((item, itemIndex) => (
                    <motion.div
                      key={itemIndex}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.5, delay: 0.6 + sectionIndex * 0.1 + itemIndex * 0.05 }}
                    >
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                        {item.subtitle}
                      </h3>
                      <p className="text-gray-600 dark:text-gray-400 leading-relaxed">
                        {item.text}
                      </p>
                    </motion.div>
                  ))}
                </div>
              </motion.section>
            ))}
          </div>

          {/* Contact Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.8 }}
            className="bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800 p-8 mt-12"
          >
            <div className="text-center">
              <Mail className="h-12 w-12 text-blue-600 dark:text-blue-400 mx-auto mb-4" />
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                Questions About Privacy?
              </h2>
              <p className="text-gray-600 dark:text-gray-400 mb-6 max-w-2xl mx-auto">
                If you have any questions about this Privacy Policy or how we handle your data, 
                please don&rsquo;t hesitate to contact us.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button asChild>
                  <Link href="/contact">
                    Contact Us
                  </Link>
                </Button>
                <Button variant="outline" asChild>
                  <Link href="mailto:<EMAIL>">
                    Email Privacy Team
                  </Link>
                </Button>
              </div>
            </div>
          </motion.div>
        </motion.div>
      </div>
      </div>
    </>
  );
}
