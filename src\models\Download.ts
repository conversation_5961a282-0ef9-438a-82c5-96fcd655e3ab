import mongoose, { Schema, Document } from 'mongoose';

export interface IDownload extends Document {
  userId: string;
  userEmail: string;
  userName?: string;
  fileName: string;
  conversionType: string;
  originalFileSize?: number;
  convertedFileSize?: number;
  toolName?: string;
  userAgent?: string;
  clientIP?: string;
  timestamp: Date;
  createdAt: Date;
  updatedAt: Date;
}

const DownloadSchema: Schema = new Schema({
  userId: {
    type: String,
    required: true,
    index: true
  },
  userEmail: {
    type: String,
    required: true,
    index: true
  },
  userName: {
    type: String,
    required: false
  },
  fileName: {
    type: String,
    required: true
  },
  conversionType: {
    type: String,
    required: true,
    index: true
  },
  originalFileSize: {
    type: Number,
    required: false
  },
  convertedFileSize: {
    type: Number,
    required: false
  },
  toolName: {
    type: String,
    required: false,
    index: true
  },
  userAgent: {
    type: String,
    required: false
  },
  clientIP: {
    type: String,
    required: false
  },
  timestamp: {
    type: Date,
    required: true,
    index: true
  },
  createdAt: {
    type: Date,
    default: Date.now,
    index: true
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Compound indexes for efficient queries
DownloadSchema.index({ userId: 1, timestamp: -1 });
DownloadSchema.index({ userId: 1, conversionType: 1 });
DownloadSchema.index({ createdAt: 1 }); // For archival queries

// TTL index to automatically delete records older than 30 days (for cleanup)
DownloadSchema.index({ createdAt: 1 }, { expireAfterSeconds: 30 * 24 * 60 * 60 });

// Update the updatedAt field before saving
DownloadSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

// Static method to get download statistics
DownloadSchema.statics.getStatistics = async function(userId?: string, days?: number) {
  const matchStage: any = {};
  
  if (userId) {
    matchStage.userId = userId;
  }
  
  if (days) {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);
    matchStage.createdAt = { $gte: startDate };
  }

  return this.aggregate([
    { $match: matchStage },
    {
      $group: {
        _id: null,
        totalDownloads: { $sum: 1 },
        totalFileSize: { $sum: '$convertedFileSize' },
        avgFileSize: { $avg: '$convertedFileSize' },
        uniqueUsers: { $addToSet: '$userId' },
        conversionTypes: { $addToSet: '$conversionType' },
        toolsUsed: { $addToSet: '$toolName' }
      }
    },
    {
      $project: {
        _id: 0,
        totalDownloads: 1,
        totalFileSize: 1,
        avgFileSize: 1,
        uniqueUserCount: { $size: '$uniqueUsers' },
        uniqueConversionTypes: { $size: '$conversionTypes' },
        uniqueToolsUsed: { $size: '$toolsUsed' },
        conversionTypes: 1,
        toolsUsed: 1
      }
    }
  ]);
};

// Static method to get downloads for archival (older than specified hours)
DownloadSchema.statics.getDownloadsForArchival = async function(hoursOld: number = 24) {
  const cutoffDate = new Date();
  cutoffDate.setHours(cutoffDate.getHours() - hoursOld);
  
  return this.find({
    createdAt: { $lt: cutoffDate }
  }).sort({ createdAt: 1 });
};

// Static method to delete archived downloads
DownloadSchema.statics.deleteArchivedDownloads = async function(downloadIds: string[]) {
  return this.deleteMany({
    _id: { $in: downloadIds }
  });
};

// Instance method to format for export
DownloadSchema.methods.toExportFormat = function() {
  return {
    'User ID': this.userId,
    'User Email': this.userEmail,
    'User Name': this.userName || '',
    'File Name': this.fileName,
    'Conversion Type': this.conversionType,
    'Original File Size (bytes)': this.originalFileSize || 0,
    'Converted File Size (bytes)': this.convertedFileSize || 0,
    'Tool Name': this.toolName || '',
    'User Agent': this.userAgent || '',
    'Client IP': this.clientIP || '',
    'Download Timestamp': this.timestamp.toISOString(),
    'Record Created': this.createdAt.toISOString()
  };
};

export default mongoose.models.Download || mongoose.model<IDownload>('Download', DownloadSchema);
