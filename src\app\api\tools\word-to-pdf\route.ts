import { NextRequest, NextResponse } from 'next/server';
import { PDFDocument, rgb, StandardFonts } from 'pdf-lib';
import mammoth from 'mammoth';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }

    if (!file.name.toLowerCase().endsWith('.docx') && 
        file.type !== 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
      return NextResponse.json(
        { error: 'File must be a DOCX document' },
        { status: 400 }
      );
    }

    // Convert file to buffer
    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);
    
    // Extract text from DOCX using mammoth
    const result = await mammoth.extractRawText({ buffer });
    const extractedText = result.value;

    if (!extractedText || extractedText.trim().length === 0) {
      return NextResponse.json(
        { error: 'No text content found in the Word document.' },
        { status: 400 }
      );
    }

    // Create PDF document
    const pdfDoc = await PDFDocument.create();
    const font = await pdfDoc.embedFont(StandardFonts.Helvetica);
    const fontSize = 12;
    const lineHeight = fontSize * 1.2;
    const margin = 50;
    
    // Page dimensions
    const pageWidth = 595.28; // A4 width in points
    const pageHeight = 841.89; // A4 height in points
    const textWidth = pageWidth - (margin * 2);
    const textHeight = pageHeight - (margin * 2);

    // Split text into paragraphs
    const paragraphs = extractedText.split('\n\n').filter(p => p.trim().length > 0);
    
    let currentPage = pdfDoc.addPage([pageWidth, pageHeight]);
    let yPosition = pageHeight - margin;

    for (const paragraph of paragraphs) {
      // Clean up paragraph text
      const cleanText = paragraph.replace(/\n/g, ' ').trim();
      
      // Split paragraph into lines that fit the page width
      const words = cleanText.split(' ');
      let currentLine = '';
      
      for (const word of words) {
        const testLine = currentLine ? `${currentLine} ${word}` : word;
        const textWidth = font.widthOfTextAtSize(testLine, fontSize);
        
        if (textWidth > textWidth - margin) {
          // Draw current line and start new line
          if (currentLine) {
            // Check if we need a new page
            if (yPosition < margin + lineHeight) {
              currentPage = pdfDoc.addPage([pageWidth, pageHeight]);
              yPosition = pageHeight - margin;
            }
            
            currentPage.drawText(currentLine, {
              x: margin,
              y: yPosition,
              size: fontSize,
              font: font,
              color: rgb(0, 0, 0),
            });
            
            yPosition -= lineHeight;
            currentLine = word;
          }
        } else {
          currentLine = testLine;
        }
      }
      
      // Draw the last line of the paragraph
      if (currentLine) {
        // Check if we need a new page
        if (yPosition < margin + lineHeight) {
          currentPage = pdfDoc.addPage([pageWidth, pageHeight]);
          yPosition = pageHeight - margin;
        }
        
        currentPage.drawText(currentLine, {
          x: margin,
          y: yPosition,
          size: fontSize,
          font: font,
          color: rgb(0, 0, 0),
        });
        
        yPosition -= lineHeight * 1.5; // Extra space between paragraphs
      }
    }

    // Save the PDF
    const pdfBytes = await pdfDoc.save();

    // Generate filename
    const originalName = file.name.replace(/\.(docx?)$/i, '');
    const pdfFilename = `${originalName}_converted.pdf`;

    // Create response with PDF
    const response = new NextResponse(pdfBytes, {
      status: 200,
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `attachment; filename="${pdfFilename}"`,
        'X-Original-Size': arrayBuffer.byteLength.toString(),
        'X-Extracted-Characters': extractedText.length.toString(),
        'X-Pages-Created': pdfDoc.getPageCount().toString(),
      },
    });

    return response;

  } catch (error) {
    console.error('Word to PDF conversion error:', error);
    
    // Provide more specific error messages
    if (error instanceof Error) {
      if (error.message.includes('Invalid DOCX')) {
        return NextResponse.json(
          { error: 'Invalid DOCX file. Please ensure the file is not corrupted.' },
          { status: 400 }
        );
      }
      if (error.message.includes('password')) {
        return NextResponse.json(
          { error: 'Password-protected Word documents are not supported.' },
          { status: 400 }
        );
      }
    }

    return NextResponse.json(
      { error: 'Failed to convert Word to PDF. Please ensure the file is a valid DOCX document.' },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json(
    { 
      message: 'Word to PDF Conversion API',
      supportedInput: 'DOCX',
      outputFormat: 'PDF',
      maxFileSize: '10MB',
      note: 'Only DOCX format is supported. Password-protected documents cannot be converted.'
    },
    { status: 200 }
  );
}
