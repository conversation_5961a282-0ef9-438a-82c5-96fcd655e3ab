# 🚀 GitHub Actions CI/CD Deployment Guide

## 📋 Overview

This guide provides comprehensive setup instructions for the enterprise-grade GitHub Actions CI/CD workflow that deploys the ToolBox project to Hostinger VPS at toolrapter.com domain.

## 🎯 Key Features

- **Enterprise Security**: Vulnerability scanning, dependency auditing, zero-tolerance TypeScript errors
- **Performance Validation**: <20s build time, <5s page load requirements
- **Zero-Downtime Deployment**: PM2 clustering with atomic deployments and rollback capability
- **Complete Upstash Removal**: In-memory rate limiting, no external paid services
- **Comprehensive Monitoring**: Health checks, SSL validation, performance metrics

## 🔐 Required GitHub Repository Secrets

Configure these secrets in your GitHub repository:
**Repository → Settings → Secrets and variables → Actions → Repository secrets**

### Hostinger VPS Configuration
```
HOSTINGER_VPS_HOST
```
**Value:** Your VPS IP address
```
*************
```

```
HOSTINGER_VPS_USERNAME
```
**Value:** SSH username (typically root or cpanel username)
```
root
```

```
HOSTINGER_VPS_SSH_KEY
```
**Value:** ED25519 private key (complete key including headers)
```
-----BEGIN OPENSSH PRIVATE KEY-----
b3BlbnNzaC1rZXktdjEAAAAABG5vbmUAAAAEbm9uZQAAAAAAAAABAAAAFwAAAAdzc2gtcn
[... your complete private key ...]
-----END OPENSSH PRIVATE KEY-----
```

```
HOSTINGER_VPS_PORT
```
**Value:** SSH port number (default: 22)
```
22
```

```
HOSTINGER_DEPLOY_PATH
```
**Value:** Full deployment path on your VPS
```
/home/<USER>/public_html
```

```
HOSTINGER_PM2_APP_NAME
```
**Value:** PM2 application name
```
toolrapter-app
```

### Application Configuration
```
MONGODB_URI
```
**Value:** MongoDB connection string
```
mongodb+srv://username:<EMAIL>/toolbox?retryWrites=true&w=majority
```

```
NEXTAUTH_SECRET
```
**Value:** NextAuth.js secret key (minimum 32 characters)
```
your-super-secure-secret-key-minimum-32-chars
```

```
NEXTAUTH_URL
```
**Value:** Production URL
```
https://toolrapter.com
```

### Optional OAuth Configuration
```
GOOGLE_CLIENT_ID
```
**Value:** Google OAuth client ID (optional)
```
your-google-client-id.apps.googleusercontent.com
```

```
GOOGLE_CLIENT_SECRET
```
**Value:** Google OAuth client secret (optional)
```
your-google-client-secret
```

## 🏗️ Workflow Architecture

### 1. Security Scan Job
- **Trivy vulnerability scanner** for filesystem security
- **npm audit** for dependency vulnerabilities
- **Results uploaded** to GitHub Security tab

### 2. Build & Test Job
- **TypeScript strict checking** (zero errors required)
- **ESLint validation** (zero warnings tolerance)
- **Jest test suite** with coverage
- **Performance-monitored build** (<20s requirement)
- **Optimized deployment package** creation

### 3. Deploy Job
- **Secret validation** for all required configurations
- **Atomic backup** of current deployment
- **Zero-downtime PM2 deployment** with clustering
- **Nginx reload** with enterprise security headers
- **Comprehensive health checks**

### 4. Verification Job
- **External health check** with retries
- **Performance validation** (<5s page load)
- **SSL certificate verification**
- **Domain accessibility confirmation**

### 5. Emergency Rollback
- **Automatic rollback** on deployment failure
- **Backup restoration** with PM2 restart
- **Health verification** post-rollback

## 🚀 Deployment Process

### Automatic Deployment
Triggered on push to `main` branch:
```bash
git push origin main
```

### Manual Deployment
Use GitHub Actions interface:
1. Go to **Actions** tab in your repository
2. Select **Enterprise Hostinger VPS Deployment**
3. Click **Run workflow**
4. Choose environment and options

### Emergency Deployment
For urgent fixes (skips tests):
1. Run workflow manually
2. Check **Skip tests (emergency deployment only)**
3. Monitor deployment carefully

## 📊 Performance Requirements

### Build Performance
- **Build Time**: <20 seconds (strict requirement)
- **Bundle Optimization**: Dynamic imports and code splitting
- **TypeScript**: Zero compilation errors

### Runtime Performance
- **Page Load**: <5 seconds (measured via external check)
- **Security Overhead**: <50ms per request
- **Memory Usage**: <512MB per PM2 instance

## 🔒 Security Features

### Vulnerability Scanning
- **Trivy scanner** for filesystem vulnerabilities
- **npm audit** for dependency security
- **Results integration** with GitHub Security

### Enterprise Headers
- **HSTS** with preload
- **CSP** compatible with Framer Motion
- **X-Frame-Options**, **X-Content-Type-Options**
- **Rate limiting** via Nginx

## 🛠️ Troubleshooting

### Common Issues

**Build Time Exceeded**
```bash
# Optimize bundle size
npm run analyze
# Check for large dependencies
npx bundle-analyzer
```

**TypeScript Errors**
```bash
# Run type check locally
pnpm run type-check
# Fix all errors before deployment
```

**Deployment Failure**
```bash
# Check VPS connectivity
ssh -p 22 username@your-vps-ip
# Verify PM2 status
pm2 status
```

**Health Check Failed**
```bash
# Check application logs
pm2 logs toolrapter-app
# Verify Nginx configuration
nginx -t
```

### Secret Configuration Issues
1. Verify all required secrets are configured
2. Check secret values don't contain extra spaces
3. Ensure SSH key includes complete headers
4. Validate MongoDB URI connectivity

## 📈 Monitoring & Maintenance

### Build Artifacts
- **Retention**: 5 days for deployment packages
- **Compression**: Level 9 for optimal storage
- **Naming**: Includes commit SHA for tracking

### Backup Strategy
- **Automatic backups** before each deployment
- **Retention**: Last 5 backups maintained
- **Emergency rollback** capability

### Performance Monitoring
- **Build time tracking** with failure on >20s
- **Page load validation** with <5s requirement
- **SSL certificate monitoring**

## 🎉 Success Criteria

✅ **Zero TypeScript compilation errors**  
✅ **Build completion within 20 seconds**  
✅ **All PM2 processes running in cluster mode**  
✅ **Nginx serving with enterprise security headers**  
✅ **Domain accessible with valid SSL certificate**  
✅ **Application health checks passing**  
✅ **Performance requirements met (<5s page load)**  
✅ **Complete Upstash removal verified**  

## 📞 Support

For deployment issues:
1. Check GitHub Actions logs
2. Verify VPS connectivity and resources
3. Review PM2 and Nginx logs
4. Validate all secret configurations

---

**🌟 Enterprise-Ready Deployment Pipeline**  
*Zero-downtime • Security-first • Performance-optimized*
