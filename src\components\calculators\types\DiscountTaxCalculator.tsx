"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";

interface DiscountResult {
  originalPrice: number;
  discountAmount: number;
  discountedPrice: number;
  taxAmount: number;
  finalPrice: number;
  totalSavings: number;
}

export default function DiscountTaxCalculator() {
  // Discount Calculator
  const [originalPrice, setOriginalPrice] = useState<number>(100);
  const [discountPercent, setDiscountPercent] = useState<number>(20);
  const [taxRate, setTaxRate] = useState<number>(8.5);
  const [applyTaxAfterDiscount, setApplyTaxAfterDiscount] = useState<boolean>(true);

  // Multiple Discounts
  const [multiPrice, setMultiPrice] = useState<number>(200);
  const [discount1, setDiscount1] = useState<number>(15);
  const [discount2, setDiscount2] = useState<number>(10);
  const [discount3, setDiscount3] = useState<number>(5);
  const [multiTaxRate, setMultiTaxRate] = useState<number>(8.5);

  // Reverse Calculator
  const [finalPrice, setFinalPrice] = useState<number>(85);
  const [knownDiscountPercent, setKnownDiscountPercent] = useState<number>(15);

  const [result, setResult] = useState<DiscountResult | null>(null);
  const [multiResult, setMultiResult] = useState<any>(null);
  const [reverseResult, setReverseResult] = useState<any>(null);

  const calculateDiscount = () => {
    const discountAmount = (originalPrice * discountPercent) / 100;
    const discountedPrice = originalPrice - discountAmount;
    
    let taxAmount: number;
    let finalPriceCalc: number;
    
    if (applyTaxAfterDiscount) {
      taxAmount = (discountedPrice * taxRate) / 100;
      finalPriceCalc = discountedPrice + taxAmount;
    } else {
      taxAmount = (originalPrice * taxRate) / 100;
      finalPriceCalc = discountedPrice + taxAmount;
    }
    
    const totalSavings = originalPrice + (originalPrice * taxRate) / 100 - finalPriceCalc;

    setResult({
      originalPrice,
      discountAmount,
      discountedPrice,
      taxAmount,
      finalPrice: finalPriceCalc,
      totalSavings
    });
  };

  const calculateMultipleDiscounts = () => {
    let currentPrice = multiPrice;
    const discounts = [discount1, discount2, discount3].filter(d => d > 0);
    const discountBreakdown: Array<{discount: number, amount: number, priceAfter: number}> = [];
    
    discounts.forEach(discount => {
      const discountAmount = (currentPrice * discount) / 100;
      currentPrice -= discountAmount;
      discountBreakdown.push({
        discount,
        amount: discountAmount,
        priceAfter: currentPrice
      });
    });
    
    const totalDiscountAmount = multiPrice - currentPrice;
    const totalDiscountPercent = (totalDiscountAmount / multiPrice) * 100;
    const taxAmount = (currentPrice * multiTaxRate) / 100;
    const finalPriceWithTax = currentPrice + taxAmount;
    
    setMultiResult({
      originalPrice: multiPrice,
      discountBreakdown,
      totalDiscountAmount,
      totalDiscountPercent,
      priceAfterDiscounts: currentPrice,
      taxAmount,
      finalPrice: finalPriceWithTax
    });
  };

  const calculateReverse = () => {
    // Calculate original price from final price and discount
    const discountMultiplier = 1 - (knownDiscountPercent / 100);
    const calculatedOriginalPrice = finalPrice / discountMultiplier;
    const discountAmount = calculatedOriginalPrice - finalPrice;
    
    setReverseResult({
      finalPrice,
      discountPercent: knownDiscountPercent,
      originalPrice: calculatedOriginalPrice,
      discountAmount,
      savings: discountAmount
    });
  };

  const reset = () => {
    setOriginalPrice(100);
    setDiscountPercent(20);
    setTaxRate(8.5);
    setApplyTaxAfterDiscount(true);
    setMultiPrice(200);
    setDiscount1(15);
    setDiscount2(10);
    setDiscount3(5);
    setMultiTaxRate(8.5);
    setFinalPrice(85);
    setKnownDiscountPercent(15);
    setResult(null);
    setMultiResult(null);
    setReverseResult(null);
  };

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount);
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Discount & Tax Calculator</CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="simple" className="space-y-6">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="simple">Simple Discount</TabsTrigger>
              <TabsTrigger value="multiple">Multiple Discounts</TabsTrigger>
              <TabsTrigger value="reverse">Reverse Calculator</TabsTrigger>
            </TabsList>

            {/* Simple Discount */}
            <TabsContent value="simple" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="original-price">Original Price</Label>
                    <Input
                      id="original-price"
                      type="number"
                      value={originalPrice}
                      onChange={(e) => setOriginalPrice(parseFloat(e.target.value) || 0)}
                      min="0"
                      step="0.01"
                    />
                  </div>

                  <div>
                    <Label htmlFor="discount-percent">Discount (%)</Label>
                    <Input
                      id="discount-percent"
                      type="number"
                      value={discountPercent}
                      onChange={(e) => setDiscountPercent(parseFloat(e.target.value) || 0)}
                      min="0"
                      max="100"
                      step="0.1"
                    />
                  </div>

                  <div>
                    <Label htmlFor="tax-rate">Tax Rate (%)</Label>
                    <Input
                      id="tax-rate"
                      type="number"
                      value={taxRate}
                      onChange={(e) => setTaxRate(parseFloat(e.target.value) || 0)}
                      min="0"
                      max="50"
                      step="0.1"
                    />
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                    <h3 className="font-semibold mb-2">Tax Application</h3>
                    <div className="space-y-2">
                      <label className="flex items-center space-x-2">
                        <input
                          type="radio"
                          checked={applyTaxAfterDiscount}
                          onChange={() => setApplyTaxAfterDiscount(true)}
                        />
                        <span className="text-sm">Apply tax after discount</span>
                      </label>
                      <label className="flex items-center space-x-2">
                        <input
                          type="radio"
                          checked={!applyTaxAfterDiscount}
                          onChange={() => setApplyTaxAfterDiscount(false)}
                        />
                        <span className="text-sm">Apply tax on original price</span>
                      </label>
                    </div>
                  </div>

                  <div className="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                    <h3 className="font-semibold mb-2">Quick Preview</h3>
                    <div className="space-y-1 text-sm">
                      <div className="flex justify-between">
                        <span>Original:</span>
                        <span>{formatCurrency(originalPrice)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Discount:</span>
                        <span>-{formatCurrency((originalPrice * discountPercent) / 100)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>After Discount:</span>
                        <span>{formatCurrency(originalPrice - (originalPrice * discountPercent) / 100)}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <Button onClick={calculateDiscount} className="w-full">
                Calculate Final Price
              </Button>

              {result && (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <Card className="bg-red-50 dark:bg-red-900/20">
                    <CardContent className="pt-6">
                      <div className="text-center space-y-2">
                        <div className="text-sm text-muted-foreground">Discount Amount</div>
                        <div className="text-xl font-bold text-red-600 dark:text-red-400">
                          -{formatCurrency(result.discountAmount)}
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="bg-blue-50 dark:bg-blue-900/20">
                    <CardContent className="pt-6">
                      <div className="text-center space-y-2">
                        <div className="text-sm text-muted-foreground">After Discount</div>
                        <div className="text-xl font-bold text-blue-600 dark:text-blue-400">
                          {formatCurrency(result.discountedPrice)}
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="bg-orange-50 dark:bg-orange-900/20">
                    <CardContent className="pt-6">
                      <div className="text-center space-y-2">
                        <div className="text-sm text-muted-foreground">Tax Amount</div>
                        <div className="text-xl font-bold text-orange-600 dark:text-orange-400">
                          +{formatCurrency(result.taxAmount)}
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="bg-green-50 dark:bg-green-900/20">
                    <CardContent className="pt-6">
                      <div className="text-center space-y-2">
                        <div className="text-sm text-muted-foreground">Final Price</div>
                        <div className="text-xl font-bold text-green-600 dark:text-green-400">
                          {formatCurrency(result.finalPrice)}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              )}

              {result && (
                <Card>
                  <CardContent className="pt-6">
                    <div className="text-center space-y-2">
                      <div className="text-sm text-muted-foreground">Total Savings</div>
                      <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                        {formatCurrency(result.totalSavings)}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        You saved {((result.totalSavings / (originalPrice + (originalPrice * taxRate) / 100)) * 100).toFixed(1)}% overall
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            {/* Multiple Discounts */}
            <TabsContent value="multiple" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="multi-price">Original Price</Label>
                    <Input
                      id="multi-price"
                      type="number"
                      value={multiPrice}
                      onChange={(e) => setMultiPrice(parseFloat(e.target.value) || 0)}
                      min="0"
                      step="0.01"
                    />
                  </div>

                  <div>
                    <Label htmlFor="discount1">First Discount (%)</Label>
                    <Input
                      id="discount1"
                      type="number"
                      value={discount1}
                      onChange={(e) => setDiscount1(parseFloat(e.target.value) || 0)}
                      min="0"
                      max="100"
                      step="0.1"
                    />
                  </div>

                  <div>
                    <Label htmlFor="discount2">Second Discount (%)</Label>
                    <Input
                      id="discount2"
                      type="number"
                      value={discount2}
                      onChange={(e) => setDiscount2(parseFloat(e.target.value) || 0)}
                      min="0"
                      max="100"
                      step="0.1"
                    />
                  </div>
                </div>

                <div className="space-y-4">
                  <div>
                    <Label htmlFor="discount3">Third Discount (%)</Label>
                    <Input
                      id="discount3"
                      type="number"
                      value={discount3}
                      onChange={(e) => setDiscount3(parseFloat(e.target.value) || 0)}
                      min="0"
                      max="100"
                      step="0.1"
                    />
                  </div>

                  <div>
                    <Label htmlFor="multi-tax-rate">Tax Rate (%)</Label>
                    <Input
                      id="multi-tax-rate"
                      type="number"
                      value={multiTaxRate}
                      onChange={(e) => setMultiTaxRate(parseFloat(e.target.value) || 0)}
                      min="0"
                      max="50"
                      step="0.1"
                    />
                  </div>

                  <div className="p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
                    <h3 className="font-semibold mb-2">Note</h3>
                    <p className="text-sm">
                      Multiple discounts are applied sequentially, not additively. 
                      Each discount is applied to the already discounted price.
                    </p>
                  </div>
                </div>
              </div>

              <Button onClick={calculateMultipleDiscounts} className="w-full">
                Calculate Multiple Discounts
              </Button>

              {multiResult && (
                <div className="space-y-4">
                  <Card>
                    <CardHeader>
                      <CardTitle>Discount Breakdown</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span>Original Price:</span>
                          <span className="font-semibold">{formatCurrency(multiResult.originalPrice)}</span>
                        </div>
                        {multiResult.discountBreakdown.map((item: any, index: number) => (
                          <div key={index} className="flex justify-between text-sm">
                            <span>Discount {index + 1} ({item.discount}%):</span>
                            <span>-{formatCurrency(item.amount)} = {formatCurrency(item.priceAfter)}</span>
                          </div>
                        ))}
                        <div className="border-t pt-2 flex justify-between">
                          <span>Total Discount:</span>
                          <span className="font-semibold text-red-600">
                            -{formatCurrency(multiResult.totalDiscountAmount)} ({multiResult.totalDiscountPercent.toFixed(1)}%)
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span>Tax ({multiTaxRate}%):</span>
                          <span>+{formatCurrency(multiResult.taxAmount)}</span>
                        </div>
                        <div className="border-t pt-2 flex justify-between text-lg">
                          <span className="font-semibold">Final Price:</span>
                          <span className="font-bold text-green-600">{formatCurrency(multiResult.finalPrice)}</span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              )}
            </TabsContent>

            {/* Reverse Calculator */}
            <TabsContent value="reverse" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="final-price">Final Price (After Discount)</Label>
                    <Input
                      id="final-price"
                      type="number"
                      value={finalPrice}
                      onChange={(e) => setFinalPrice(parseFloat(e.target.value) || 0)}
                      min="0"
                      step="0.01"
                    />
                  </div>

                  <div>
                    <Label htmlFor="known-discount">Discount Percentage (%)</Label>
                    <Input
                      id="known-discount"
                      type="number"
                      value={knownDiscountPercent}
                      onChange={(e) => setKnownDiscountPercent(parseFloat(e.target.value) || 0)}
                      min="0"
                      max="100"
                      step="0.1"
                    />
                  </div>
                </div>

                <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                  <h3 className="font-semibold mb-2">Reverse Calculator</h3>
                  <p className="text-sm">
                    Enter the final price you paid and the discount percentage 
                    to find out what the original price was.
                  </p>
                </div>
              </div>

              <Button onClick={calculateReverse} className="w-full">
                Calculate Original Price
              </Button>

              {reverseResult && (
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Card className="bg-blue-50 dark:bg-blue-900/20">
                    <CardContent className="pt-6">
                      <div className="text-center space-y-2">
                        <div className="text-sm text-muted-foreground">Original Price</div>
                        <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                          {formatCurrency(reverseResult.originalPrice)}
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="bg-red-50 dark:bg-red-900/20">
                    <CardContent className="pt-6">
                      <div className="text-center space-y-2">
                        <div className="text-sm text-muted-foreground">Discount Amount</div>
                        <div className="text-2xl font-bold text-red-600 dark:text-red-400">
                          -{formatCurrency(reverseResult.discountAmount)}
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="bg-green-50 dark:bg-green-900/20">
                    <CardContent className="pt-6">
                      <div className="text-center space-y-2">
                        <div className="text-sm text-muted-foreground">You Saved</div>
                        <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                          {formatCurrency(reverseResult.savings)}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              )}
            </TabsContent>
          </Tabs>

          <div className="flex gap-4 mt-6">
            <Button onClick={reset} variant="outline" className="flex-1">
              Reset All
            </Button>
          </div>

          {/* Tips */}
          <div className="p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
            <h3 className="font-semibold mb-2">Shopping Tips</h3>
            <ul className="text-sm space-y-1">
              <li>• Multiple discounts are applied sequentially, not added together</li>
              <li>• Tax is usually applied after all discounts</li>
              <li>• Compare final prices, not just discount percentages</li>
              <li>• Consider cashback and rewards when calculating total savings</li>
              <li>• Some stores apply discounts before tax, others after</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
