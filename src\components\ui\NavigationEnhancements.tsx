"use client";

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter, usePathname } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';
import { Button } from './button';

// Breadcrumb navigation
export function Breadcrumb({
  items,
  className
}: {
  items: Array<{ label: string; href?: string; current?: boolean }>;
  className?: string;
}) {
  return (
    <nav className={cn("flex", className)} aria-label="Breadcrumb">
      <ol className="inline-flex items-center space-x-1 md:space-x-3">
        {items.map((item, index) => (
          <li key={index} className="inline-flex items-center">
            {index > 0 && (
              <svg
                className="w-3 h-3 text-gray-400 mx-1"
                aria-hidden="true"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 5l7 7-7 7"
                />
              </svg>
            )}
            {item.href && !item.current ? (
              <Link
                href={item.href}
                className="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white transition-colors"
              >
                {index === 0 && (
                  <svg
                    className="w-3 h-3 mr-2.5"
                    aria-hidden="true"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z" />
                  </svg>
                )}
                {item.label}
              </Link>
            ) : (
              <span className="ml-1 text-sm font-medium text-gray-500 md:ml-2 dark:text-gray-400">
                {item.label}
              </span>
            )}
          </li>
        ))}
      </ol>
    </nav>
  );
}

// Recently used tools/calculators
export function RecentlyUsed({
  items,
  title = "Recently Used",
  maxItems = 5,
  className
}: {
  items: Array<{ id: string; name: string; href: string; type: 'tool' | 'calculator'; lastUsed: Date }>;
  title?: string;
  maxItems?: number;
  className?: string;
}) {
  const sortedItems = items
    .sort((a, b) => b.lastUsed.getTime() - a.lastUsed.getTime())
    .slice(0, maxItems);

  if (sortedItems.length === 0) return null;

  return (
    <div className={cn("space-y-3", className)}>
      <h3 className="text-sm font-medium text-gray-900 dark:text-gray-100">
        {title}
      </h3>
      <div className="space-y-1">
        {sortedItems.map((item) => (
          <Link
            key={item.id}
            href={item.href}
            className="flex items-center space-x-3 p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors group"
          >
            <div className={cn(
              "w-2 h-2 rounded-full",
              item.type === 'tool' ? "bg-blue-500" : "bg-green-500"
            )} />
            <span className="text-sm text-gray-700 dark:text-gray-300 group-hover:text-gray-900 dark:group-hover:text-gray-100">
              {item.name}
            </span>
            <span className="text-xs text-gray-500 ml-auto">
              {formatRelativeTime(item.lastUsed)}
            </span>
          </Link>
        ))}
      </div>
    </div>
  );
}

// Quick navigation menu
export function QuickNavigation({
  categories,
  className
}: {
  categories: Array<{
    name: string;
    href: string;
    icon?: React.ReactNode;
    count?: number;
    items?: Array<{ name: string; href: string }>;
  }>;
  className?: string;
}) {
  const [expandedCategory, setExpandedCategory] = useState<string | null>(null);

  return (
    <div className={cn("space-y-2", className)}>
      {categories.map((category) => (
        <div key={category.name}>
          <button
            onClick={() => setExpandedCategory(
              expandedCategory === category.name ? null : category.name
            )}
            className="flex items-center justify-between w-full p-2 text-left rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
          >
            <div className="flex items-center space-x-2">
              {category.icon}
              <span className="text-sm font-medium">{category.name}</span>
              {category.count && (
                <span className="text-xs bg-gray-200 dark:bg-gray-700 px-2 py-0.5 rounded-full">
                  {category.count}
                </span>
              )}
            </div>
            {category.items && (
              <svg
                className={cn(
                  "w-4 h-4 transition-transform",
                  expandedCategory === category.name ? "rotate-180" : ""
                )}
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            )}
          </button>
          
          <AnimatePresence>
            {expandedCategory === category.name && category.items && (
              <motion.div
                initial={{ height: 0, opacity: 0 }}
                animate={{ height: "auto", opacity: 1 }}
                exit={{ height: 0, opacity: 0 }}
                transition={{ duration: 0.2 }}
                className="overflow-hidden"
              >
                <div className="ml-6 mt-1 space-y-1">
                  {category.items.map((item) => (
                    <Link
                      key={item.name}
                      href={item.href}
                      className="block p-1 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 transition-colors"
                    >
                      {item.name}
                    </Link>
                  ))}
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      ))}
    </div>
  );
}

// Back button with history
export function BackButton({
  fallbackHref = '/',
  className
}: {
  fallbackHref?: string;
  className?: string;
}) {
  const router = useRouter();
  const [canGoBack, setCanGoBack] = useState(false);

  useEffect(() => {
    setCanGoBack(window.history.length > 1);
  }, []);

  const handleBack = () => {
    if (canGoBack) {
      router.back();
    } else {
      router.push(fallbackHref);
    }
  };

  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={handleBack}
      className={cn("flex items-center space-x-2", className)}
    >
      <svg
        className="w-4 h-4"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M15 19l-7-7 7-7"
        />
      </svg>
      <span>Back</span>
    </Button>
  );
}

// Page navigation with keyboard shortcuts
export function PageNavigation({
  previousPage,
  nextPage,
  className
}: {
  previousPage?: { title: string; href: string };
  nextPage?: { title: string; href: string };
  className?: string;
}) {
  const router = useRouter();

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.altKey) {
        if (event.key === 'ArrowLeft' && previousPage) {
          event.preventDefault();
          router.push(previousPage.href);
        } else if (event.key === 'ArrowRight' && nextPage) {
          event.preventDefault();
          router.push(nextPage.href);
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [router, previousPage, nextPage]);

  if (!previousPage && !nextPage) return null;

  return (
    <div className={cn("flex justify-between items-center", className)}>
      {previousPage ? (
        <Link
          href={previousPage.href}
          className="flex items-center space-x-2 p-3 rounded-lg border hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors group"
        >
          <svg
            className="w-4 h-4 text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-300"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
          </svg>
          <div>
            <div className="text-xs text-gray-500">Previous</div>
            <div className="text-sm font-medium">{previousPage.title}</div>
          </div>
        </Link>
      ) : (
        <div />
      )}

      {nextPage && (
        <Link
          href={nextPage.href}
          className="flex items-center space-x-2 p-3 rounded-lg border hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors group text-right"
        >
          <div>
            <div className="text-xs text-gray-500">Next</div>
            <div className="text-sm font-medium">{nextPage.title}</div>
          </div>
          <svg
            className="w-4 h-4 text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-300"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
          </svg>
        </Link>
      )}
    </div>
  );
}

// Floating action button for quick access
export function FloatingActionButton({
  actions,
  className
}: {
  actions: Array<{
    label: string;
    icon: React.ReactNode;
    onClick: () => void;
    color?: 'primary' | 'secondary' | 'success' | 'warning' | 'danger';
  }>;
  className?: string;
}) {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className={cn("fixed bottom-6 right-6 z-40", className)}>
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            className="absolute bottom-16 right-0 space-y-2"
          >
            {actions.map((action, index) => (
              <motion.button
                key={index}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: 20 }}
                transition={{ delay: index * 0.1 }}
                onClick={() => {
                  action.onClick();
                  setIsOpen(false);
                }}
                className="flex items-center space-x-2 bg-white dark:bg-gray-800 shadow-lg rounded-full px-4 py-2 hover:shadow-xl transition-shadow"
              >
                {action.icon}
                <span className="text-sm font-medium">{action.label}</span>
              </motion.button>
            ))}
          </motion.div>
        )}
      </AnimatePresence>

      <button
        onClick={() => setIsOpen(!isOpen)}
        className="w-14 h-14 bg-primary text-primary-foreground rounded-full shadow-lg hover:shadow-xl transition-shadow flex items-center justify-center"
      >
        <motion.svg
          animate={{ rotate: isOpen ? 45 : 0 }}
          className="w-6 h-6"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
        </motion.svg>
      </button>
    </div>
  );
}

// Helper function to format relative time
function formatRelativeTime(date: Date): string {
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

  if (diffInSeconds < 60) return 'Just now';
  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
  if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d ago`;
  
  return date.toLocaleDateString();
}
