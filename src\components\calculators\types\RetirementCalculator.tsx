"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

interface RetirementResult {
  totalSavings: number;
  totalContributions: number;
  totalInterest: number;
  monthlyIncomeNeeded: number;
  monthlyIncomeGenerated: number;
  shortfall: number;
  recommendedSavings: number;
}

export default function RetirementCalculator() {
  const [currentAge, setCurrentAge] = useState<number>(30);
  const [retirementAge, setRetirementAge] = useState<number>(65);
  const [currentSavings, setCurrentSavings] = useState<number>(50000);
  const [monthlyContribution, setMonthlyContribution] = useState<number>(1000);
  const [expectedReturn, setExpectedReturn] = useState<number>(7);
  const [currentIncome, setCurrentIncome] = useState<number>(75000);
  const [incomeReplacement, setIncomeReplacement] = useState<number>(80);
  const [inflationRate, setInflationRate] = useState<number>(3);
  const [result, setResult] = useState<RetirementResult | null>(null);

  const calculateRetirement = () => {
    const yearsToRetirement = retirementAge - currentAge;
    const monthsToRetirement = yearsToRetirement * 12;
    const monthlyReturn = expectedReturn / 100 / 12;
    const monthlyInflation = inflationRate / 100 / 12;

    // Future value of current savings
    const futureValueCurrentSavings = currentSavings * Math.pow(1 + expectedReturn / 100, yearsToRetirement);

    // Future value of monthly contributions
    let futureValueContributions = 0;
    if (monthlyReturn > 0) {
      futureValueContributions = monthlyContribution * 
        ((Math.pow(1 + monthlyReturn, monthsToRetirement) - 1) / monthlyReturn);
    } else {
      futureValueContributions = monthlyContribution * monthsToRetirement;
    }

    const totalSavings = futureValueCurrentSavings + futureValueContributions;
    const totalContributions = currentSavings + (monthlyContribution * monthsToRetirement);
    const totalInterest = totalSavings - totalContributions;

    // Calculate income needs
    const futureIncomeNeeded = currentIncome * (incomeReplacement / 100) * 
      Math.pow(1 + inflationRate / 100, yearsToRetirement);
    const monthlyIncomeNeeded = futureIncomeNeeded / 12;

    // Assume 4% withdrawal rate in retirement
    const monthlyIncomeGenerated = (totalSavings * 0.04) / 12;
    const shortfall = Math.max(0, monthlyIncomeNeeded - monthlyIncomeGenerated);

    // Calculate recommended monthly savings to meet goal
    const targetSavings = (monthlyIncomeNeeded * 12) / 0.04;
    const neededFromContributions = targetSavings - futureValueCurrentSavings;
    let recommendedSavings = 0;
    
    if (monthlyReturn > 0 && neededFromContributions > 0) {
      recommendedSavings = neededFromContributions * monthlyReturn / 
        (Math.pow(1 + monthlyReturn, monthsToRetirement) - 1);
    }

    setResult({
      totalSavings,
      totalContributions,
      totalInterest,
      monthlyIncomeNeeded,
      monthlyIncomeGenerated,
      shortfall,
      recommendedSavings: Math.max(0, recommendedSavings)
    });
  };

  const reset = () => {
    setCurrentAge(30);
    setRetirementAge(65);
    setCurrentSavings(50000);
    setMonthlyContribution(1000);
    setExpectedReturn(7);
    setCurrentIncome(75000);
    setIncomeReplacement(80);
    setInflationRate(3);
    setResult(null);
  };

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const getRetirementReadiness = (): { status: string; color: string; message: string } => {
    if (!result) return { status: "Unknown", color: "gray", message: "" };
    
    if (result.shortfall <= 0) {
      return { 
        status: "On Track", 
        color: "green", 
        message: "You're on track to meet your retirement goals!" 
      };
    } else if (result.shortfall <= result.monthlyIncomeNeeded * 0.2) {
      return { 
        status: "Close", 
        color: "yellow", 
        message: "You're close to your goal. Consider increasing contributions." 
      };
    } else {
      return { 
        status: "Behind", 
        color: "red", 
        message: "You may need to significantly increase savings or adjust expectations." 
      };
    }
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Retirement Savings Calculator</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Input Section */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div>
                <Label htmlFor="current-age">Current Age</Label>
                <Input
                  id="current-age"
                  type="number"
                  value={currentAge}
                  onChange={(e) => setCurrentAge(parseInt(e.target.value) || 0)}
                  min="18"
                  max="80"
                />
              </div>

              <div>
                <Label htmlFor="retirement-age">Retirement Age</Label>
                <Input
                  id="retirement-age"
                  type="number"
                  value={retirementAge}
                  onChange={(e) => setRetirementAge(parseInt(e.target.value) || 0)}
                  min="50"
                  max="80"
                />
              </div>

              <div>
                <Label htmlFor="current-savings">Current Retirement Savings</Label>
                <Input
                  id="current-savings"
                  type="number"
                  value={currentSavings}
                  onChange={(e) => setCurrentSavings(parseFloat(e.target.value) || 0)}
                  min="0"
                  step="1000"
                />
              </div>

              <div>
                <Label htmlFor="monthly-contribution">Monthly Contribution</Label>
                <Input
                  id="monthly-contribution"
                  type="number"
                  value={monthlyContribution}
                  onChange={(e) => setMonthlyContribution(parseFloat(e.target.value) || 0)}
                  min="0"
                  step="50"
                />
              </div>
            </div>

            <div className="space-y-4">
              <div>
                <Label htmlFor="expected-return">Expected Annual Return (%)</Label>
                <Input
                  id="expected-return"
                  type="number"
                  value={expectedReturn}
                  onChange={(e) => setExpectedReturn(parseFloat(e.target.value) || 0)}
                  min="0"
                  max="15"
                  step="0.1"
                />
              </div>

              <div>
                <Label htmlFor="current-income">Current Annual Income</Label>
                <Input
                  id="current-income"
                  type="number"
                  value={currentIncome}
                  onChange={(e) => setCurrentIncome(parseFloat(e.target.value) || 0)}
                  min="0"
                  step="1000"
                />
              </div>

              <div>
                <Label htmlFor="income-replacement">Income Replacement (%)</Label>
                <Input
                  id="income-replacement"
                  type="number"
                  value={incomeReplacement}
                  onChange={(e) => setIncomeReplacement(parseFloat(e.target.value) || 0)}
                  min="50"
                  max="100"
                  step="5"
                />
              </div>

              <div>
                <Label htmlFor="inflation-rate">Expected Inflation Rate (%)</Label>
                <Input
                  id="inflation-rate"
                  type="number"
                  value={inflationRate}
                  onChange={(e) => setInflationRate(parseFloat(e.target.value) || 0)}
                  min="0"
                  max="10"
                  step="0.1"
                />
              </div>
            </div>
          </div>

          {/* Summary */}
          <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
            <h3 className="font-semibold mb-2">Planning Summary</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <span className="text-muted-foreground">Years to retirement:</span>
                <div className="font-semibold">{retirementAge - currentAge} years</div>
              </div>
              <div>
                <span className="text-muted-foreground">Annual contributions:</span>
                <div className="font-semibold">{formatCurrency(monthlyContribution * 12)}</div>
              </div>
              <div>
                <span className="text-muted-foreground">Target income:</span>
                <div className="font-semibold">{formatCurrency(currentIncome * incomeReplacement / 100)}</div>
              </div>
              <div>
                <span className="text-muted-foreground">Contribution rate:</span>
                <div className="font-semibold">{((monthlyContribution * 12 / currentIncome) * 100).toFixed(1)}%</div>
              </div>
            </div>
          </div>

          {/* Calculate Button */}
          <div className="flex gap-4">
            <Button onClick={calculateRetirement} className="flex-1">
              Calculate Retirement Plan
            </Button>
            <Button onClick={reset} variant="outline">
              Reset
            </Button>
          </div>

          {/* Results */}
          {result && (
            <div className="space-y-6">
              {/* Status Card */}
              <Card className={`bg-${getRetirementReadiness().color}-50 dark:bg-${getRetirementReadiness().color}-900/20`}>
                <CardContent className="pt-6">
                  <div className="text-center space-y-2">
                    <div className="text-sm text-muted-foreground">Retirement Readiness</div>
                    <div className={`text-2xl font-bold text-${getRetirementReadiness().color}-600 dark:text-${getRetirementReadiness().color}-400`}>
                      {getRetirementReadiness().status}
                    </div>
                    <div className="text-sm">{getRetirementReadiness().message}</div>
                  </div>
                </CardContent>
              </Card>

              {/* Summary Cards */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card className="bg-green-50 dark:bg-green-900/20">
                  <CardContent className="pt-6">
                    <div className="text-center space-y-2">
                      <div className="text-sm text-muted-foreground">Total at Retirement</div>
                      <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                        {formatCurrency(result.totalSavings)}
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-blue-50 dark:bg-blue-900/20">
                  <CardContent className="pt-6">
                    <div className="text-center space-y-2">
                      <div className="text-sm text-muted-foreground">Monthly Income Generated</div>
                      <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                        {formatCurrency(result.monthlyIncomeGenerated)}
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-purple-50 dark:bg-purple-900/20">
                  <CardContent className="pt-6">
                    <div className="text-center space-y-2">
                      <div className="text-sm text-muted-foreground">Monthly Income Needed</div>
                      <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                        {formatCurrency(result.monthlyIncomeNeeded)}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Detailed Breakdown */}
              <Card>
                <CardHeader>
                  <CardTitle>Detailed Breakdown</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span>Total Contributions:</span>
                        <span className="font-semibold">{formatCurrency(result.totalContributions)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Investment Growth:</span>
                        <span className="font-semibold text-green-600">{formatCurrency(result.totalInterest)}</span>
                      </div>
                      <div className="flex justify-between border-t pt-2">
                        <span>Total at Retirement:</span>
                        <span className="font-bold">{formatCurrency(result.totalSavings)}</span>
                      </div>
                    </div>
                    
                    <div className="space-y-3">
                      {result.shortfall > 0 && (
                        <>
                          <div className="flex justify-between">
                            <span>Monthly Shortfall:</span>
                            <span className="font-semibold text-red-600">{formatCurrency(result.shortfall)}</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Recommended Monthly Savings:</span>
                            <span className="font-semibold text-blue-600">{formatCurrency(result.recommendedSavings)}</span>
                          </div>
                        </>
                      )}
                      <div className="flex justify-between">
                        <span>Return on Investment:</span>
                        <span className="font-semibold">
                          {((result.totalInterest / result.totalContributions) * 100).toFixed(1)}%
                        </span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* Tips */}
          <div className="p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
            <h3 className="font-semibold mb-2">Retirement Planning Tips</h3>
            <ul className="text-sm space-y-1">
              <li>• Start early to maximize compound interest benefits</li>
              <li>• Aim to save 10-15% of your income for retirement</li>
              <li>• Take advantage of employer 401(k) matching</li>
              <li>• Consider Roth vs Traditional retirement accounts</li>
              <li>• Review and adjust your plan annually</li>
              <li>• Factor in Social Security and pension benefits</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
