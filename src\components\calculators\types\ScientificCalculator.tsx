"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

export default function ScientificCalculator() {
  const [display, setDisplay] = useState<string>("0");
  const [previousValue, setPreviousValue] = useState<number | null>(null);
  const [operation, setOperation] = useState<string | null>(null);
  const [waitingForOperand, setWaitingForOperand] = useState<boolean>(false);
  const [angleMode, setAngleMode] = useState<"deg" | "rad">("deg");
  const [memory, setMemory] = useState<number>(0);

  const inputNumber = (num: string) => {
    if (waitingForOperand) {
      setDisplay(num);
      setWaitingForOperand(false);
    } else {
      setDisplay(display === "0" ? num : display + num);
    }
  };

  const inputDecimal = () => {
    if (waitingForOperand) {
      setDisplay("0.");
      setWaitingForOperand(false);
    } else if (display.indexOf(".") === -1) {
      setDisplay(display + ".");
    }
  };

  const clear = () => {
    setDisplay("0");
    setPreviousValue(null);
    setOperation(null);
    setWaitingForOperand(false);
  };

  const clearEntry = () => {
    setDisplay("0");
  };

  const performOperation = (nextOperation: string) => {
    const inputValue = parseFloat(display);

    if (previousValue === null) {
      setPreviousValue(inputValue);
    } else if (operation) {
      const currentValue = previousValue || 0;
      const newValue = calculate(currentValue, inputValue, operation);

      setDisplay(String(newValue));
      setPreviousValue(newValue);
    }

    setWaitingForOperand(true);
    setOperation(nextOperation);
  };

  const calculate = (firstValue: number, secondValue: number, operation: string): number => {
    switch (operation) {
      case "+":
        return firstValue + secondValue;
      case "-":
        return firstValue - secondValue;
      case "*":
        return firstValue * secondValue;
      case "/":
        return secondValue !== 0 ? firstValue / secondValue : 0;
      case "^":
        return Math.pow(firstValue, secondValue);
      case "mod":
        return firstValue % secondValue;
      default:
        return secondValue;
    }
  };

  const performScientificOperation = (func: string) => {
    const value = parseFloat(display);
    let result: number;

    switch (func) {
      case "sin":
        result = Math.sin(angleMode === "deg" ? (value * Math.PI) / 180 : value);
        break;
      case "cos":
        result = Math.cos(angleMode === "deg" ? (value * Math.PI) / 180 : value);
        break;
      case "tan":
        result = Math.tan(angleMode === "deg" ? (value * Math.PI) / 180 : value);
        break;
      case "asin":
        result = Math.asin(value);
        if (angleMode === "deg") result = (result * 180) / Math.PI;
        break;
      case "acos":
        result = Math.acos(value);
        if (angleMode === "deg") result = (result * 180) / Math.PI;
        break;
      case "atan":
        result = Math.atan(value);
        if (angleMode === "deg") result = (result * 180) / Math.PI;
        break;
      case "log":
        result = Math.log10(value);
        break;
      case "ln":
        result = Math.log(value);
        break;
      case "sqrt":
        result = Math.sqrt(value);
        break;
      case "square":
        result = value * value;
        break;
      case "cube":
        result = value * value * value;
        break;
      case "factorial":
        result = factorial(Math.floor(value));
        break;
      case "reciprocal":
        result = value !== 0 ? 1 / value : 0;
        break;
      case "abs":
        result = Math.abs(value);
        break;
      case "exp":
        result = Math.exp(value);
        break;
      case "pi":
        result = Math.PI;
        break;
      case "e":
        result = Math.E;
        break;
      default:
        result = value;
    }

    setDisplay(String(result));
    setWaitingForOperand(true);
  };

  const factorial = (n: number): number => {
    if (n < 0) return NaN;
    if (n === 0 || n === 1) return 1;
    let result = 1;
    for (let i = 2; i <= n; i++) {
      result *= i;
    }
    return result;
  };

  const memoryStore = () => {
    setMemory(parseFloat(display));
  };

  const memoryRecall = () => {
    setDisplay(String(memory));
    setWaitingForOperand(true);
  };

  const memoryClear = () => {
    setMemory(0);
  };

  const memoryAdd = () => {
    setMemory(memory + parseFloat(display));
  };

  const memorySubtract = () => {
    setMemory(memory - parseFloat(display));
  };

  const formatDisplay = (value: string): string => {
    const num = parseFloat(value);
    if (isNaN(num)) return "Error";
    if (Math.abs(num) > 1e15 || (Math.abs(num) < 1e-6 && num !== 0)) {
      return num.toExponential(6);
    }
    return num.toString();
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Scientific Calculator</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Display */}
          <div className="bg-black text-white p-4 rounded text-right text-2xl font-mono min-h-[60px] flex items-center justify-end">
            {formatDisplay(display)}
          </div>

          {/* Angle Mode and Memory Status */}
          <div className="flex justify-between items-center">
            <div className="flex items-center space-x-2">
              <span className="text-sm">Angle Mode:</span>
              <Select value={angleMode} onValueChange={(value: "deg" | "rad") => setAngleMode(value)}>
                <SelectTrigger className="w-20">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="deg">DEG</SelectItem>
                  <SelectItem value="rad">RAD</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="text-sm">
              Memory: {memory !== 0 ? memory.toFixed(4) : "0"}
            </div>
          </div>

          {/* Button Grid */}
          <div className="grid grid-cols-6 gap-2">
            {/* Row 1 - Memory and Clear */}
            <Button onClick={memoryClear} variant="outline" className="text-xs">MC</Button>
            <Button onClick={memoryRecall} variant="outline" className="text-xs">MR</Button>
            <Button onClick={memoryStore} variant="outline" className="text-xs">MS</Button>
            <Button onClick={memoryAdd} variant="outline" className="text-xs">M+</Button>
            <Button onClick={memorySubtract} variant="outline" className="text-xs">M-</Button>
            <Button onClick={clear} variant="destructive">C</Button>

            {/* Row 2 - Scientific Functions */}
            <Button onClick={() => performScientificOperation("sin")} variant="outline" className="text-xs">sin</Button>
            <Button onClick={() => performScientificOperation("cos")} variant="outline" className="text-xs">cos</Button>
            <Button onClick={() => performScientificOperation("tan")} variant="outline" className="text-xs">tan</Button>
            <Button onClick={() => performScientificOperation("log")} variant="outline" className="text-xs">log</Button>
            <Button onClick={() => performScientificOperation("ln")} variant="outline" className="text-xs">ln</Button>
            <Button onClick={clearEntry} variant="outline">CE</Button>

            {/* Row 3 - More Scientific Functions */}
            <Button onClick={() => performScientificOperation("asin")} variant="outline" className="text-xs">asin</Button>
            <Button onClick={() => performScientificOperation("acos")} variant="outline" className="text-xs">acos</Button>
            <Button onClick={() => performScientificOperation("atan")} variant="outline" className="text-xs">atan</Button>
            <Button onClick={() => performScientificOperation("sqrt")} variant="outline" className="text-xs">√</Button>
            <Button onClick={() => performScientificOperation("square")} variant="outline" className="text-xs">x²</Button>
            <Button onClick={() => performOperation("^")} variant="outline" className="text-xs">x^y</Button>

            {/* Row 4 - Constants and Functions */}
            <Button onClick={() => performScientificOperation("pi")} variant="outline" className="text-xs">π</Button>
            <Button onClick={() => performScientificOperation("e")} variant="outline" className="text-xs">e</Button>
            <Button onClick={() => performScientificOperation("factorial")} variant="outline" className="text-xs">n!</Button>
            <Button onClick={() => performScientificOperation("reciprocal")} variant="outline" className="text-xs">1/x</Button>
            <Button onClick={() => performScientificOperation("abs")} variant="outline" className="text-xs">|x|</Button>
            <Button onClick={() => performOperation("mod")} variant="outline" className="text-xs">mod</Button>

            {/* Row 5 - Numbers and Operations */}
            <Button onClick={() => inputNumber("7")}>7</Button>
            <Button onClick={() => inputNumber("8")}>8</Button>
            <Button onClick={() => inputNumber("9")}>9</Button>
            <Button onClick={() => performOperation("/")} variant="outline">÷</Button>
            <Button onClick={() => inputNumber("(")} variant="outline">(</Button>
            <Button onClick={() => inputNumber(")")} variant="outline">)</Button>

            {/* Row 6 */}
            <Button onClick={() => inputNumber("4")}>4</Button>
            <Button onClick={() => inputNumber("5")}>5</Button>
            <Button onClick={() => inputNumber("6")}>6</Button>
            <Button onClick={() => performOperation("*")} variant="outline">×</Button>
            <Button onClick={() => performScientificOperation("exp")} variant="outline" className="text-xs">exp</Button>
            <Button onClick={() => performScientificOperation("cube")} variant="outline" className="text-xs">x³</Button>

            {/* Row 7 */}
            <Button onClick={() => inputNumber("1")}>1</Button>
            <Button onClick={() => inputNumber("2")}>2</Button>
            <Button onClick={() => inputNumber("3")}>3</Button>
            <Button onClick={() => performOperation("-")} variant="outline">-</Button>
            <Button onClick={() => setDisplay(display.startsWith("-") ? display.slice(1) : "-" + display)} variant="outline">±</Button>
            <Button onClick={() => performOperation("=")} className="bg-blue-600 hover:bg-blue-700">=</Button>

            {/* Row 8 */}
            <Button onClick={() => inputNumber("0")} className="col-span-2">0</Button>
            <Button onClick={inputDecimal}>.</Button>
            <Button onClick={() => performOperation("+")} variant="outline">+</Button>
            <Button onClick={() => setDisplay(display.slice(0, -1) || "0")} variant="outline">⌫</Button>
            <Button onClick={() => performOperation("=")} className="bg-blue-600 hover:bg-blue-700">=</Button>
          </div>

          {/* Instructions */}
          <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
            <h3 className="font-semibold mb-2">Scientific Calculator Features</h3>
            <ul className="text-sm space-y-1">
              <li>• Trigonometric functions (sin, cos, tan, asin, acos, atan)</li>
              <li>• Logarithmic functions (log, ln, exp)</li>
              <li>• Power functions (x², x³, x^y, √x)</li>
              <li>• Memory functions (MC, MR, MS, M+, M-)</li>
              <li>• Constants (π, e) and factorial (n!)</li>
              <li>• Switch between degrees and radians</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
