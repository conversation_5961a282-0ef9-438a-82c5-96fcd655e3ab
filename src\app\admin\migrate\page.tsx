'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, CheckCircle, XCircle } from 'lucide-react';

export default function MigratePage() {
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  const runMigration = async () => {
    setIsLoading(true);
    setError(null);
    setResult(null);

    try {
      const response = await fetch('/api/migrate/categories', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();

      if (data.success) {
        setResult(data);
      } else {
        setError(data.error || 'Migration failed');
      }
    } catch (err: any) {
      setError(err.message || 'An error occurred during migration');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle className="h-6 w-6 text-green-600" />
            Blog Category Migration
          </CardTitle>
          <CardDescription>
            This tool will assign categories to blog posts that don&rsquo;t have them and update category counts.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <h3 className="text-lg font-semibold">What this migration does:</h3>
            <ul className="list-disc list-inside space-y-1 text-sm text-muted-foreground">
              <li>Creates default categories (Technology, Health, General) if none exist</li>
              <li>Assigns categories to posts based on content keywords</li>
              <li>Updates category post counts</li>
              <li>Ensures all posts have proper category relationships</li>
            </ul>
          </div>

          <Button 
            onClick={runMigration} 
            disabled={isLoading}
            className="w-full"
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Running Migration...
              </>
            ) : (
              'Run Category Migration'
            )}
          </Button>

          {error && (
            <Alert variant="destructive">
              <XCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {result && (
            <Alert>
              <CheckCircle className="h-4 w-4" />
              <AlertDescription>
                <div className="space-y-2">
                  <p className="font-semibold">{result.message}</p>
                  <p>Posts updated: {result.updated}</p>
                  {result.categoryUpdates && (
                    <div>
                      <p className="font-medium">Category counts:</p>
                      <ul className="list-disc list-inside ml-4">
                        {result.categoryUpdates.map((cat: any, index: number) => (
                          <li key={index}>
                            {cat.name}: {cat.count} posts
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              </AlertDescription>
            </Alert>
          )}

          <div className="mt-6 p-4 bg-muted rounded-lg">
            <h4 className="font-semibold mb-2">After running the migration:</h4>
            <ol className="list-decimal list-inside space-y-1 text-sm">
              <li>Go to the blog pages to see the updated categories</li>
              <li>Check that category badges appear on blog cards</li>
              <li>Verify that category counts are correct in the sidebar</li>
              <li>Test category filtering functionality</li>
            </ol>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
