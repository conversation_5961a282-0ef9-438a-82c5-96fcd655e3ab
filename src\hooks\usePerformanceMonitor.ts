"use client";

import { useEffect, useRef, useCallback } from 'react';

interface PerformanceMetrics {
  loadTime: number;
  renderTime: number;
  interactionTime: number;
  memoryUsage?: {
    used: number;
    total: number;
    limit: number;
  };
}

interface PerformanceMonitorOptions {
  trackMemory?: boolean;
  trackInteractions?: boolean;
  onMetricsUpdate?: (metrics: PerformanceMetrics) => void;
  componentName?: string;
}

export function usePerformanceMonitor(options: PerformanceMonitorOptions = {}) {
  const {
    trackMemory = false,
    trackInteractions = true,
    onMetricsUpdate,
    componentName = 'Unknown'
  } = options;

  const metricsRef = useRef<PerformanceMetrics>({
    loadTime: 0,
    renderTime: 0,
    interactionTime: 0
  });

  const startTimeRef = useRef<number>(performance.now());
  const renderStartRef = useRef<number>(performance.now());
  const interactionStartRef = useRef<number | null>(null);

  // Measure component render time
  const measureRenderTime = useCallback(() => {
    const renderTime = performance.now() - renderStartRef.current;
    metricsRef.current.renderTime = renderTime;
    
    if (process.env.NODE_ENV === 'development') {
      console.log(`[Performance] ${componentName} render time: ${renderTime.toFixed(2)}ms`);
    }
  }, [componentName]);

  // Measure memory usage
  const measureMemoryUsage = useCallback(() => {
    if (!trackMemory || typeof window === 'undefined') return;

    if ('memory' in performance) {
      const memory = (performance as any).memory;
      metricsRef.current.memoryUsage = {
        used: memory.usedJSHeapSize,
        total: memory.totalJSHeapSize,
        limit: memory.jsHeapSizeLimit
      };
    }
  }, [trackMemory]);

  // Start interaction timing
  const startInteractionTiming = useCallback(() => {
    if (!trackInteractions) return;
    interactionStartRef.current = performance.now();
  }, [trackInteractions]);

  // End interaction timing
  const endInteractionTiming = useCallback((interactionType: string = 'unknown') => {
    if (!trackInteractions || interactionStartRef.current === null) return;
    
    const interactionTime = performance.now() - interactionStartRef.current;
    metricsRef.current.interactionTime = interactionTime;
    interactionStartRef.current = null;
    
    if (process.env.NODE_ENV === 'development') {
      console.log(`[Performance] ${componentName} ${interactionType} interaction: ${interactionTime.toFixed(2)}ms`);
    }
    
    // Call callback if provided
    if (onMetricsUpdate) {
      onMetricsUpdate({ ...metricsRef.current });
    }
  }, [trackInteractions, componentName, onMetricsUpdate]);

  // Measure Core Web Vitals
  const measureCoreWebVitals = useCallback(() => {
    if (typeof window === 'undefined') return;

    // Largest Contentful Paint (LCP)
    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      const lastEntry = entries[entries.length - 1];
      
      if (process.env.NODE_ENV === 'development') {
        console.log(`[Performance] LCP: ${lastEntry.startTime.toFixed(2)}ms`);
      }
    });

    try {
      observer.observe({ entryTypes: ['largest-contentful-paint'] });
    } catch (error) {
      // LCP not supported
    }

    // First Input Delay (FID) - approximation
    const handleFirstInput = (event: Event) => {
      const fid = performance.now() - (event as any).timeStamp;
      
      if (process.env.NODE_ENV === 'development') {
        console.log(`[Performance] FID: ${fid.toFixed(2)}ms`);
      }
      
      // Remove listener after first interaction
      ['mousedown', 'keydown', 'touchstart', 'pointerdown'].forEach(type => {
        document.removeEventListener(type, handleFirstInput, true);
      });
    };

    ['mousedown', 'keydown', 'touchstart', 'pointerdown'].forEach(type => {
      document.addEventListener(type, handleFirstInput, { once: true, capture: true });
    });

    return () => {
      observer.disconnect();
      ['mousedown', 'keydown', 'touchstart', 'pointerdown'].forEach(type => {
        document.removeEventListener(type, handleFirstInput, true);
      });
    };
  }, []);

  // Track long tasks
  const trackLongTasks = useCallback(() => {
    if (typeof window === 'undefined') return;

    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      entries.forEach(entry => {
        if (process.env.NODE_ENV === 'development') {
          console.warn(`[Performance] Long task detected: ${entry.duration.toFixed(2)}ms`);
        }
      });
    });

    try {
      observer.observe({ entryTypes: ['longtask'] });
      return () => observer.disconnect();
    } catch (error) {
      // Long tasks not supported
      return () => {};
    }
  }, []);

  // Initialize performance monitoring
  useEffect(() => {
    const loadTime = performance.now() - startTimeRef.current;
    metricsRef.current.loadTime = loadTime;

    if (process.env.NODE_ENV === 'development') {
      console.log(`[Performance] ${componentName} load time: ${loadTime.toFixed(2)}ms`);
    }

    measureRenderTime();
    measureMemoryUsage();
    
    const cleanupWebVitals = measureCoreWebVitals();
    const cleanupLongTasks = trackLongTasks();

    return () => {
      cleanupWebVitals?.();
      cleanupLongTasks?.();
    };
  }, [componentName, measureRenderTime, measureMemoryUsage, measureCoreWebVitals, trackLongTasks]);

  // Update metrics periodically if tracking memory
  useEffect(() => {
    if (!trackMemory) return;

    const interval = setInterval(() => {
      measureMemoryUsage();
      if (onMetricsUpdate) {
        onMetricsUpdate({ ...metricsRef.current });
      }
    }, 5000); // Every 5 seconds

    return () => clearInterval(interval);
  }, [trackMemory, measureMemoryUsage, onMetricsUpdate]);

  // Performance budget warnings
  const checkPerformanceBudget = useCallback(() => {
    const metrics = metricsRef.current;
    
    // Warn if render time exceeds budget
    if (metrics.renderTime > 100) {
      console.warn(`[Performance Budget] ${componentName} render time (${metrics.renderTime.toFixed(2)}ms) exceeds budget (100ms)`);
    }
    
    // Warn if interaction time exceeds budget
    if (metrics.interactionTime > 100) {
      console.warn(`[Performance Budget] ${componentName} interaction time (${metrics.interactionTime.toFixed(2)}ms) exceeds budget (100ms)`);
    }
    
    // Warn if memory usage is high
    if (metrics.memoryUsage && metrics.memoryUsage.used > metrics.memoryUsage.limit * 0.8) {
      console.warn(`[Performance Budget] High memory usage: ${(metrics.memoryUsage.used / 1024 / 1024).toFixed(2)}MB`);
    }
  }, [componentName]);

  return {
    metrics: metricsRef.current,
    startInteractionTiming,
    endInteractionTiming,
    measureMemoryUsage,
    checkPerformanceBudget,
    
    // Utility functions
    markStart: (name: string) => performance.mark(`${componentName}-${name}-start`),
    markEnd: (name: string) => {
      performance.mark(`${componentName}-${name}-end`);
      performance.measure(
        `${componentName}-${name}`,
        `${componentName}-${name}-start`,
        `${componentName}-${name}-end`
      );
    }
  };
}
