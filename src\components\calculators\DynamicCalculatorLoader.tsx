"use client";

import { lazy, Suspense, ComponentType } from 'react';
import { CalculatorSkeleton } from './CalculatorSkeleton';

// Dynamic imports for all calculators to enable code splitting
const calculatorComponents: Record<string, () => Promise<{ default: ComponentType<any> }>> = {
  'bmi-calculator': () => import('./types/BMICalculator'),
  'percentage-calculator': () => import('./types/PercentageCalculator'),
  'tip-calculator': () => import('./types/TipCalculator'),
  'mortgage-calculator': () => import('./types/MortgageCalculator'),
  'compound-interest-calculator': () => import('./types/CompoundInterestCalculator'),
  'unit-converter': () => import('./types/UnitConverter'),
  'ai-token-calculator': () => import('./types/AITokenCalculator'),
  'age-calculator': () => import('./types/AgeCalculator'),
  'bmr-calculator': () => import('./types/BMRCalculator'),
  'calorie-calculator': () => import('./types/CalorieCalculator'),
  'calories-burned-calculator': () => import('./types/CaloriesBurnedCalculator'),
  'date-difference-calculator': () => import('./types/DateDifferenceCalculator'),
  'emi-calculator': () => import('./types/EMICalculator'),
  'gpa-calculator': () => import('./types/GPACalculator'),
  'loan-calculator': () => import('./types/LoanCalculator'),
  'sip-calculator': () => import('./types/SIPCalculator'),
  'text-tools-calculator': () => import('./types/TextToolsCalculator'),
  
  // Newly implemented calculators
  'fraction-calculator': () => import('./types/FractionCalculator'),
  'scientific-calculator': () => import('./types/ScientificCalculator'),
  'statistics-calculator': () => import('./types/StatisticsCalculator'),
  'probability-calculator': () => import('./types/ProbabilityCalculator'),
  'investment-calculator': () => import('./types/InvestmentCalculator'),
  'car-loan-calculator': () => import('./types/CarLoanCalculator'),
  'salary-hourly-calculator': () => import('./types/SalaryHourlyCalculator'),
  'discount-tax-calculator': () => import('./types/DiscountTaxCalculator'),
  'retirement-calculator': () => import('./types/RetirementCalculator'),
  'mortgage-affordability-calculator': () => import('./types/MortgageAffordabilityCalculator'),
  'body-fat-calculator': () => import('./types/BodyFatCalculator'),
  'water-intake-calculator': () => import('./types/WaterIntakeCalculator'),
  'pregnancy-calculator': () => import('./types/PregnancyCalculator'),
  'temperature-converter': () => import('./types/TemperatureConverter'),
  'timezone-converter': () => import('./types/TimeZoneConverter'),
  'currency-converter': () => import('./types/CurrencyConverter'),
  'freelancer-rate-calculator': () => import('./types/FreelancerRateCalculator'),
};

// Create lazy components with proper error boundaries
const createLazyCalculator = (importFn: () => Promise<{ default: ComponentType<any> }>) => {
  return lazy(() =>
    importFn()
      .then(module => {
        // Ensure we have a default export
        if (module.default) {
          return module;
        }
        // If no default export, try to find the component
        const componentName = Object.keys(module).find(key =>
          typeof module[key as keyof typeof module] === 'function'
        );
        if (componentName) {
          return { default: module[componentName as keyof typeof module] as ComponentType<any> };
        }
        throw new Error('No valid component found in module');
      })
      .catch(error => {
        console.error('Failed to load calculator component:', error);
        // Return a fallback component
        return {
          default: () => (
            <div className="p-8 text-center">
              <h3 className="text-lg font-semibold text-red-600 mb-2">
                Failed to Load Calculator
              </h3>
              <p className="text-gray-600">
                Please refresh the page to try again.
              </p>
            </div>
          )
        };
      })
  );
};

// Pre-create all lazy components
const lazyCalculators: Record<string, ComponentType<any>> = {};
Object.entries(calculatorComponents).forEach(([key, importFn]) => {
  lazyCalculators[key] = createLazyCalculator(importFn);
});

interface DynamicCalculatorLoaderProps {
  calculatorId: string;
  [key: string]: any;
}

export function DynamicCalculatorLoader({ calculatorId, ...props }: DynamicCalculatorLoaderProps) {
  const LazyCalculator = lazyCalculators[calculatorId];

  if (!LazyCalculator) {
    return (
      <div className="p-8 text-center">
        <h3 className="text-lg font-semibold text-red-600 mb-2">
          Calculator Not Found
        </h3>
        <p className="text-gray-600">
          The calculator "{calculatorId}" could not be found.
        </p>
      </div>
    );
  }

  return (
    <Suspense fallback={<CalculatorSkeleton />}>
      <LazyCalculator {...props} />
    </Suspense>
  );
}

// Export for preloading specific calculators
export const preloadCalculator = (calculatorId: string) => {
  const importFn = calculatorComponents[calculatorId];
  if (importFn) {
    importFn().catch(error => {
      console.error(`Failed to preload calculator ${calculatorId}:`, error);
    });
  }
};

// Export list of available calculators
export const availableCalculators = Object.keys(calculatorComponents);

// Default export
export default DynamicCalculatorLoader;
