import { NextRequest, NextResponse } from 'next/server';
import { PDFDocument } from 'pdf-lib';
import <PERSON><PERSON><PERSON><PERSON> from 'jszip';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const splitType = formData.get('splitType') as string || 'pages';
    const pageRanges = formData.get('pageRanges') as string || '';
    const pagesPerFile = parseInt(formData.get('pagesPerFile') as string || '1');

    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }

    if (file.type !== 'application/pdf') {
      return NextResponse.json(
        { error: 'File must be a PDF document' },
        { status: 400 }
      );
    }

    // Convert file to buffer
    const arrayBuffer = await file.arrayBuffer();
    
    // Load the PDF document
    const pdfDoc = await PDFDocument.load(arrayBuffer);
    const totalPages = pdfDoc.getPageCount();

    if (totalPages === 0) {
      return NextResponse.json(
        { error: 'PDF document has no pages' },
        { status: 400 }
      );
    }

    const splitPdfs: { name: string; bytes: Uint8Array }[] = [];

    if (splitType === 'pages' && pageRanges) {
      // Split by specific page ranges (e.g., "1-3,5,7-9")
      const ranges = parsePageRanges(pageRanges, totalPages);
      
      for (let i = 0; i < ranges.length; i++) {
        const range = ranges[i];
        const newPdf = await PDFDocument.create();
        
        // Copy pages for this range
        const pagesToCopy = [];
        for (let pageNum = range.start; pageNum <= range.end; pageNum++) {
          pagesToCopy.push(pageNum - 1); // Convert to 0-based index
        }
        
        const copiedPages = await newPdf.copyPages(pdfDoc, pagesToCopy);
        copiedPages.forEach(page => newPdf.addPage(page));
        
        const pdfBytes = await newPdf.save();
        const fileName = `${file.name.replace('.pdf', '')}_pages_${range.start}-${range.end}.pdf`;
        
        splitPdfs.push({
          name: fileName,
          bytes: pdfBytes
        });
      }
    } else if (splitType === 'interval') {
      // Split by pages per file
      const validPagesPerFile = Math.max(1, Math.min(pagesPerFile, totalPages));
      
      for (let startPage = 0; startPage < totalPages; startPage += validPagesPerFile) {
        const endPage = Math.min(startPage + validPagesPerFile - 1, totalPages - 1);
        const newPdf = await PDFDocument.create();
        
        // Copy pages for this interval
        const pagesToCopy = [];
        for (let pageNum = startPage; pageNum <= endPage; pageNum++) {
          pagesToCopy.push(pageNum);
        }
        
        const copiedPages = await newPdf.copyPages(pdfDoc, pagesToCopy);
        copiedPages.forEach(page => newPdf.addPage(page));
        
        const pdfBytes = await newPdf.save();
        const fileName = `${file.name.replace('.pdf', '')}_part_${Math.floor(startPage / validPagesPerFile) + 1}.pdf`;
        
        splitPdfs.push({
          name: fileName,
          bytes: pdfBytes
        });
      }
    } else {
      // Split each page into separate files (default)
      for (let i = 0; i < totalPages; i++) {
        const newPdf = await PDFDocument.create();
        const [copiedPage] = await newPdf.copyPages(pdfDoc, [i]);
        newPdf.addPage(copiedPage);
        
        const pdfBytes = await newPdf.save();
        const fileName = `${file.name.replace('.pdf', '')}_page_${i + 1}.pdf`;
        
        splitPdfs.push({
          name: fileName,
          bytes: pdfBytes
        });
      }
    }

    if (splitPdfs.length === 0) {
      return NextResponse.json(
        { error: 'No pages to split' },
        { status: 400 }
      );
    }

    // Create ZIP file containing all split PDFs
    const zip = new JSZip();
    
    splitPdfs.forEach(pdf => {
      zip.file(pdf.name, pdf.bytes);
    });

    const zipBytes = await zip.generateAsync({ type: 'uint8array' });

    // Generate filename for ZIP
    const originalName = file.name.replace(/\.pdf$/i, '');
    const zipFilename = `${originalName}_split_${splitPdfs.length}_files.zip`;

    // Create response with ZIP
    const response = new NextResponse(zipBytes, {
      status: 200,
      headers: {
        'Content-Type': 'application/zip',
        'Content-Disposition': `attachment; filename="${zipFilename}"`,
        'X-Original-Pages': totalPages.toString(),
        'X-Split-Files': splitPdfs.length.toString(),
        'X-Split-Type': splitType,
      },
    });

    return response;

  } catch (error) {
    console.error('PDF split error:', error);
    
    if (error instanceof Error) {
      if (error.message.includes('Invalid PDF')) {
        return NextResponse.json(
          { error: 'Invalid PDF file. Please ensure the file is not corrupted.' },
          { status: 400 }
        );
      }
      if (error.message.includes('password')) {
        return NextResponse.json(
          { error: 'Password-protected PDF files are not supported.' },
          { status: 400 }
        );
      }
    }

    return NextResponse.json(
      { error: 'Failed to split PDF. Please ensure the file is a valid PDF document.' },
      { status: 500 }
    );
  }
}

// Helper function to parse page ranges like "1-3,5,7-9"
function parsePageRanges(rangeString: string, totalPages: number): { start: number; end: number }[] {
  const ranges: { start: number; end: number }[] = [];
  const parts = rangeString.split(',').map(s => s.trim());
  
  for (const part of parts) {
    if (part.includes('-')) {
      const [startStr, endStr] = part.split('-').map(s => s.trim());
      const start = Math.max(1, parseInt(startStr) || 1);
      const end = Math.min(totalPages, parseInt(endStr) || totalPages);
      
      if (start <= end) {
        ranges.push({ start, end });
      }
    } else {
      const pageNum = Math.max(1, Math.min(totalPages, parseInt(part) || 1));
      ranges.push({ start: pageNum, end: pageNum });
    }
  }
  
  return ranges;
}

export async function GET() {
  return NextResponse.json(
    { 
      message: 'PDF Split API',
      supportedInput: 'PDF',
      outputFormat: 'ZIP containing multiple PDF files',
      splitTypes: ['individual', 'interval', 'pages'],
      maxFileSize: '10MB',
      note: 'Splits PDF into multiple files based on specified criteria'
    },
    { status: 200 }
  );
}
